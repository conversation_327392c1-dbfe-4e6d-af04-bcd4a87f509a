import { TableCell, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import type { TableSkeletonProps } from "@/types/app/dashboard/admin/users/TableSkeletonProps"; // Import the interface

// TableSkeletonProps interface removed, now imported

// Make the skeleton slightly more dynamic based on props
export function TableSkeleton({ rows = 5, showOrgColumn = false }: TableSkeletonProps) { // Use imported interface


  return (
    <>
      {[...Array(rows)].map((_, i) => (
        <TableRow key={`skeleton-${i}`}>
          {/* User */}
          <TableCell className="py-4">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-md" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          </TableCell>
          {/* Role */}
          <TableCell>
            <Skeleton className="h-6 w-28 rounded" />
          </TableCell>
           {/* Organization (Conditional) */}
           {showOrgColumn && (
            <TableCell>
              <Skeleton className="h-4 w-24" />
            </TableCell>
           )}
          {/* Default */}
           <TableCell>
             <Skeleton className="h-4 w-10" />
           </TableCell>
          {/* Created */}
          <TableCell>
            <Skeleton className="h-4 w-20" />
          </TableCell>
          {/* Updated */}
          <TableCell>
            <Skeleton className="h-4 w-20" />
          </TableCell>
          {/* Status */}
          <TableCell className="text-center">
            <Skeleton className="h-6 w-[80px] rounded-md mx-auto" />
          </TableCell>
          {/* Actions */}
          <TableCell className="text-center">
            <Skeleton className="h-8 w-16 rounded-md mx-auto" />
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}