export async function getUserOrganizations(userId: string) {
  const { data: organizations, error } = await supabase
    .from('organizations')
    .select(`
      id,
      org_name,
      org_icon,
      organization_members!inner (
        is_default_org,
        org_member_role,
        org_member_is_active
      )
    `)
    .eq('organization_members.user_id', userId)
    .eq('organization_members.org_member_is_active', true)
    .order('organization_members.is_default_org', { ascending: false })

  if (error) throw error
  
  // Add this console.log to see raw data from database
  console.log('Raw DB response:', organizations)
  
  return organizations.map(org => ({
    id: org.id,
    name: org.org_name,
    role: org.organization_members[0].org_member_role === 1 ? 'superadmin' : 'member',
    org_member_role: org.organization_members[0].org_member_role,
    isActive: true,
    isDefault: org.organization_members[0].is_default_org,
    createdAt: org.created_at,
    updatedAt: org.updated_at,
    org_icon: org.org_icon
  }))
} 