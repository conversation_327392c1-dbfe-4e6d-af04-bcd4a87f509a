// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter } from '@/lib/eventBus'
import { clientLog } from '@/lib/eventBus/emitter'
import type { OrganizationMemberInsertedEvent, OrganizationMemberUpdatedEvent, AuthEvent } from '@/lib/eventTypes'
import { AUTH_CONTEXT_CHANGED } from '../constants'
import { useAuthContextStore } from '@/stores/useAuthContextStore'

// Using debounce instead of throttle to avoid dependency issues
// This more specific type ensures proper event handler compatibility
function debounce<EventType>(func: (event: EventType) => void, wait: number): (event: EventType) => void {
  let timeout: NodeJS.Timeout | null = null;
  return (event: EventType) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(event);
    }, wait);
  };
}

// Event deduplication cache
const recentEvents = new Map<string, number>();

// Cleanup old events periodically
if (typeof window !== 'undefined') {
  setInterval(() => {
    const now = Date.now();
    recentEvents.forEach((timestamp, key) => {
      // Remove events older than 1 second
      if (now - timestamp > 1000) {
        recentEvents.delete(key);
      }
    });
  }, 5000);
}

// Check if an event is a duplicate (same user, org, and values within a short time window)
function isDuplicateEvent(userId: string, orgId: string, data: Record<string, unknown>): boolean {
  const key = `${userId}:${orgId}:${JSON.stringify(data)}`;
  const now = Date.now();
  
  if (recentEvents.has(key)) {
    return true;
  }
  
  // Store this event as recently processed
  recentEvents.set(key, now);
  return false;
}

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organization_members:* events and emit member:status:changed events as appropriate.
 */
export function registerStatusInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    console.log('[StatusInterpreter] Already registered, skipping duplicate registration')
    return
  }
  
  isRegistered = true
  
  // Handle INSERT (new member added)
  emitter.on('db:organization_members:inserted', (event: OrganizationMemberInsertedEvent) => {
    const { orgId, userId, data, timestamp } = event
    const isActive = data.org_member_is_active
    if (!orgId || !userId || typeof isActive !== 'boolean') return

    // Only emit AUTH_CONTEXT_CHANGED for the current user
    const { userId: currentUserId } = useAuthContextStore.getState();
    if (userId !== currentUserId) {
      console.log('[StatusInterpreter] Status change is for different user, skipping AUTH_CONTEXT_CHANGED emission')
      return
    }

    // Check for duplicate events within a short time window
    if (isDuplicateEvent(userId, orgId, { isActive, eventType: 'INSERT' })) {
      console.log('[StatusInterpreter] Detected duplicate INSERT event, skipping')
      return
    }

    // Emit unified AUTH_CONTEXT_CHANGED event
    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'MEMBER_STATUS_CHANGE',
      data: {
        isActive
      },
      timestamp
    }
    clientLog(`[EventBus] Member status changed for user ${userId} in org ${orgId} to ${isActive ? 'active' : 'inactive'}`)
    console.log('[StatusInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  })

  // Handle UPDATE (status changed) with debouncing
  const handleStatusUpdated = debounce((event: OrganizationMemberUpdatedEvent) => {
    const { orgId, userId, data } = event
    const isActive = data.org_member_is_active

    // Early exit: Only process events for current organization context
    if (!data.is_current_context) {
      return; // Event is not for current organization context
    }

    // Early exit: Skip if status didn't change (most efficient check)
    if (isActive === data.old_org_member_is_active) {
      return; // No logging needed for unchanged status
    }

    // Early exit: Only emit AUTH_CONTEXT_CHANGED for the current user
    const { userId: currentUserId } = useAuthContextStore.getState();
    if (userId !== currentUserId) {
      console.log('[StatusInterpreter] Status change is for different user, skipping AUTH_CONTEXT_CHANGED emission')
      return
    }

    console.log('[StatusInterpreter] db:organization_members:updated handler fired:', { orgId, userId, isActive, data })

    // Determine old value safely, with a series of fallbacks
    let oldIsActive: boolean | undefined;
    
    // Try direct property from enhanced payload
    if (typeof data.old_org_member_is_active === 'boolean') {
      oldIsActive = data.old_org_member_is_active;
    } 
    // Try to extract from the old record if it's present
    else if (data.old) {
      const oldData = data.old as Record<string, unknown>;
      if (typeof oldData['org_member_is_active'] === 'boolean') {
        oldIsActive = oldData['org_member_is_active'] as boolean;
      }
    }
    
    // If we still don't have an old value, use current as fallback
    if (oldIsActive === undefined && typeof isActive === 'boolean') {
      oldIsActive = isActive;
    }
    
    console.log('[StatusInterpreter] Status values:', { isActive, oldIsActive });
    
    if (!orgId || !userId || typeof isActive !== 'boolean' || typeof oldIsActive !== 'boolean') {
      console.warn('[StatusInterpreter] Missing required data for status change event:', { 
        hasOrgId: !!orgId, 
        hasUserId: !!userId, 
        isActiveType: typeof isActive, 
        oldIsActiveType: typeof oldIsActive 
      })
      return
    }
    
    if (isActive === oldIsActive) {
      console.log('[StatusInterpreter] Status did not change, skipping event emission')
      return
    }
    
    // Check for duplicate events within a short time window
    if (isDuplicateEvent(userId, orgId, { isActive, oldIsActive })) {
      console.log('[StatusInterpreter] Detected duplicate event, skipping to prevent event storms')
      return
    }
    
    console.log(`[StatusInterpreter] Emitting AUTH_CONTEXT_CHANGED event: User ${userId} in org ${orgId} is now ${isActive ? 'active' : 'inactive'}`)
    clientLog(`[EventBus] Member status changed for user ${userId} in org ${orgId} to ${isActive ? 'active' : 'inactive'}`)
    
    // Emit only the unified AUTH_CONTEXT_CHANGED event
    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'MEMBER_STATUS_CHANGE',
      data: {
        isActive,
        oldIsActive
      }
    }
    console.log('[StatusInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  }, 250); // Debounce to wait 250ms before processing
  
  emitter.on('db:organization_members:updated', handleStatusUpdated)

  // Handle DELETE (member removed)
  emitter.on('db:organization_members:deleted', () => {
    // Optionally emit a status change event for cleanup if needed
  })
}
