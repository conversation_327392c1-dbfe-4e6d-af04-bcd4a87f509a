/**
 * Event types for the organization context and role changes
 */

/**
 * Emitted when a user's default organization context changes.
 */
export interface OrganizationContextEvent {
  userId: string;
  orgId: string;
  timestamp: number;
}

/**
 * Emitted when a user's role in an organization changes.
 */
export interface UserRoleChangedEvent {
  userId: string;
  orgId: string;
  newRoleId: number;
  oldRoleId?: number; // Optional, only available for updates
  timestamp: number;
  eventType?: 'INSERT' | 'UPDATE' | 'DELETE'; // Optional for context
}

/**
 * Emitted when a member's active status in an organization changes.
 */
export interface MemberStatusChangedEvent {
  userId: string;
  orgId: string;
  isActive: boolean;
  timestamp: number;
  eventType?: 'INSERT' | 'UPDATE' | 'DELETE'; // Optional for context
  _contextRefreshed?: boolean; // Hint that this event follows a global context refresh
}

/**
 * Emitted when an organization's name changes.
 */
export interface OrgNameChangedEvent {
  orgId: string;
  newName: string;
  timestamp: number;
}

/**
 * Emitted for any change (insert, update, delete) to an organization member.
 */
export interface AnyMemberChangeEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  orgId: string;
  userId: string;
  data: Record<string, unknown>;
  timestamp: number;
}

/**
 * Emitted when a member is inserted into organization_members.
 */
export interface OrganizationMemberInsertedEvent {
  orgId: string;
  userId: string;
  data: Record<string, unknown>;
  timestamp: number;
}

/**
 * Emitted when a member is updated in organization_members.
 */
export interface OrganizationMemberUpdatedEvent {
  orgId: string;
  userId: string;
  data: Record<string, unknown>;
  timestamp: number;
}

/**
 * Emitted when a member is deleted from organization_members.
 */
export interface OrganizationMemberDeletedEvent {
  orgId: string;
  userId: string;
  data: Record<string, unknown>;
  timestamp: number;
}

/**
 * Emitted when an organization record is updated (e.g., name, status).
 */
export interface OrganizationDataChangedEvent {
  orgId: string;
  changes: Record<string, unknown>;
  oldRecord?: Record<string, unknown>;
  timestamp: number;
}

/**
 * Emitted when an organization's active status (is_active) changes.
 */
export interface OrganizationActiveStatusChangedEvent {
  orgId: string;
  isActive: boolean;
  oldIsActive: boolean;
  timestamp: number;
}

/**
 * Unified auth context change event - emitted for any change that affects
 * user authentication or authorization context.
 */
export interface AuthEvent {
  userId: string;
  orgId?: string;
  reason: string; // Reason for the auth context change
  data?: Record<string, unknown>; // Additional context information
  timestamp?: number; // Optional timestamp, will be auto-added if not provided
} 