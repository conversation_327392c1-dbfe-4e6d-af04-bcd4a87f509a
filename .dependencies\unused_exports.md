# ❓ Unused Exports

- `next.config.ts` (Line 23) → ⚠️
  default: nextConfig

- `src/app/api/organizations/authorized/route.ts` (Line 9) → ⚠️
  PaginatedOrganizationsResponse

- `src/app/api/organizations/create/route.ts` (Line 7) → ⚠️
  Organization
  CreateOrganizationRequest
  CreateOrganizationResponse

- `src/app/dashboard/admin/flows/page.tsx` (Line 22) → ⚠️
  Step
  Flow

- `src/app/dashboard/developer/create-organization/actions.ts` (Line 7) → ⚠️
  CreateOrganizationData
  ActionState
  createOrganization

- `src/app/dashboard/handbooks/handbook-search.tsx` (Line 23) → ⚠️
  HandbookSearch

- `src/components/Charts.tsx` (Line 37) → ✅
  description

- `src/components/auth/auth-card.tsx` (Line 15) → ⚠️
  AuthCard

- `src/components/auth/google-auth-button.tsx` (Line 8) → ⚠️
  GoogleAuthButton

- `src/components/auth/with-role-check.tsx` (Line 8) → ⚠️
  withRoleCheck

- `src/components/dashboard/dashboard-header.tsx` (Line 8) → ⚠️
  DashboardHeader

- `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 22) → ✅
  FlowStepEditDialog

- `src/components/labels/LabelEditDialog.tsx` (Line 10) → ✅
  LabelEditDialog

- `src/components/login-form.tsx` (Line 6) → ✅
  LoginForm

- `src/components/no-access.tsx` (Line 7) → ✅
  NoAccess

- `src/components/organization/organization-list.tsx` (Line 20) → ⚠️
  OrganizationList

- `src/components/organization/organization-nav.tsx` (Line 6) → ⚠️
  OrganizationNav

- `src/components/shared/Tooltip.tsx` (Line 5) → ✅
  Tooltip

- `src/components/shared/member-table.tsx` (Line 26) → ⚠️
  MemberTablePermissionModel
  MemberTablePagePermissions

- `src/components/theme-provider.tsx` (Line 6) → ⚠️
  ThemeProvider

- `src/components/ui/alert-dialog.tsx` (Line 129) → ✅
  AlertDialogPortal
  AlertDialogOverlay
  AlertDialogTrigger

- `src/components/ui/breadcrumb.tsx` (Line 107) → ✅
  BreadcrumbEllipsis

- `src/components/ui/chart.tsx` (Line 11) → ✅
  ChartConfig
  ChartLegend
  ChartLegendContent
  ChartStyle

- `src/components/ui/checkbox.tsx` (Line 30) → ✅
  Checkbox

- `src/components/ui/command.tsx` (Line 143) → ✅
  CommandDialog
  CommandShortcut
  CommandSeparator

- `src/components/ui/context-menu.tsx` (Line 184) → ✅
  ContextMenu
  ContextMenuTrigger
  ContextMenuContent
  ContextMenuItem
  ContextMenuCheckboxItem
  ContextMenuRadioItem
  ContextMenuLabel
  ContextMenuSeparator
  ContextMenuShortcut
  ContextMenuGroup
  ContextMenuPortal
  ContextMenuSub
  ContextMenuSubContent
  ContextMenuSubTrigger
  ContextMenuRadioGroup

- `src/components/ui/dialog.tsx` (Line 111) → ✅
  DialogPortal
  DialogOverlay
  DialogClose

- `src/components/ui/drawer.tsx` (Line 107) → ✅
  Drawer
  DrawerPortal
  DrawerOverlay
  DrawerTrigger
  DrawerClose
  DrawerContent
  DrawerHeader
  DrawerFooter
  DrawerTitle
  DrawerDescription

- `src/components/ui/dropdown-menu.tsx` (Line 185) → ✅
  DropdownMenuCheckboxItem
  DropdownMenuShortcut
  DropdownMenuGroup
  DropdownMenuPortal
  DropdownMenuSub
  DropdownMenuSubContent
  DropdownMenuSubTrigger

- `src/components/ui/form.tsx` (Line 169) → ✅
  useFormField

- `src/components/ui/navigation-menu.tsx` (Line 118) → ✅
  navigationMenuTriggerStyle
  NavigationMenu
  NavigationMenuList
  NavigationMenuItem
  NavigationMenuContent
  NavigationMenuTrigger
  NavigationMenuLink
  NavigationMenuIndicator
  NavigationMenuViewport

- `src/components/ui/pagination.tsx` (Line 110) → ✅
  Pagination
  PaginationContent
  PaginationLink
  PaginationItem
  PaginationPrevious
  PaginationNext
  PaginationEllipsis

- `src/components/ui/popover.tsx` (Line 33) → ✅
  PopoverAnchor

- `src/components/ui/scroll-area.tsx` (Line 48) → ✅
  ScrollArea
  ScrollBar

- `src/components/ui/select.tsx` (Line 148) → ✅
  SelectGroup
  SelectLabel
  SelectSeparator
  SelectScrollUpButton
  SelectScrollDownButton

- `src/components/ui/sheet.tsx` (Line 128) → ✅
  SheetPortal
  SheetOverlay
  SheetTrigger
  SheetClose
  SheetFooter
  SheetDescription

- `src/components/ui/table.tsx` (Line 111) → ✅
  TableFooter
  TableCaption

- `src/components/ui/tabs.tsx` (Line 55) → ✅
  Tabs
  TabsList
  TabsTrigger
  TabsContent

- `src/components/ui/toast.tsx` (Line 119) → ✅
  ToastProvider
  ToastViewport
  Toast
  ToastTitle
  ToastDescription
  ToastClose
  ToastAction

- `src/components/ui/toggle.tsx` (Line 45) → ✅
  Toggle
  toggleVariants

- `src/hooks/use-auth-context-events.ts` (Line 9) → ⚠️
  refreshAuthContextThrottled
  throttle

- `src/hooks/use-auth.ts` (Line 7) → ✅
  useAuth

- `src/hooks/use-organization-context.ts` (Line 36) → ✅
  useOrganizationContext

- `src/hooks/use-organizations-list.ts` (Line 5) → ⚠️
  PaginatedOrganizationsResponse

- `src/lib/auth-context.ts` (Line 12) → ⚠️
  AuthContext
  getAuthContextWithBackgroundRefresh

- `src/lib/auth-utils.ts` (Line 3) → ⚠️
  UserRole
  getUserRole
  getUserRoleServer

- `src/lib/eventBus/emitter.ts` (Line 88) → ✅
  isServer
  CONNECTION_HEALTH_CHECK_INTERVAL
  CONNECTION_TIMEOUT

- `src/lib/eventBus/hooks/useAllMemberChanges.ts` (Line 9) → ⚠️
  useAllMemberChanges

- `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 11) → ⚠️
  useOrganizationEvents

- `src/lib/eventBus/index.ts` (Line 6) → ⚠️
  OrganizationContextEvent
  UserRoleChangedEvent
  MemberStatusChangedEvent
  OrgNameChangedEvent
  AnyMemberChangeEvent
  OrganizationEventBus
  useAllMemberChanges
  useOrganizationEvents

- `src/lib/get-user-active-organization.ts` (Line 30) → ✅
  getUserActiveOrganization
  getOrganizationByContext

- `src/lib/index.ts` (Line 2) → ⚠️
  emitter
  Events
  useOrganizationContextEvents
  useUserRoleEvents
  useMemberStatusEvents
  useOrgNameEvents
  useAllMemberChanges
  organizationEventBus
  OrganizationContextEvent
  UserRoleChangedEvent
  MemberStatusChangedEvent
  OrgNameChangedEvent
  AnyMemberChangeEvent

- `src/lib/organization-utils-server.ts` (Line 164) → ✅
  getUserOrganizationById
  getUserDefaultOrganization

- `src/lib/permissions-service-client.ts` (Line 47) → ⚠️
  invalidatePermissionCache
  checkPermissionClient
  getUserPermissionsClient
  clientPermissionFunctions

- `src/lib/rbac/index.ts` (Line 13) → ⚠️
  Restricted

- `src/lib/rbac/middleware.ts` (Line 13) → ✅
  createPermissionMiddleware

- `src/lib/rbac/rbac-utils.ts` (Line 8) → ⚠️
  UserRole
  roleIdToKey
  checkMinRole
  checkRoles
  parseCrudFromKey
  mapCrudToPermissions
  evaluateRbacForCrud

- `src/lib/rbac/role-utils.ts` (Line 169) → ⚠️
  roleGuards

- `src/lib/rbac/roles.ts` (Line 22) → ⚠️
  roleSchema
  Role
  isAtLeastRole

- `src/lib/rbac/server-action-multi.ts` (Line 11) → ✅
  withPermissions

- `src/lib/rbac/server-action-org.ts` (Line 11) → ✅
  withOrgAdmin

- `src/lib/toast-messages.ts` (Line 6) → ⚠️
  ToastOptions

- `src/middleware.ts` (Line 77) → ⚠️
  middleware
  config

- `src/tools/mitt-debugger.ts` (Line 22) → ✅
  debugEmitterListeners

- `src/types/app/dashboard/admin/flowsteps/FlowStepData.ts` (Line 26) → ⚠️
  FlowStep

- `src/types/app/dashboard/admin/invites/Profile.ts` (Line 1) → ✅
  Profile

- `src/types/app/dashboard/admin/invites/Role.ts` (Line 1) → ✅
  Role

- `src/types/app/dashboard/admin/organization/FormData.ts` (Line 1) → ✅
  FormData

- `src/types/app/dashboard/admin/users/OrganizationMemberResponse.ts` (Line 3) → ✅
  OrganizationMemberResponse

- `src/types/components/UserProfileDialogProps.ts` (Line 4) → ✅
  UserProfileDialogProps

- `src/types/components/UserTableProps.ts` (Line 4) → ✅
  UserTableProps

- `src/types/components/providers/DashboardContextType.ts` (Line 4) → ✅
  DashboardContextType

- `src/types/components/ui/BadgeProps.ts` (Line 5) → ✅
  BadgeProps

- `src/types/components/ui/ButtonProps.ts` (Line 5) → ⚠️
  ButtonProps

- `src/types/handbook.ts` (Line 11) → ✅
  CriteriaItems

- `src/types/lib/auth-context.ts` (Line 4) → ⚠️
  AuthContext

- `src/types/lib/rbac/index.ts` (Line 16) → ⚠️
  UserRole
  ROLE_KEY_TO_ID
  ROLE_ID_TO_KEY
  CrudKey
  RolePermission
  RoleRecord
  PermissionMiddlewareConfig

- `src/types/organization.ts` (Line 15) → ⚠️
  OrganizationMember

- `src/types/organization/index.ts` (Line 9) → ⚠️
  OrganizationWithRole
  OrganizationMemberWithOrg
  OrganizationMemberFull
  Organization
  OrganizationMember
  OrganizationMemberBasic
  OrganizationMembershipResponse

- `src/types/user/index.ts` (Line 8) → ✅
  UserProfileWithPersonalInfo

- `tailwind.config.ts` (Line 5) → ⚠️
  default

*Note: This list includes exports not found in static imports within the project. This includes:*

1. *Some exports that the extension doesn't recognize as being used by the framework. The extension attempts to identify:*
   - *Next.js page components (default exports from `page.tsx` files)*
   - *Next.js route handlers (HTTP methods in `route.ts` files)*
   - *Next.js layout components (default exports from `layout.tsx` files)*
   - *Server actions (exports from `actions/` directory or marked with 'use server')*
   - *Special Next.js exports like `generateMetadata`, `getStaticProps`, etc.*
2. *Exports used through dynamic imports or runtime resolution*
3. *Exports only used in external projects*
4. *Genuinely unused exports that could be removed*

*If you believe an export is incorrectly listed here, it may be using a pattern not yet recognized by the extension.*

---

## Analysis Summary

### Legend:
- ✅ **Safe to remove** - These exports are truly unused and can be safely deleted
- ⚠️ **Not safe to remove** - These exports are actually in use (false positives from the tool)

### Key Findings:

**Framework Required Exports (⚠️):**
- `next.config.ts`, `tailwind.config.ts`, `middleware.ts` - Required by Next.js/Tailwind frameworks
- Most API route types and server actions are actually being used

**Actually Unused Exports (✅):**
- `description` from `src/components/Charts.tsx` - Just a string constant, not used anywhere
- Many UI component sub-exports (like `AlertDialogPortal`, `CommandDialog`, etc.) - These are Shadcn UI components that export many variants but only some are used
- Several type definitions that appear to be unused
- Some utility functions and constants

**False Positives (⚠️):**
- Most hooks, RBAC utilities, and core types are actually being used
- Event bus related exports are part of the active event system
- Organization and auth context types are heavily used throughout the app

### Recommendation:
Focus on removing the ✅ marked items first, as these are confirmed to be safe to remove. The ⚠️ items should be left alone as they are either framework-required or actually in use despite not being detected by the static analysis tool.
