import { createBrowserClient } from '@supabase/ssr'

export const createClient = () => {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      // Configure client options with proper auth settings
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
      },
      // Configure global options that apply to all modules
      global: {
        // Use the fetch API provided by the browser
        fetch: (...args) => fetch(...args),
        // Additional headers if needed
        headers: {
          'x-application-name': 'visa-automation-app',
        },
      },
      // Configure realtime with correct configuration structure
      realtime: {
        params: {
          eventsPerSecond: 10,
          // Add reconnection parameters
          autoRefreshToken: true,
          persistSession: true,
          timeout: 60000 // 60 seconds to match the current timeout
        }
      }
    }
  )
}