import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { evaluateRbac } from '@/lib/rbac/rbac-utils'

// Define the OrganizationResponse type inline
interface OrganizationResponse {
  org_id: string;
  org_member_role: number;
  org_member_is_active: boolean;
  organizations: {
    id: string;
    org_name: string;
    org_icon: string | null;
    is_active: boolean;
  };
}

export async function POST(request: Request) {
  try {
    const { organizationId } = await request.json()
    const supabase = await createClient()

    // Verify the user has access to this organization
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        org_member_role,
        org_member_is_active,
        organizations!inner (
          id,
          is_active,
          org_name,
          org_icon
        )
      `)
      .eq('user_id', user.id)
      .eq('org_id', organizationId)
      .eq('org_member_is_active', true)
      .eq('organizations.is_active', true)
      .single()

    if (membershipError || !membership) {
      console.error('Error verifying organization access:', membershipError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const typedMembership = membership as unknown as OrganizationResponse

    // Use the DB function to set the context
    const { error: contextError } = await supabase.rpc('set_user_organization_context', {
      p_user_id: user.id,
      p_org_id: organizationId
    })

    if (contextError) {
      console.error('Error setting organization context:', contextError)
      return NextResponse.json({ 
        error: 'Failed to set organization context',
        message: contextError.message 
      }, { status: 500 })
    }

    console.log(`[API Org Switch] User ${user.id} switched to organization: ${organizationId}`);

    // Create response with organization data
    return NextResponse.json({ 
      success: true, 
      message: 'Organization switched successfully',
      organization: {
        id: typedMembership.organizations.id,
        name: typedMembership.organizations.org_name,
        org_icon: typedMembership.organizations.org_icon || '',
        role: evaluateRbac(typedMembership.org_member_role, { rRoles: ["superAdmin"] }) ? 'superadmin' : 'member',
        org_member_role: typedMembership.org_member_role,
        isActive: true
      }
    })
  } catch (error) {
    console.error('Error switching organization:', error)
    return NextResponse.json({ 
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 