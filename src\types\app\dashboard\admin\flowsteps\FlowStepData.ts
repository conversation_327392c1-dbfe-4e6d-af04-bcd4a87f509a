export const FLOW_STEP_TYPES = [
  "text",
  "number",
  "date",
  "email",
  "phone",
  "select",
  "checkbox",
  "radio",
] as const

export type FlowStepType = (typeof FLOW_STEP_TYPES)[number]

export interface FlowStepData {
  id: number
  uid: string
  name: string
  description: string
  type: FlowStepType
  category_id: number
  position: number
  created_at?: string
  updated_at?: string
}

export type FlowStep = FlowStepData 