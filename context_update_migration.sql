-- Migration: Replace cookie-based organization context with DB-driven approach
-- This migration adds database functions for managing organization context

-- Set up user organization context function
CREATE OR REPLACE FUNCTION public.set_user_organization_context(
  p_user_id UUID,
  p_org_id UUID
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_current_context_org_id UUID;
BEGIN
  -- Check if the requested org is already the current context
  SELECT org_id INTO v_current_context_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  LIMIT 1;
  
  -- If the requested org is already the current context, do nothing
  IF v_current_context_org_id = p_org_id THEN
    RETURN;
  END IF;
  
  -- First, set the current context to false (only if there is one)
  IF v_current_context_org_id IS NOT NULL THEN
    UPDATE public.organization_members
    SET is_current_context = false
    WHERE user_id = p_user_id
    AND org_id = v_current_context_org_id;
  END IF;
  
  -- Then set the selected org to true
  UPDATE public.organization_members
  SET is_current_context = true
  WHERE user_id = p_user_id
  AND org_id = p_org_id;
END;
$$;

-- Grant access to authenticated users
GRANT EXECUTE ON FUNCTION public.set_user_organization_context(UUID, UUID) TO authenticated;

-- Create function to initialize user organization context
CREATE OR REPLACE FUNCTION public.initialize_user_organization_context(
  p_user_id UUID
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_org_id UUID;
  v_default_org_id UUID;
  v_any_org_id UUID;
BEGIN
  -- Check if user already has an active context
  SELECT org_id INTO v_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  AND org_member_is_active = true
  LIMIT 1;
  
  -- Return existing context if found
  IF v_org_id IS NOT NULL THEN
    RETURN v_org_id;
  END IF;
  
  -- Try to find default organization
  SELECT org_id INTO v_default_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_default_org = true
  AND org_member_is_active = true
  LIMIT 1;
  
  -- If default org found, set it as context
  IF v_default_org_id IS NOT NULL THEN
    PERFORM public.set_user_organization_context(p_user_id, v_default_org_id);
    RETURN v_default_org_id;
  END IF;
  
  -- No default org, try any active org
  SELECT org_id INTO v_any_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND org_member_is_active = true
  LIMIT 1;
  
  -- If any org found, set it as context
  IF v_any_org_id IS NOT NULL THEN
    PERFORM public.set_user_organization_context(p_user_id, v_any_org_id);
    RETURN v_any_org_id;
  END IF;
  
  -- No organizations found
  RETURN NULL;
END;
$$;

-- Grant access to authenticated users
GRANT EXECUTE ON FUNCTION public.initialize_user_organization_context(UUID) TO authenticated;

-- Replace current_user_has_organizations function
CREATE OR REPLACE FUNCTION public.current_user_has_organizations()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_user_id UUID;
  v_has_orgs BOOLEAN;
  v_org_id UUID;
BEGIN
  -- Get current user
  v_user_id := auth.uid();
  
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;
  
  -- Check if user has any organizations
  SELECT EXISTS (
    SELECT 1
    FROM public.organization_members
    WHERE user_id = v_user_id
    AND org_member_is_active = true
  ) INTO v_has_orgs;
  
  -- If user has organizations, ensure one is set as current context
  IF v_has_orgs THEN
    v_org_id := public.initialize_user_organization_context(v_user_id);
  END IF;
  
  RETURN v_has_orgs;
END;
$$;

-- Grant access to authenticated users
GRANT EXECUTE ON FUNCTION public.current_user_has_organizations() TO authenticated;

-- Initialize context for all users who don't have a context set
DO $$
DECLARE
  v_user record;
BEGIN
  FOR v_user IN
    SELECT DISTINCT user_id 
    FROM public.organization_members 
    WHERE user_id NOT IN (
      SELECT user_id 
      FROM public.organization_members 
      WHERE is_current_context = true
    )
    AND org_member_is_active = true
  LOOP
    PERFORM public.initialize_user_organization_context(v_user.user_id);
  END LOOP;
END $$; 