'use server'

import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Name validation schema matching existing patterns
const nameSchema = z.string()
  .min(1, 'Name is required')
  .max(25, 'Name must be 25 characters or less')
  .regex(/^[\p{L}\-'\s]+$/u, 'Name contains invalid characters')

export interface AcceptInvitationResult {
  success: boolean
  error?: string
  result?: any
}

export interface DeclineInvitationResult {
  success: boolean
  error?: string
}

/**
 * Accept an email invitation with user's name
 */
export async function acceptEmailInvitation(
  token: string,
  firstName: string,
  lastName: string
): Promise<AcceptInvitationResult> {
  try {
    // 1. Validate inputs
    const validatedFirstName = nameSchema.parse(firstName.trim())
    const validatedLastName = nameSchema.parse(lastName.trim())
    const fullName = `${validatedFirstName} ${validatedLastName}`.trim()

    // 2. Validate token format
    if (!token || token.length < 20) {
      return { success: false, error: 'Invalid invitation token' }
    }

    // 3. Get current user
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return { success: false, error: 'Authentication required. Please sign in first.' }
    }

    // 4. Use database function for atomic acceptance
    const { data: result, error } = await supabase.rpc('accept_invitation', {
      p_token: token,
      p_user_id: user.id,
      p_full_name: fullName
    })

    if (error) {
      console.error('Accept invitation error:', error)

      // Provide user-friendly error messages
      if (error.message.includes('not found') || error.message.includes('expired')) {
        return {
          success: false,
          error: 'This invitation link is invalid or has expired. Please request a new invitation.'
        }
      }

      return { success: false, error: 'Failed to accept invitation. Please try again.' }
    }

    return { success: true, result }

  } catch (error) {
    console.error('Accept invitation error:', error)

    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      return { success: false, error: firstError?.message || 'Invalid name format' }
    }

    return { success: false, error: 'An unexpected error occurred. Please try again.' }
  }
}

/**
 * Decline an email invitation
 */
export async function declineEmailInvitation(token: string): Promise<DeclineInvitationResult> {
  try {
    // 1. Validate token format
    if (!token || token.length < 20) {
      return { success: false, error: 'Invalid invitation token' }
    }

    const supabase = await createClient()

    // 2. Update invitation status to declined
    const { error } = await supabase
      .from('email_invitations')
      .update({ status: 'declined' })
      .eq('invitation_token', token)
      .eq('status', 'delivered') // Only allow declining delivered invitations

    if (error) {
      console.error('Decline invitation error:', error)
      return { success: false, error: 'Failed to decline invitation. The invitation may have already been processed.' }
    }

    return { success: true }

  } catch (error) {
    console.error('Decline invitation error:', error)
    return { success: false, error: 'An unexpected error occurred. Please try again.' }
  }
}

/**
 * Get invitation details for display (without accepting)
 */
export async function getInvitationDetails(token: string) {
  try {
    if (!token || token.length < 20) {
      return { success: false, error: 'Invalid invitation token' }
    }

    const supabase = await createClient()

    const { data: invite, error } = await supabase
      .from('email_invitations')
      .select(`
        id,
        org_id,
        role_id,
        email,
        status,
        expires_at,
        personal_message,
        organizations (org_name)
      `)
      .eq('invitation_token', token)
      .eq('status', 'delivered')
      .gte('expires_at', new Date().toISOString())
      .maybeSingle()

    if (error) {
      console.error('Get invitation details error:', error)
      return { success: false, error: 'Failed to load invitation details' }
    }

    if (!invite) {
      return { success: false, error: 'Invitation not found or has expired' }
    }

    return { success: true, invite }

  } catch (error) {
    console.error('Get invitation details error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}
