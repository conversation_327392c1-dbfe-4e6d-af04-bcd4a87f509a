import { withRbacPermission } from "@/lib/rbac/permissions-server";
import { MemberTable, MemberTableBusinessRules } from "@/components/shared/member-table";
import { <PERSON><PERSON><PERSON> } from "@/lib/rbac/rbac-utils";
import { PageProps } from "@/types/app/PageProps";

const OrgAdminMembersPageInternal = async (_props: PageProps) => {
  // Define business rules for OrgAdmins managing their members
  // OrgAdmins have full permissions for all actions within their organization
  // SECURITY: Additional safeguard - restrict role assignments to orgAdmin level and below
  const orgAdminBusinessRules: MemberTableBusinessRules = {
    profileEditMinRole: "orgAdmin", // OrgAdmin+ can view/edit profiles
    roleEditMinRole: "orgAdmin", // OrgAdmin+ can change roles
    maxAssignableRole: "orgAdmin", // SAFEGUARD: Cannot assign roles above orgAdmin (prevents superAdmin/supportAdmin assignment)
    statusChangeMinRole: "orgAdmin", // OrgAdmin+ can change status
    deleteMinRole: "orgAdmin", // OrgAdmin+ can delete members
    inviteMinRole: "orgAdmin", // OrgAdmin+ can invite new members
  };

  const orgAdminRoleFilters: RoleKey[] = ['orgAdmin', 'orgMember', 'orgAccounting', 'orgClient'];

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Organization Members</h1>
      <MemberTable
        organizationIdScope="CURRENT_CONTEXT"
        roleFilters={orgAdminRoleFilters}
        permissionModel="role-based"
        businessRules={orgAdminBusinessRules}
        showOrganizationColumn={false}
        tableTitle="Current Organization Members"
        // onInviteUser prop omitted for now.
        // If MemberTable shows an invite button based on businessRules.inviteMinRole,
        // it would need an actual handler (client-side navigation or dialog management).
      />
    </div>
  );
};

const OrgAdminMembersPage = withRbacPermission(
  { rMinRole: "orgAdmin" },
  { redirectTo: "/dashboard" }
)(OrgAdminMembersPageInternal);

export default OrgAdminMembersPage;