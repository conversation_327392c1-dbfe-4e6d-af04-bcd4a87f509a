// Extracted from src/components/user-profile-dialog.tsx
import type { OrganizationMemberFull } from "@/types/organization/"; // Correct import path

export interface UserProfileDialogProps {
  user: OrganizationMemberFull;
  onClose: () => void;
  onUpdateRole: (userId: string, newRole: string) => Promise<void>;
  onUpdateStatus: (userId: string, isActive: boolean) => Promise<void>;
  onRemove: (userId: string) => Promise<void>;
}