/**
 * Type-safe role utilities for RBAC system
 * Provides enhanced type safety and validation for role operations
 */

import type { <PERSON><PERSON><PERSON>, RoleId, RoleHelpers, PermissionResult } from '@/types/lib/rbac';

// Import the mappings from the types file
const ROLE_KEY_TO_ID_MAP: Record<RoleKey, RoleId> = {
  superAdmin: 1,
  supportAdmin: 2,
  orgAdmin: 3,
  orgMember: 4,
  orgAccounting: 5,
  orgClient: 6,
} as const;

const ROLE_ID_TO_KEY_MAP: Record<RoleId, RoleKey> = {
  1: 'superAdmin',
  2: 'supportAdmin',
  3: 'orgAdmin',
  4: 'orgMember',
  5: 'orgAccounting',
  6: 'orgClient',
} as const;

/**
 * Type-safe role helper functions implementation
 */
export const roleHelpers: RoleHelpers = {
  keyToId: (key: RoleKey): RoleId => {
    return ROLE_KEY_TO_ID_MAP[key];
  },

  idToKey: (id: RoleId): RoleKey => {
    return ROLE_ID_TO_KEY_MAP[id];
  },

  isValidRoleId: (id: number): id is RoleId => {
    return id >= 1 && id <= 6 && Number.isInteger(id);
  },

  isValidRoleKey: (key: string): key is RoleKey => {
    return key in ROLE_KEY_TO_ID_MAP;
  },

  getAllRoleKeys: (): readonly RoleKey[] => {
    return Object.keys(ROLE_KEY_TO_ID_MAP) as RoleKey[];
  },

  getAllRoleIds: (): readonly RoleId[] => {
    return Object.values(ROLE_KEY_TO_ID_MAP);
  },
};

/**
 * Enhanced permission result factory functions
 */
export const createPermissionResult = {
  /**
   * Create a successful permission result
   */
  allowed: (reason: string, context?: PermissionResult['context']): PermissionResult => ({
    allowed: true,
    reason,
    ...(context && { context }),
  }),

  /**
   * Create a denied permission result
   */
  denied: (
    reason: string,
    userRoleId?: RoleId,
    requiredRole?: RoleKey,
    context?: PermissionResult['context']
  ): PermissionResult => ({
    allowed: false,
    reason,
    ...(userRoleId !== undefined && { userRoleId }),
    ...(requiredRole && { requiredRole }),
    ...(context && { context }),
  }),

  /**
   * Create a result for insufficient role
   */
  insufficientRole: (
    userRoleId: RoleId,
    requiredRole: RoleKey,
    context?: PermissionResult['context']
  ): PermissionResult => ({
    allowed: false,
    reason: `Insufficient role: user has ${roleHelpers.idToKey(userRoleId)}, but ${requiredRole} or higher is required`,
    userRoleId,
    requiredRole,
    ...(context && { context }),
  }),

  /**
   * Create a result for missing organization context
   */
  missingOrgContext: (context?: PermissionResult['context']): PermissionResult => ({
    allowed: false,
    reason: 'Missing organization context',
    ...(context && { context }),
  }),

  /**
   * Create a result for invalid role
   */
  invalidRole: (roleId: number, context?: PermissionResult['context']): PermissionResult => ({
    allowed: false,
    reason: `Invalid role ID: ${roleId}`,
    ...(context && { context }),
  }),
};

/**
 * Type-safe role comparison functions
 */
export const roleComparison = {
  /**
   * Check if a role is at least as powerful as another role (lower ID = more powerful)
   */
  isAtLeast: (userRoleId: RoleId, requiredRoleId: RoleId): boolean => {
    return userRoleId <= requiredRoleId;
  },

  /**
   * Check if a role is more powerful than another role
   */
  isHigherThan: (userRoleId: RoleId, compareRoleId: RoleId): boolean => {
    return userRoleId < compareRoleId;
  },

  /**
   * Check if a role is exactly equal to another role
   */
  isExactly: (userRoleId: RoleId, compareRoleId: RoleId): boolean => {
    return userRoleId === compareRoleId;
  },

  /**
   * Get the hierarchy level of a role (1 = highest, 6 = lowest)
   */
  getHierarchyLevel: (roleId: RoleId): number => {
    return roleId;
  },

  /**
   * Get all roles that are at least as powerful as the given role
   */
  getRolesAtLeast: (minRoleId: RoleId): RoleId[] => {
    return roleHelpers.getAllRoleIds().filter(id => id <= minRoleId);
  },

  /**
   * Get all roles that are less powerful than the given role
   */
  getRolesBelow: (roleId: RoleId): RoleId[] => {
    return roleHelpers.getAllRoleIds().filter(id => id > roleId);
  },
};

/**
 * Type guards for role validation
 */
export const roleGuards = {
  /**
   * Assert that a number is a valid role ID
   */
  assertValidRoleId: (id: number): asserts id is RoleId => {
    if (!roleHelpers.isValidRoleId(id)) {
      throw new Error(`Invalid role ID: ${id}. Must be between 1 and 6.`);
    }
  },

  /**
   * Assert that a string is a valid role key
   */
  assertValidRoleKey: (key: string): asserts key is RoleKey => {
    if (!roleHelpers.isValidRoleKey(key)) {
      throw new Error(`Invalid role key: ${key}. Must be one of: ${roleHelpers.getAllRoleKeys().join(', ')}`);
    }
  },
};

/**
 * Utility functions for role operations
 */
export const roleUtils = {
  /**
   * Safely convert a number to a role ID with validation
   */
  safeRoleId: (id: number): RoleId | null => {
    return roleHelpers.isValidRoleId(id) ? id : null;
  },

  /**
   * Safely convert a string to a role key with validation
   */
  safeRoleKey: (key: string): RoleKey | null => {
    return roleHelpers.isValidRoleKey(key) ? key : null;
  },

  /**
   * Get a human-readable role name
   */
  getRoleName: (roleId: RoleId): string => {
    const roleKey = roleHelpers.idToKey(roleId);
    const nameMap: Record<RoleKey, string> = {
      superAdmin: 'Super Administrator',
      supportAdmin: 'Support Administrator',
      orgAdmin: 'Organization Administrator',
      orgMember: 'Organization Member',
      orgAccounting: 'Organization Accounting',
      orgClient: 'Organization Client',
    };
    return nameMap[roleKey];
  },

  /**
   * Get role description
   */
  getRoleDescription: (roleId: RoleId): string => {
    const roleKey = roleHelpers.idToKey(roleId);
    const descriptionMap: Record<RoleKey, string> = {
      superAdmin: 'Full system access across all organizations',
      supportAdmin: 'Support access across all organizations',
      orgAdmin: 'Full administrative access within organization',
      orgMember: 'Standard member access within organization',
      orgAccounting: 'Accounting and financial access within organization',
      orgClient: 'Limited client access within organization',
    };
    return descriptionMap[roleKey];
  },
};
