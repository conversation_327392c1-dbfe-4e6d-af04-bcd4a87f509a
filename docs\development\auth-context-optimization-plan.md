# Authentication Context Optimization Plan

## Problem Analysis Summary

Based on the provided logs and code analysis, the current implementation for handling user and organization status changes leads to redundant data fetching and incorrect client-side state updates, specifically when a user's active status in the current context organization changes.

*   **Redundant Server Fetches:** The list of user organizations is fetched in multiple server components (`src/app/dashboard/admin/organization/page.tsx`, `src/app/dashboard/layout.tsx`, `src/app/dashboard/developer/organizations/page.tsx`) using `getUserOrganizations`, and also by the `/api/organizations/authorized` API route.
*   **Redundant Client Fetches:** The `OrganizationSwitcher` component fetches the organization list via `/api/organizations/authorized` when a `member:status:changed` event occurs, which is redundant with the context refresh triggered by `DashboardScenarioManager`.
*   **Critical Empty API Response:** The `/api/auth/refresh-context` endpoint, called by `useServerContextRefresher`, sometimes returns an empty body despite a 200 OK status. This prevents the client-side Zustand store from receiving the updated context, specifically the `isUserActiveInOrg` status.
*   **Incorrect Client State:** Due to the empty API response, the client-side context store (`useAuthContextStore`) is not updated with the correct `isUserActiveInOrg: false` status after a user is deactivated.
*   **Broken Redirect Logic:** The `DashboardScenarioManager` relies on the incorrect client-side state (`ctx.isUserActiveInOrg === false`) to trigger the redirect to `/dashboard/account-disabled`. Since the state is not updated correctly, the redirect does not happen.

## Optimization Plan

1.  **Fix Empty Body Response in `/api/auth/refresh-context`:**
    *   Investigate the implementation of the `POST` function in [`src/app/api/auth/refresh-context/route.ts`](src/app/api/auth/refresh-context/route.ts).
    *   Ensure that `NextResponse.json()` is always called with a valid, non-empty JSON object containing the `context` property when the fetch is successful. Add more detailed logging *immediately before* the `return NextResponse.json(...)` call to inspect the exact content and headers being prepared for the response.

2.  **Refactor `OrganizationSwitcher` Event Handling:**
    *   Modify the `handleMemberStatusEvent` function in [`src/components/organization/organization-switcher.tsx`](src/components/organization/organization-switcher.tsx).
    *   Instead of fetching `/api/organizations/authorized` when a status change occurs, update the `localOrgs` state directly using the `event.isActive` payload provided by the `member:status:changed` event. This eliminates a redundant API call.

3.  **Refine `useServerContextRefresher` Null Data Handling:**
    *   In [`src/hooks/use-server-context-refresher.ts`](src/hooks/use-server-context-refresher.ts), adjust the `onSuccess` callback.
    *   If the `fetcher` returns `null` (due to an empty body from the API), instead of calling `updateFullContext({})`, log a warning and potentially keep the existing context state, or attempt a retry.

4.  **Review Server Component Data Fetching:**
    *   Examine the server components that call `getUserOrganizations` ([`src/app/dashboard/admin/organization/page.tsx`](src/app/dashboard/admin/organization/page.tsx), [`src/app/dashboard/layout.tsx`](src/app/dashboard/layout.tsx), [`src/app/dashboard/developer/organizations/page.tsx`](src/app/dashboard/developer/organizations/page.tsx)).
    *   Consider fetching the data once in the layout and passing it down as props to child components where needed.
    *   Ensure consistent usage of `getUserOrganizations` from a single location and leverage React's `cache` function.

5.  **Verify Redirect Logic:**
    *   Confirm that the redirect logic in `DashboardScenarioManager` (Rule 1) correctly checks `ctx.isUserActiveInOrg`. Once the client-side context is reliably updated (after fixing step 1), this logic should function correctly.

## Current (Problematic) Flow

```mermaid
graph TD
    A[Supabase Realtime Update: organization_members:updated] --> B(Client Event Bus: db:organization_members:updated)
    B --> C(StatusInterpreter: Emits member:status:changed)
    C --> D(DashboardScenarioManager: Receives member:status:changed)
    C --> E(OrganizationSwitcher: Receives member:status:changed)
    D --> F(useServerContextRefresher: Calls forceRefreshContext)
    E --> G(OrganizationSwitcher: Fetches /api/organizations/authorized)
    F --> H(API Route: /api/auth/refresh-context)
    G --> I(Database: Fetch Organizations)
    H --> J(Database: Fetch Context & Organizations)
    J --> K{API Response OK?}
    K -- Yes --> L{Body Empty?}
    L -- Yes --> M(useServerContextRefresher: Updates Zustand Store with Null/Empty Context)
    L -- No --> N(useServerContextRefresher: Updates Zustand Store with Context)
    M --> O(DashboardScenarioManager: Reads Incorrect State from Zustand)
    N --> P(DashboardScenarioManager: Reads Correct State from Zustand)
    O --> Q{Is User Inactive?}
    Q -- No (Incorrect State) --> R(No Redirect)
    P --> S{Is User Inactive?}
    S -- Yes (Correct State) --> T(Redirect to /account-disabled)
    I --> U(OrganizationSwitcher: Updates Local State)
```

## Proposed Optimized Flow

```mermaid
graph TD
    A[Supabase Realtime Update: organization_members:updated] --> B(Client Event Bus: db:organization_members:updated)
    B --> C(StatusInterpreter: Emits member:status:changed)
    C --> D(DashboardScenarioManager: Receives member:status:changed)
    C --> E(OrganizationSwitcher: Receives member:status:changed)
    D --> F(useServerContextRefresher: Calls forceRefreshContext)
    E --> G(OrganizationSwitcher: Updates Local State Directly from Event Payload)
    F --> H(API Route: /api/auth/refresh-context - FIX EMPTY BODY)
    H --> J(Database: Fetch Context & Organizations)
    J --> K{API Response OK?}
    K -- Yes --> L(useServerContextRefresher: Updates Zustand Store with Correct Context)
    L --> M(DashboardScenarioManager: Reads Correct State from Zustand)
    M --> N{Is User Inactive?}
    N -- Yes --> O(Redirect to /account-disabled)

    %% Optional: Server-side optimization
    P[Server Component: Layout] --> Q(Database: Fetch Organizations - Cached)
    Q --> R(Pass Organizations as Props)
    R --> S[Server Component: Child Pages/Components]

    %% Removed redundant client fetch
    G -- No Fetch --> E