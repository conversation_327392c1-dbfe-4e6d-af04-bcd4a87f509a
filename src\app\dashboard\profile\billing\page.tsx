import { ChevronRight, Download } from "lucide-react";
import {
  B<PERSON><PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

const plans = [
  {
    name: "Basic plan",
    price: "$10",
    period: "month",
    description:
      "Includes up to 10 users, 20GB individual data and access to all features.",
    current: true,
  },
  {
    name: "Business plan",
    price: "$20",
    period: "month",
    description:
      "Includes up to 20 users, 40GB individual data and access to all features.",
    current: false,
  },
  {
    name: "Enterprise plan",
    price: "$40",
    period: "month",
    description:
      "Unlimited users, unlimited individual data and access to all features.",
    current: false,
  },
];

const billingHistory = [
  {
    month: "Dec 2022",
    status: "Paid",
    amount: "USD $10.00",
    plan: "Basic plan",
  },
  {
    month: "Nov 2022",
    status: "Paid",
    amount: "USD $10.00",
    plan: "Basic plan",
  },
  {
    month: "Oct 2022",
    status: "Paid",
    amount: "USD $10.00",
    plan: "Basic plan",
  },
  {
    month: "Sep 2022",
    status: "Paid",
    amount: "USD $10.00",
    plan: "Basic plan",
  },
  {
    month: "Aug 2022",
    status: "Paid",
    amount: "USD $10.00",
    plan: "Basic plan",
  },
  {
    month: "Jul 2022",
    status: "Paid",
    amount: "USD $10.00",
    plan: "Basic plan",
  },
];

export default function BillingPage() {
  return (
    <div className="p-6 pt-0">
      <div className="mb-6 space-y-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/profile">Profile</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbPage>Billing</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="space-y-8">
        {/* Account Plans Section */}
        <div>
          <h2 className="text-2xl font-semibold mb-2 text-[#194852]">
            Account plans
          </h2>
          <p className="text-muted-foreground mb-6">
            Pick an account plan that fits your workflow.
          </p>
          <div className="grid gap-4">
            {plans.map((plan) => (
              <Card
                key={plan.name}
                className={cn(
                  "relative cursor-pointer transition-all hover:shadow-md",
                  plan.current && "border-[#194852] border-4"
                )}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold flex items-center gap-2">
                        {plan.name}
                        {plan.current && (
                          <Badge variant="default" className="bg-[#194852]">
                            Current plan
                          </Badge>
                        )}
                      </h3>
                      <p className="text-muted-foreground mt-1">
                        {plan.description}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-[#194852]">
                        {plan.price}
                        <span className="text-muted-foreground font-normal">
                          /{plan.period}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Billing History Section */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-semibold text-[#194852]">
                Billing and invoicing
              </h2>
              <p className="text-muted-foreground mt-1">
                Pick an account plan that fits your workflow.
              </p>
            </div>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download all
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">Invoice</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead className="text-right">Download</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {billingHistory.map((item) => (
                    <TableRow key={item.month}>
                      <TableCell className="font-medium">
                        {item.month}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className="bg-green-100 text-green-700 hover:bg-green-100"
                        >
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{item.amount}</TableCell>
                      <TableCell>{item.plan}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="link"
                          className="text-primary hover:text-primary/80"
                        >
                          Download
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
