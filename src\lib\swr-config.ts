import { SWRConfiguration } from 'swr';
import { handleAuthenticationError } from '@/lib/auth-error-handler';

/**
 * Global SWR configuration with authentication error handling
 */
export const swrConfig: SWRConfiguration = {
  // Global error handler for all SWR requests
  onError: (error, key) => {
    console.error(`[SWR] Error for key "${key}":`, error);
    
    // Handle authentication errors globally
    try {
      handleAuthenticationError(error);
    } catch (e) {
      // If it's not a 401 error, the handleAuthenticationError will throw
      // Let the individual components handle other types of errors
    }
  },
  
  // Global error retry configuration
  errorRetryCount: 3,
  errorRetryInterval: 1000,
  
  // Don't retry on 401 errors (authentication failures)
  shouldRetryOnError: (error) => {
    // Don't retry on 401 errors as they indicate session expiry
    if (error?.message?.includes('401') || 
        error?.message?.includes('Authentication required') ||
        error?.message?.includes('Unauthorized')) {
      return false;
    }
    
    // Retry on other errors
    return true;
  },
  
  // Global revalidation settings
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  
  // Deduplication settings
  dedupingInterval: 2000,
};

/**
 * Enhanced fetcher function with authentication error handling
 */
export async function swrFetcher(url: string, init?: RequestInit): Promise<any> {
  try {
    const response = await fetch(url, {
      ...init,
      headers: {
        'Content-Type': 'application/json',
        ...init?.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      const error = new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
      
      // Handle authentication errors
      handleAuthenticationError(error, response.status);
      
      // This line won't be reached if it's a 401, but satisfies TypeScript
      throw error;
    }

    return await response.json();
  } catch (error) {
    // Handle network errors and other exceptions
    if (error instanceof Error) {
      handleAuthenticationError(error);
    }
    throw error;
  }
}
