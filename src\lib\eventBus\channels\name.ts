// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import type { OrganizationDataChangedEvent, OrgNameChangedEvent } from '@/lib/eventTypes'

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organizations:* events and emit organization:name:changed events as appropriate.
 */
export function registerNameInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    // console.log('[NameInterpreter] Already registered, skipping duplicate registration')
    return
  }
  
  // REMOVE OR COMMENT OUT THE LINE BELOW:
  // console.log('[NameInterpreter] Registered', typeof window !== 'undefined' ? 'in browser' : 'on server')
  isRegistered = true
  
  emitter.on('db:organizations:updated', (event: OrganizationDataChangedEvent) => {
    const { orgId, changes, timestamp } = event
    if (!orgId || !changes || typeof changes.org_name !== 'string') return
    // Only emit if org_name actually changed
    const newName = changes.org_name as string
    if (!newName) return
    const nameEvent: OrgNameChangedEvent = {
      orgId,
      newName,
      timestamp,
    }
    clientLog(`[EventBus] Organization name changed for org ${orgId} to ${newName}`)
    emitter.emit('organization:name:changed', nameEvent)
  })
} 