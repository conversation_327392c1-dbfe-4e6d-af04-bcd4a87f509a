// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import { AUTH_CONTEXT_CHANGED } from '../constants'
import { useAuthContextStore } from '@/stores/useAuthContextStore'
import type { OrganizationDataChangedEvent, AuthEvent } from '@/lib/eventTypes'

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organizations:* events and emit organization:name:changed events as appropriate.
 */
export function registerNameInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    // console.log('[NameInterpreter] Already registered, skipping duplicate registration')
    return
  }
  
  // REMOVE OR COMMENT OUT THE LINE BELOW:
  // console.log('[NameInterpreter] Registered', typeof window !== 'undefined' ? 'in browser' : 'on server')
  isRegistered = true
  
  emitter.on('db:organizations:updated', (event: OrganizationDataChangedEvent) => {
    const { orgId, changes, timestamp } = event
    if (!orgId || !changes || typeof changes.org_name !== 'string') return
    // Only emit if org_name actually changed
    const newName = changes.org_name as string
    if (!newName) return

    // Only emit AUTH_CONTEXT_CHANGED for the current organization context
    const { orgId: currentOrgId, userId: currentUserId } = useAuthContextStore.getState();
    if (orgId !== currentOrgId || !currentUserId) {
      return; // Event is not for current organization context or no current user
    }

    const authEvent: AuthEvent = {
      userId: currentUserId,
      orgId,
      reason: `Organization name changed to "${newName}"`,
      data: {
        newName
      },
      timestamp,
    }

    clientLog(`[EventBus] Organization name changed for org ${orgId} to ${newName}`)
    console.log('[NameInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  })
} 