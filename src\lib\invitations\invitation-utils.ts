import { createClient } from '@/lib/supabase/client'
import type { EmailInvitation } from '@/components/shared/invitation-table'
import type { Organization } from '@/types/organization'

/**
 * Enriches invitations with organization names from the store
 * This avoids database joins and uses cached organization data
 */
export function enrichInvitationsWithOrgNames(
  invitations: any[],
  organizations: Organization[]
): any[] {
  if (!invitations || invitations.length === 0) {
    return []
  }

  return invitations.map(invitation => ({
    ...invitation,
    organizations: organizations
      .filter(org => org.id === invitation.org_id)
      .map(org => ({ org_name: org.name }))
  }))
}

/**
 * Enriches invitations with inviter profile data
 * This is needed because the foreign key points to auth.users but we want profiles data
 */
export async function enrichInvitationsWithProfiles(
  invitations: any[]
): Promise<EmailInvitation[]> {
  if (!invitations || invitations.length === 0) {
    return []
  }

  const supabase = createClient()

  // Get unique inviter IDs
  const inviterIds = [...new Set(invitations.map(inv => inv.invited_by).filter(Boolean))]

  if (inviterIds.length === 0) {
    return invitations.map(invitation => ({
      ...invitation,
      invited_by_profile: []
    }))
  }

  // Fetch profiles for all inviters
  const { data: profiles } = await supabase
    .from('profiles')
    .select('id, full_name')
    .in('id', inviterIds)

  // Map profiles to invitations
  return invitations.map(invitation => ({
    ...invitation,
    invited_by_profile: profiles?.filter(p => p.id === invitation.invited_by) || []
  }))
}

/**
 * Fetches invitations with enriched profile data for a specific organization
 * Organization names will be enriched client-side using the store
 */
export async function fetchInvitationsWithProfiles(
  orgId: string,
  roleIds: number[]
): Promise<EmailInvitation[]> {
  const supabase = createClient()

  // Fetch invitations
  const { data: invitations, error } = await supabase
    .from('email_invitations')
    .select(`
      id,
      org_id,
      email,
      role_id,
      status,
      created_at,
      expires_at,
      personal_message,
      resend_email_id,
      invited_by
    `)
    .in('role_id', roleIds)
    .eq('org_id', orgId)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Failed to fetch invitations:', error)
    return []
  }

  return enrichInvitationsWithProfiles(invitations || [])
}

/**
 * Fetches all invitations with enriched profile data (for developer view)
 * Organization names will be enriched client-side using the store
 */
export async function fetchAllInvitationsWithProfiles(
  roleIds: number[],
  orgIdFilter?: string
): Promise<EmailInvitation[]> {
  const supabase = createClient()

  let query = supabase
    .from('email_invitations')
    .select(`
      id,
      org_id,
      email,
      role_id,
      status,
      created_at,
      expires_at,
      personal_message,
      resend_email_id,
      invited_by
    `)
    .in('role_id', roleIds)

  // Apply organization filter if specified and not 'all'
  if (orgIdFilter && orgIdFilter !== 'all') {
    query = query.eq('org_id', orgIdFilter)
  }

  const { data: invitations, error } = await query.order('created_at', { ascending: false })

  if (error) {
    console.error('Failed to fetch invitations:', error)
    return []
  }

  return enrichInvitationsWithProfiles(invitations || [])
}
