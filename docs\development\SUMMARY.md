# Enhanced Realtime Event System

## Overview

We've implemented a comprehensive realtime event system for organization-related changes to ensure all components that rely on this information are automatically updated. The system now tracks changes to:

1. `org_member_role` - User role changes within an organization
2. `org_member_is_active` - Member status changes (active/inactive)
3. `org_name` - Organization name changes
4. `is_current_context` - Context switching between organizations

## Architecture

The implementation follows a publish-subscribe pattern with these key components:

### 1. OrganizationEventBus (Singleton)

A centralized event bus that:
- Manages Supabase realtime subscriptions
- Debounces and deduplicates events
- Provides type-safe event handlers
- Maintains subscription references for cleanup
- Monitors connection health and auto-reconnects when needed
- Maintains activity timestamps to detect stale connections

### 2. Event Types

- `OrganizationContextEvent` - Context switches
- `UserRoleChangedEvent` - Role changes
- `MemberStatusChangedEvent` - Member status changes 
- `OrgNameChangedEvent` - Organization name changes

### 3. React Hooks

Custom hooks for component integration:
- `useOrganizationContextEvents` - Subscribe to context changes
- `useUserRoleEvents` - Subscribe to role changes
- `useMemberStatusEvents` - Subscribe to member status changes
- `useOrgNameEvents` - Subscribe to organization name changes

### 4. DashboardEventManager

A centralized component that:
- Manages dashboard-wide event subscriptions
- Refreshes the UI when important events occur
- Is included in the DashboardProvider for global availability
- Performs periodic health checks to ensure connection stability
- Implements periodic refresh to ensure UI stays synchronized
- Monitors connection health and forces reconnects as needed

## Connection Stability Features

To ensure reliable realtime updates even during long sessions, we've implemented:

1. **Connection Health Monitoring**: Tracks the last activity timestamp and detects stale connections
2. **Automatic Reconnection**: Reconnects channels automatically after periods of inactivity
3. **Periodic UI Refresh**: Performs a full UI refresh every 10 minutes to ensure synchronization
4. **Manual Reconnection**: Provides an admin interface for manually forcing reconnection
5. **Connection Status Dashboard**: Shows detailed connection health information for debugging

## Implementation Examples

### Organization Switcher

The organization switcher now:
- Updates organization names in realtime
- Updates user roles in realtime
- Maintains local state synchronized with the server

### Test Component

A TestEventSystem component that:
- Shows all events as they occur
- Allows admins to trigger test events
- Provides a debugging interface
- Displays connection health status
- Allows manual reconnection of channels

## How to Use

### Basic Usage

```tsx
import { useOrganizationContextEvents, OrganizationContextEvent } from '@/lib/event-bus';

function MyComponent({ userId }) {
  const handleContextChange = useCallback((event: OrganizationContextEvent) => {
    // Handle the context change
    console.log('Context changed:', event);
  }, []);

  // Subscribe to organization context changes
  useOrganizationContextEvents(userId, handleContextChange);

  return <div>My Component</div>;
}
```

### Advanced Usage

To add event handling for a new event type:

1. Add a new event type in `event-bus.ts`
2. Add appropriate subscription and handler methods
3. Create a React hook for the event type
4. Update the DashboardEventManager to handle the new event

### Connection Monitoring

To get the current connection status:

```tsx
import { organizationEventBus } from '@/lib/event-bus';

// Get connection status
const status = organizationEventBus.getConnectionStatus();

// Force a reconnection if needed
organizationEventBus.forceReconnect();
```

## Testing

Access the event testing page at `/dashboard/admin/test-events` (admin access only).

## Benefits

- Reduced duplicate events through debouncing and deduplication
- Type-safe event handling
- Consistent UI updates across the application
- Better user experience with immediate feedback
- Reliable long-running connections with automatic recovery
- Graceful handling of network interruptions
- Periodic refresh ensures UI stays in sync with database 