import { createClient } from '@/lib/supabase/server'
import { Organization } from '@/types/organization'

// Define OrganizationMembershipResponse locally based on the expected query result structure
interface OrganizationMembershipResponse {
  org_id: string;
  org_member_role: number;
  org_member_is_active: boolean;
  is_default_org: boolean;
  organizations: {
    id: string;
    org_name: string;
    org_icon: string | null;
    created_at: string;
    updated_at: string;
    is_active: boolean;
  };
  roles: {
    role_id: number;
    role_name: string;
  };
}

const ORGANIZATION_SELECT_QUERY = `
  org_id,
  org_member_role,
  org_member_is_active,
  is_default_org,
  organizations!inner (
    id,
    org_name,
    org_icon,
    created_at,
    updated_at,
    is_active
  ),
  roles!org_member_role (
    role_id,
    role_name
  )
`

export interface PaginatedOrganizationsResponse {
  organizations: Organization[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export async function getUserOrganizations(
  userId: string,
  includeInactive: boolean = true,
  page: number = 1,
  pageSize: number = 10, // Default page size
  search?: string // Optional search parameter
): Promise<PaginatedOrganizationsResponse> {
  if (!userId) {
    console.error('Invalid userId provided to getUserOrganizations')
    return { organizations: [], totalCount: 0, page, pageSize }
  }

  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Auth error in getUserOrganizations:', userError || 'No user found')
      return { organizations: [], totalCount: 0, page, pageSize }
    }

    const from = (page - 1) * pageSize
    const to = from + pageSize - 1

    // Base query for counting total matching records
    let countQuery = supabase
      .from('organization_members')
      .select('org_id', { count: 'exact', head: true })
      .eq('user_id', userId)

    let dataQuery = supabase
      .from('organization_members')
      .select(ORGANIZATION_SELECT_QUERY)
      .eq('user_id', userId)

    if (!includeInactive) {
      countQuery = countQuery.eq('org_member_is_active', true)
      dataQuery = dataQuery.eq('org_member_is_active', true)
    } else {
      countQuery = countQuery.or('org_member_is_active.eq.true,org_member_is_active.eq.false')
      dataQuery = dataQuery.or('org_member_is_active.eq.true,org_member_is_active.eq.false')
    }

    const { data: roleCheck } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role')
      .eq('user_id', userId)
      .eq('org_member_role', 1) // 1 = SUPERADMIN role
      .limit(1)

    const isSuperAdmin = roleCheck && roleCheck.length > 0

    if (!isSuperAdmin) {
      // For non-superadmins, also filter the count by active organizations
      countQuery = countQuery.eq('organizations.is_active', true)
      dataQuery = dataQuery.eq('organizations.is_active', true)
    }

    // Add search filter if provided
    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`
      countQuery = countQuery.ilike('organizations.org_name', searchTerm)
      dataQuery = dataQuery.ilike('organizations.org_name', searchTerm)
    }

    // Get total count
    const { count: totalCount, error: countError } = await countQuery

    if (countError) {
      console.error('Error counting user organizations:', { error: countError, userId })
      // Fallback or throw, for now returning empty with 0 count
      return { organizations: [], totalCount: 0, page, pageSize }
    }

    // Fetch paginated data
    const { data: memberships, error: membershipError } = await dataQuery
      .order('is_default_org', { ascending: false })
      .order('org_name', { ascending: true, referencedTable: 'organizations' })
      .range(from, to)

    if (membershipError) {
      console.error('Error fetching user organizations:', { error: membershipError, userId })
      return { organizations: [], totalCount: totalCount || 0, page, pageSize } // Return count if available
    }

    if (!memberships?.length) {
      return { organizations: [], totalCount: totalCount || 0, page, pageSize }
    }

    const transformedOrganizations = (memberships as unknown[] as OrganizationMembershipResponse[]).map(membership => ({
      id: membership.organizations.id,
      name: membership.organizations.org_name,
      org_icon: membership.organizations.org_icon || '',
      role: membership.roles.role_name,
      org_member_role: membership.org_member_role,
      org_member_is_active: membership.org_member_is_active,
      isActive: membership.organizations.is_active,
      isDefault: membership.is_default_org,
      createdAt: membership.organizations.created_at,
      updatedAt: membership.organizations.updated_at
    }))

    return {
      organizations: transformedOrganizations,
      totalCount: totalCount || 0,
      page,
      pageSize
    }
  } catch (error) {
    console.error('Error in getUserOrganizations:', error)
    return { organizations: [], totalCount: 0, page, pageSize }
  }
}

export async function getUserOrganizationById(userId: string, orgId: string): Promise<Organization | null> {
  if (!userId || !orgId) {
    console.error('Invalid parameters for getUserOrganizationById')
    return null
  }

  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Auth error in getUserOrganizationById:', userError || 'No user found')
      return null
    }

    const { data: memberships, error: queryError } = await supabase
      .from('organization_members')
      .select(ORGANIZATION_SELECT_QUERY)
      .eq('user_id', userId)
      .eq('org_id', orgId)
      .eq('org_member_is_active', true)
      // Removed .eq('organizations.is_active', true) to fetch disabled organizations too
      .single()

    if (queryError) {
      console.error('Error fetching organization:', {
        error: queryError,
        userId,
        orgId
      })
      return null
    }

    if (!memberships) {
      console.log('No organization found:', { userId, orgId })
      return null
    }

    const typedMembership = memberships as unknown as OrganizationMembershipResponse

    console.log('Found organization:', {
      userId,
      orgId: typedMembership.organizations.id,
      orgName: typedMembership.organizations.org_name,
      role: typedMembership.roles.role_name
    })

    return {
      id: typedMembership.organizations.id,
      name: typedMembership.organizations.org_name,
      org_icon: typedMembership.organizations.org_icon || '',
      role: typedMembership.roles.role_name,
      org_member_role: typedMembership.org_member_role,
      isActive: typedMembership.organizations.is_active,
      isDefault: typedMembership.is_default_org,
      createdAt: typedMembership.organizations.created_at,
      updatedAt: typedMembership.organizations.updated_at
    }
  } catch (error) {
    console.error('Error in getUserOrganizationById:', error)
    return null
  }
}

export async function getUserDefaultOrganization(userId: string): Promise<Organization | null> {
  if (!userId) {
    console.error('Invalid userId for getUserDefaultOrganization')
    return null
  }

  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Auth error in getUserDefaultOrganization:', userError || 'No user found')
      return null
    }

    const { data: memberships, error: queryError } = await supabase
      .from('organization_members')
      .select(ORGANIZATION_SELECT_QUERY)
      .eq('user_id', userId)
      .eq('is_default_org', true)
      .eq('org_member_is_active', true)
      .eq('organizations.is_active', true)
      .single()

    if (queryError) {
      console.error('Error fetching default organization:', {
        error: queryError,
        userId
      })
      return null
    }

    if (!memberships) {
      console.log('No default organization found for user:', userId)
      return null
    }

    const typedMembership = memberships as unknown as OrganizationMembershipResponse

    console.log('Found default organization:', {
      userId,
      orgId: typedMembership.organizations.id,
      orgName: typedMembership.organizations.org_name,
      role: typedMembership.roles.role_name
    })

    return {
      id: typedMembership.organizations.id,
      name: typedMembership.organizations.org_name,
      org_icon: typedMembership.organizations.org_icon || '',
      role: typedMembership.roles.role_name,
      org_member_role: typedMembership.org_member_role,
      isActive: typedMembership.organizations.is_active,
      isDefault: typedMembership.is_default_org,
      createdAt: typedMembership.organizations.created_at,
      updatedAt: typedMembership.organizations.updated_at
    }
  } catch (error) {
    console.error('Error in getUserDefaultOrganization:', error)
    return null
  }
}

export async function getCurrentUserActiveOrganization(orgIdParam?: string): Promise<{ organization: Organization | null; error: Error | null }> {
  console.log('Resolving active organization:', {
    context: orgIdParam ? 'URL parameter' : 'Default flow',
    orgIdParam: orgIdParam || 'not provided'
  })

  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Auth error in getCurrentUserActiveOrganization:', userError || 'No user found')
      return { organization: null, error: userError || new Error('No authenticated user found') }
    }

    // If orgIdParam is provided (from URL), verify access to that specific organization
    if (orgIdParam) {
      const organization = await getUserOrganizationById(user.id, orgIdParam)
      if (organization) {
        console.log('Access granted to requested organization:', {
          userId: user.id,
          orgId: orgIdParam,
          orgName: organization.name,
          role: organization.role
        })
        return { organization, error: null }
      }
      console.log('No access to requested organization:', {
        userId: user.id,
        orgId: orgIdParam
      })
    }

    // First, check if the user is a superadmin
    const { data: roleCheck } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role')
      .eq('user_id', user.id)
      .eq('org_member_role', 1) // 1 = SUPERADMIN role
      .limit(1)

    const isSuperAdmin = roleCheck && roleCheck.length > 0

    // Get active organization from database context
    let contextQuery = supabase
      .from('organization_members')
      .select(ORGANIZATION_SELECT_QUERY)
      .eq('user_id', user.id)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)

    // For superadmins, don't filter out inactive organizations
    if (!isSuperAdmin) {
      contextQuery = contextQuery.eq('organizations.is_active', true)
    }

    const { data: currentContextOrg, error: contextError } = await contextQuery.single()

    if (!contextError && currentContextOrg) {
      const typedMembership = currentContextOrg as unknown as OrganizationMembershipResponse
      const organization = {
        id: typedMembership.organizations.id,
        name: typedMembership.organizations.org_name,
        org_icon: typedMembership.organizations.org_icon || '',
        role: typedMembership.roles.role_name,
        org_member_role: typedMembership.org_member_role,
        org_member_is_active: typedMembership.org_member_is_active,
        isActive: typedMembership.organizations.is_active,
        isDefault: typedMembership.is_default_org,
        createdAt: typedMembership.organizations.created_at,
        updatedAt: typedMembership.organizations.updated_at
      }

      console.log('Using current context organization from DB:', {
        userId: user.id,
        orgId: organization.id,
        orgName: organization.name,
        role: organization.role,
        isActive: organization.isActive // Log the organization's active status
      })

      return { organization, error: null }
    }

    // No context found, initialize one
    const { data: orgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: user.id
    })

    if (initError || !orgId) {
      console.log('No organizations found or failed to initialize context:', initError || 'No orgId returned')
      return { organization: null, error: new Error('No organization found for user') }
    }

    // Get the organization that was set as context
    const organization = await getUserOrganizationById(user.id, orgId)
    if (organization) {
      console.log('Using initialized organization context:', {
        userId: user.id,
        orgId: organization.id,
        orgName: organization.name,
        role: organization.role
      })
      return { organization, error: null }
    }

    // This should not happen if the function worked correctly
    console.log('Failed to get organization after context initialization')
    return { organization: null, error: new Error('Failed to initialize organization context') }
  } catch (error) {
    console.error('Error in getCurrentUserActiveOrganization:', error)
    return { organization: null, error: error instanceof Error ? error : new Error(String(error)) }
  }
}