import { RATE_LIMITS } from './validation';

// In-memory rate limiting store (in production, use Redis or database)
interface RateLimitEntry {
  count: number;
  resetTime: number;
  lastRequest: number;
}

class RateLimiter {
  private emailStore = new Map<string, RateLimitEntry>();
  private ipStore = new Map<string, RateLimitEntry>();

  // Clean up expired entries periodically
  private cleanup() {
    const now = Date.now();

    // Clean email store
    for (const [key, entry] of this.emailStore.entries()) {
      if (now > entry.resetTime) {
        this.emailStore.delete(key);
      }
    }

    // Clean IP store
    for (const [key, entry] of this.ipStore.entries()) {
      if (now > entry.resetTime) {
        this.ipStore.delete(key);
      }
    }
  }

  // Check if email is rate limited
  checkEmailLimit(email: string): { allowed: boolean; resetTime?: number; remainingRequests?: number } {
    this.cleanup();

    const now = Date.now();
    const hourKey = `${email}:hour`;
    const dayKey = `${email}:day`;

    // Check hourly limit
    const hourEntry = this.emailStore.get(hourKey);
    if (hourEntry && hourEntry.count >= RATE_LIMITS.EMAIL_REQUESTS_PER_HOUR) {
      return { allowed: false, resetTime: hourEntry.resetTime };
    }

    // Check daily limit
    const dayEntry = this.emailStore.get(dayKey);
    if (dayEntry && dayEntry.count >= RATE_LIMITS.EMAIL_REQUESTS_PER_DAY) {
      return { allowed: false, resetTime: dayEntry.resetTime };
    }

    // Check cooldown
    const lastRequest = hourEntry?.lastRequest || 0;
    const timeSinceLastRequest = now - lastRequest;
    if (timeSinceLastRequest < RATE_LIMITS.COOLDOWN_SECONDS * 1000) {
      const resetTime = lastRequest + (RATE_LIMITS.COOLDOWN_SECONDS * 1000);
      return { allowed: false, resetTime };
    }

    return {
      allowed: true,
      remainingRequests: RATE_LIMITS.EMAIL_REQUESTS_PER_HOUR - (hourEntry?.count || 0)
    };
  }

  // Check if IP is rate limited
  checkIPLimit(ip: string): { allowed: boolean; resetTime?: number } {
    this.cleanup();

    const minuteKey = `${ip}:minute`;
    const hourKey = `${ip}:hour`;
    const dayKey = `${ip}:day`;

    // Check minute limit
    const minuteEntry = this.ipStore.get(minuteKey);
    if (minuteEntry && minuteEntry.count >= RATE_LIMITS.IP_REQUESTS_PER_MINUTE) {
      return { allowed: false, resetTime: minuteEntry.resetTime };
    }

    // Check hourly limit
    const hourEntry = this.ipStore.get(hourKey);
    if (hourEntry && hourEntry.count >= RATE_LIMITS.IP_REQUESTS_PER_HOUR) {
      return { allowed: false, resetTime: hourEntry.resetTime };
    }

    // Check daily limit
    const dayEntry = this.ipStore.get(dayKey);
    if (dayEntry && dayEntry.count >= RATE_LIMITS.IP_REQUESTS_PER_DAY) {
      return { allowed: false, resetTime: dayEntry.resetTime };
    }

    return { allowed: true };
  }

  // Record a request for email
  recordEmailRequest(email: string): void {
    const now = Date.now();

    // Record hourly
    const hourKey = `${email}:hour`;
    const hourResetTime = now + (60 * 60 * 1000); // 1 hour
    const hourEntry = this.emailStore.get(hourKey);

    if (hourEntry) {
      hourEntry.count++;
      hourEntry.lastRequest = now;
    } else {
      this.emailStore.set(hourKey, { count: 1, resetTime: hourResetTime, lastRequest: now });
    }

    // Record daily
    const dayKey = `${email}:day`;
    const dayResetTime = now + (24 * 60 * 60 * 1000); // 24 hours
    const dayEntry = this.emailStore.get(dayKey);

    if (dayEntry) {
      dayEntry.count++;
      dayEntry.lastRequest = now;
    } else {
      this.emailStore.set(dayKey, { count: 1, resetTime: dayResetTime, lastRequest: now });
    }
  }

  // Record a request for IP
  recordIPRequest(ip: string): void {
    const now = Date.now();

    // Record per minute
    const minuteKey = `${ip}:minute`;
    const minuteResetTime = now + (60 * 1000); // 1 minute
    const minuteEntry = this.ipStore.get(minuteKey);

    if (minuteEntry) {
      minuteEntry.count++;
      minuteEntry.lastRequest = now;
    } else {
      this.ipStore.set(minuteKey, { count: 1, resetTime: minuteResetTime, lastRequest: now });
    }

    // Record hourly
    const hourKey = `${ip}:hour`;
    const hourResetTime = now + (60 * 60 * 1000); // 1 hour
    const hourEntry = this.ipStore.get(hourKey);

    if (hourEntry) {
      hourEntry.count++;
      hourEntry.lastRequest = now;
    } else {
      this.ipStore.set(hourKey, { count: 1, resetTime: hourResetTime, lastRequest: now });
    }

    // Record daily
    const dayKey = `${ip}:day`;
    const dayResetTime = now + (24 * 60 * 60 * 1000); // 24 hours
    const dayEntry = this.ipStore.get(dayKey);

    if (dayEntry) {
      dayEntry.count++;
      dayEntry.lastRequest = now;
    } else {
      this.ipStore.set(dayKey, { count: 1, resetTime: dayResetTime, lastRequest: now });
    }
  }

  // Get remaining cooldown time for email
  getCooldownTime(email: string): number {
    const hourKey = `${email}:hour`;
    const entry = this.emailStore.get(hourKey);

    if (!entry) return 0;

    const now = Date.now();
    const timeSinceLastRequest = now - entry.lastRequest;
    const remainingCooldown = RATE_LIMITS.COOLDOWN_SECONDS * 1000 - timeSinceLastRequest;

    return Math.max(0, Math.ceil(remainingCooldown / 1000));
  }
}

// Singleton instance
export const rateLimiter = new RateLimiter();

// Helper function to get client IP
export function getClientIP(request: Request): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to a default (this shouldn't happen in production)
  return '127.0.0.1';
}
