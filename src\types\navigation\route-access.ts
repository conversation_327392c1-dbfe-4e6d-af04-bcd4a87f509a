import type { RbacConditions } from '@/types/lib/rbac';

/**
 * Result of route access evaluation
 */
export interface RouteAccessResult {
  /** Whether the user can access the route */
  canAccess: boolean;
  /** The RBAC conditions that apply to this route */
  rbacConditions: RbacConditions | undefined;
  /** Whether this route requires organization-scoped data */
  requiresOrgData: boolean;
  /** The data scope type for this route */
  dataScope: 'organization' | 'user' | 'global';
  /** Reason for access denial (if applicable) */
  reason: string | undefined;
}

/**
 * Context for evaluating route access
 */
export interface RouteAccessContext {
  /** Current route path */
  currentPath: string;
  /** User's role ID in the target organization */
  userRoleId: number;
  /** Whether the user is active in the target organization */
  isUserActiveInOrg: boolean;
  /** Whether the target organization is active */
  isOrgActive: boolean;
  /** Whether the user is a super admin */
  isSuperAdmin?: boolean;
}

/**
 * Route classification information
 */
export interface RouteClassification {
  /** The data scope of the route */
  dataScope: 'organization' | 'user' | 'global';
  /** Whether the route requires data refresh on organization switch */
  requiresOrgRefresh: boolean;
  /** Whether the route is accessible across organizations */
  crossOrgAccessible: boolean;
  /** Special handling requirements */
  specialHandling?: 'disabled-org' | 'disabled-user' | 'developer-only';
}
