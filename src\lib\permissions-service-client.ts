import { emitter } from '@/lib/eventBus'
import { evaluateRbac } from '@/lib/rbac/rbac-utils'
import { useAuthContextStore } from '@/stores/useAuthContextStore'
import type { RbacConditions } from '@/types/lib/rbac'

// TTL for RBAC cache (60 seconds)
const PERM_TTL = 60_000

// Unified cache for all RBAC operations
export const rbacCache = new Map<string, { result: boolean | UserPermissionsMatrix; ts: number }>()
interface UserPermissionsMatrix {
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
  canView: boolean;
  canInviteMember: boolean;
  canManageOrgSettings: boolean;
  isSuperAdmin: boolean;
  isOrgAdmin: boolean;
}

// No additional caches needed - using unified rbacCache

/**
 * Initialize event listeners for cache invalidation
 */
function initEventListeners() {
  // Use the unified AUTH_CONTEXT_CHANGED event for all permission cache invalidation
  import('@/lib/eventBus/constants').then(({ AUTH_CONTEXT_CHANGED }) => {
    emitter.on(AUTH_CONTEXT_CHANGED, (event) => {
      console.log('[PermissionsService] Auth context changed event received, invalidating cache for user', event.userId);
      invalidatePermissionCache(event.userId);
    });
  });
  
  // Note: Legacy event listeners have been removed in favor of the unified AUTH_CONTEXT_CHANGED event
}

// Initialize the event listeners if we're on the client side
if (typeof window !== 'undefined') {
  initEventListeners()
}

/**
 * Invalidate all permissions in cache for a specific user
 */
export function invalidatePermissionCache(userId: string) {
  if (!userId) return

  let invalidatedCount = 0;
  // Delete all cache entries for this user from unified rbacCache
  for (const key of rbacCache.keys()) {
    if (key.startsWith(`${userId}|`)) {
      rbacCache.delete(key)
      invalidatedCount++;
    }
  }
  if (invalidatedCount > 0) {
    console.log(`[PermissionsService] Invalidated ${invalidatedCount} permissions cache entries for user ${userId} due to real-time event`);
  }
}

/**
 * Create a unique cache key for a permission check
 */
function createCacheKey(userId: string, orgId: string, checkType: string, params: string): string {
  return `${userId}:${orgId}:${checkType}:${params}`
}

/**
 * Get cached permission value with TTL check
 */
function getCachedPermission(cacheKey: string): boolean | null {
  const cached = rbacCache.get(cacheKey)
  if (cached && (Date.now() - cached.ts < PERM_TTL)) {
    return cached.result as boolean
  }

  // Clear expired cache entry
  if (cached) {
    rbacCache.delete(cacheKey)
  }

  return null
}

/**
 * Get cached permissions matrix with TTL check
 */
function getCachedMatrix(cacheKey: string): UserPermissionsMatrix | null {
  const cached = rbacCache.get(cacheKey)
  if (cached && (Date.now() - cached.ts < PERM_TTL)) {
    return cached.result as UserPermissionsMatrix
  }

  // Clear expired cache entry
  if (cached) {
    rbacCache.delete(cacheKey)
  }

  return null
}

/**
 * Cache permission result with timestamp
 */
function cachePermission(cacheKey: string, result: boolean): void {
  rbacCache.set(cacheKey, {
    result,
    ts: Date.now()
  })
}

/**
 * Cache permissions matrix with timestamp
 */
function cacheMatrix(cacheKey: string, result: UserPermissionsMatrix): void {
  rbacCache.set(cacheKey, {
    result,
    ts: Date.now()
  })
}

/**
 * Check RBAC permissions for a user with TTL caching
 * Client-side version that uses the client-side auth context
 */
export async function checkPermissionClient(
  userIdFromParam: string,
  rbacConditions: RbacConditions,
  skipCache: boolean = false
): Promise<boolean> {
  const { userId: storeUserId, orgId: storeOrgId, roleId: storeRoleId, isLoading: storeIsLoading } = useAuthContextStore.getState();

  const effectiveUserId = userIdFromParam || storeUserId;

  if (!effectiveUserId) {
    console.warn('[checkPermissionClient] No effective user ID available from param or store.');
    return false;
  }
  
  if (storeIsLoading && !userIdFromParam) {
    console.warn('[checkPermissionClient] Auth context store is loading, deferring permission check.');
    return false;
  }

  const orgIdFromConditions = rbacConditions.orgId || rbacConditions.resourceOrgId || null;
  const shouldUseCurrentContextFromStore = !orgIdFromConditions && rbacConditions.orgContext === 'current';
  
  let effectiveOrgId = orgIdFromConditions;
  if (shouldUseCurrentContextFromStore) {
    effectiveOrgId = storeOrgId;
  }

  if (!effectiveOrgId && shouldUseCurrentContextFromStore) {
    console.warn('[checkPermissionClient] orgContext is "current" but no orgId in store.');
    return false;
  }
  
  const effectiveRoleId = (effectiveOrgId === storeOrgId) ? storeRoleId : null;

  const cacheKey = createCacheKey(
    effectiveUserId,
    effectiveOrgId || 'allOrgs',
    'permissionCheck',
    JSON.stringify(rbacConditions)
  )

  if (!skipCache) {
    const cachedResult = getCachedPermission(cacheKey)
    if (cachedResult !== null) {
      return cachedResult
    }
  }

  if (effectiveRoleId === null || typeof effectiveRoleId === 'undefined') {
    if (!effectiveOrgId && storeUserId === effectiveUserId && storeRoleId === 1 && evaluateRbac(storeRoleId, rbacConditions)) {
        cachePermission(cacheKey, true);
        return true;
    }

    console.warn(`[checkPermissionClient] Cannot determine roleId for user ${effectiveUserId} and org ${effectiveOrgId || 'any'}. Store context: userId=${storeUserId}, orgId=${storeOrgId}, roleId=${storeRoleId}. Conditions: ${JSON.stringify(rbacConditions)}`);
    return false;
  }

  const result = evaluateRbac(effectiveRoleId, rbacConditions)
  
  cachePermission(cacheKey, result)
  return result
}

/**
 * Get all permissions for a user in a specific organization with TTL caching
 * Client-side version
 */
export async function getUserPermissionsClient(
  userIdFromParam: string,
  orgIdFromParam?: string,
  skipCache: boolean = false
): Promise<UserPermissionsMatrix> {
  const { 
    userId: storeUserId, 
    orgId: storeOrgId, 
    roleId: storeRoleId, 
    isLoading: storeIsLoading 
  } = useAuthContextStore.getState();

  const effectiveUserId = userIdFromParam || storeUserId;
  
  const defaultPermissions: UserPermissionsMatrix = {
      canCreate: false, canUpdate: false, canDelete: false, canView: false,
      canInviteMember: false, canManageOrgSettings: false,
      isSuperAdmin: false, isOrgAdmin: false
  };

  if (!effectiveUserId) {
    console.warn('[getUserPermissionsClient] No effective user ID.');
    return defaultPermissions;
  }

  if (storeIsLoading && !userIdFromParam) {
     console.warn('[getUserPermissionsClient] Auth context store is loading.');
    return defaultPermissions;
  }
  
  let targetOrgId: string | undefined = orgIdFromParam;
  if (!targetOrgId && effectiveUserId === storeUserId) {
    targetOrgId = storeOrgId === null ? undefined : storeOrgId;
  }

  if (!targetOrgId) {
    console.warn(`[getUserPermissionsClient] No target organization ID for user ${effectiveUserId}. Cannot determine permissions.`);
     // Check for superAdmin if no org context
    if (effectiveUserId === storeUserId && storeRoleId === 1 /* RoleId.SUPERADMIN */) {
        const superAdminPermissions: UserPermissionsMatrix = {
            canCreate: true, canUpdate: true, canDelete: true, canView: true,
            canInviteMember: true, canManageOrgSettings: true,
            isSuperAdmin: true, isOrgAdmin: false
        };
        const cacheKeyForSuperAdmin = createCacheKey(effectiveUserId, 'allOrgs', 'permissionsMatrix', '');
        cacheMatrix(cacheKeyForSuperAdmin, superAdminPermissions);
        return superAdminPermissions;
    }
    return defaultPermissions;
  }
  
  const roleIdToUse = (effectiveUserId === storeUserId && targetOrgId === storeOrgId) ? storeRoleId : null;

  if (roleIdToUse === null || typeof roleIdToUse === 'undefined') {
     console.warn(`[getUserPermissionsClient] Could not determine roleId for user ${effectiveUserId} in org ${targetOrgId}. Store context: userId=${storeUserId}, orgId=${storeOrgId}, roleId=${storeRoleId}.`);
    if (effectiveUserId === storeUserId && storeRoleId === 1 /* RoleId.SUPERADMIN */) {
        const superAdminPermissions: UserPermissionsMatrix = {
            canCreate: true, canUpdate: true, canDelete: true, canView: true,
            canInviteMember: true, canManageOrgSettings: true,
            isSuperAdmin: true, isOrgAdmin: false 
        };
        const cacheKeyForSuperAdmin = createCacheKey(effectiveUserId, 'allOrgs', 'permissionsMatrix', '');
         cacheMatrix(cacheKeyForSuperAdmin, superAdminPermissions);
        return superAdminPermissions;
    }
    return defaultPermissions;
  }

  const cacheKey = createCacheKey(
    effectiveUserId,
    (targetOrgId as string | undefined) || 'allOrgs',
    'permissionsMatrix',
    ''
  )

  if (!skipCache) {
    const cached = getCachedMatrix(cacheKey);
    if (cached !== null) {
      return cached;
    }
  }

  const roleId = roleIdToUse;
  const result: UserPermissionsMatrix = {
    canCreate: evaluateRbac(roleId, { crMinRole: "orgMember" }),
    canUpdate: evaluateRbac(roleId, { ruMinRole: "orgMember" }),
    canDelete: evaluateRbac(roleId, { rdMinRole: "orgAdmin" }),
    canView: evaluateRbac(roleId, { rMinRole: "orgClient" }),
    canInviteMember: evaluateRbac(roleId, { crMinRole: "orgAdmin" }),
    canManageOrgSettings: evaluateRbac(roleId, { cruMinRole: "orgAdmin" }),
    isSuperAdmin: evaluateRbac(roleId, { rRoles: ["superAdmin"] }),
    isOrgAdmin: evaluateRbac(roleId, { rRoles: ["orgAdmin"] })
  };

  cacheMatrix(cacheKey, result);

  return result;
}

// Additional client-friendly permission check functions
export const clientPermissionFunctions = {
  checkPermission: checkPermissionClient,
  getUserPermissions: getUserPermissionsClient,
  
  // We can add more client-side permission functions here as needed
  // For example: checkCanToggleUserStatus, checkCanChangeUserRole, etc.
} 