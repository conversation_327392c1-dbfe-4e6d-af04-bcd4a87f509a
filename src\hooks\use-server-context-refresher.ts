import useSWR from 'swr';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { refreshAuthContext } from '@/lib/refresh-auth-context';
import { toast } from '@/hooks/use-toast';
import { useEffect } from 'react';
import { handleAuthenticationError } from '@/lib/auth-error-handler';

export interface ApiRefreshedAuthContext {
  userId?: string | null;
  orgId?: string | null;
  roleId?: number | null;
  isUserActiveInOrg?: boolean | null;
  isOrgActive?: boolean | null;
  isSuperAdmin?: boolean;
  userEmail?: string | null;
  userFullName?: string | null;
  avatarUrl?: string | null;
  activeOrgName?: string | null;
  isActiveOrgDisabled?: boolean | null;
}

const API_CONTEXT_KEY = '/api/auth/refresh-context';

export function useServerContextRefresher(initialContext?: ApiRefreshedAuthContext, enabled = true) {
  const { updateFullContext, setLoading, setError } = useAuthContextStore();
  const allCurrentState = useAuthContextStore.getState();

  // Extract only data properties for fallbackData to avoid cloning functions
  const currentStateData: ApiRefreshedAuthContext = {
    userId: allCurrentState.userId,
    orgId: allCurrentState.orgId,
    roleId: allCurrentState.roleId,
    isUserActiveInOrg: allCurrentState.isUserActiveInOrg,
    isOrgActive: allCurrentState.isOrgActive,
    isSuperAdmin: allCurrentState.isSuperAdmin,
    userEmail: allCurrentState.userEmail,
    userFullName: allCurrentState.userFullName,
    avatarUrl: allCurrentState.avatarUrl,
    activeOrgName: allCurrentState.activeOrgName,
    // isActiveOrgDisabled is derived, not directly in store usually, but ApiRefreshedAuthContext has it.
    // If it's part of `initialContext` it's fine. If derived from `isOrgActive`, ensure consistency.
    // For cloning, we can include it if it's on allCurrentState or derive it.
    // Let's assume it could be on allCurrentState if it mirrors ApiRefreshedAuthContext structure.
    isActiveOrgDisabled: allCurrentState.isOrgActive === false, // Derive for safety if not directly on store state
  };

  const { data, error, isLoading, isValidating } = useSWR<ApiRefreshedAuthContext | null>(
    enabled ? API_CONTEXT_KEY : null,
    async () => {
      try {
        const context = await refreshAuthContext();
        return context || null;
      } catch (err) {
        console.error('[useServerContextRefresher] Error refreshing context:', err);
        throw err;
      }
    },
    {
      fallbackData: initialContext || structuredClone(currentStateData),
      revalidateOnMount: false,
      revalidateOnFocus: false,
      revalidateIfStale: false,
      dedupingInterval: 60000,
      errorRetryCount: 3,
      onSuccess: (newData) => {
        if (newData) {
          // Get current state to compare with new data
          const currentState = useAuthContextStore.getState();

          // Extract comparable properties from current state
          const currentStateData: ApiRefreshedAuthContext = {
            userId: currentState.userId,
            orgId: currentState.orgId,
            roleId: currentState.roleId,
            isUserActiveInOrg: currentState.isUserActiveInOrg,
            isOrgActive: currentState.isOrgActive,
            isSuperAdmin: currentState.isSuperAdmin,
            userEmail: currentState.userEmail,
            userFullName: currentState.userFullName,
            avatarUrl: currentState.avatarUrl,
            activeOrgName: currentState.activeOrgName,
            isActiveOrgDisabled: currentState.isActiveOrgDisabled
          };

          // Only update if data has actually changed
          if (JSON.stringify(newData) !== JSON.stringify(currentStateData)) {
            if (process.env.NODE_ENV === 'development') {
              console.debug('[useServerContextRefresher] SWR onSuccess: Updating store with refreshed context');
            }
            updateFullContext(newData);
          } else if (process.env.NODE_ENV === 'development') {
            console.debug('[useServerContextRefresher] SWR onSuccess: Skipping update, data unchanged');
          }
        }
      },
      onError: (err) => {
        console.error('[useServerContextRefresher] SWR onError:', err);

        // Handle authentication errors first
        try {
          handleAuthenticationError(err);
        } catch (e) {
          // If it's not a 401 error, continue with normal error handling
          setError(err.message || 'Failed to refresh server context');

          // Show toast for timeout errors
          if (err.message?.includes('timeout') || err.message?.includes('time-out') ||
              err.message?.includes('timed out') || err.message?.includes('network')) {
            toast({
              title: "Connection issue",
              description: "We're having trouble connecting. Some features may be unavailable.",
              variant: "destructive",
              duration: 5000
            });
          }
        }
      }
    }
  );

  // Update loading state
  useEffect(() => {
    setLoading(isLoading || isValidating);
  }, [isLoading, isValidating, setLoading]);

  // Handle errors
  useEffect(() => {
    if (error) {
      setError(error.message);
    }
  }, [error, setError]);

  // REMOVED: Visibility change listener for refreshing context
  // We no longer refresh context directly on visibility change in this hook
  // to avoid duplicate refreshes. DashboardEventManagerCore now handles this centrally.
  // This resolves the issue where context was refreshed multiple times when tab becomes visible.

  // Set up online/offline listeners
  useEffect(() => {
    if (!enabled) return;

    const handleOnline = () => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('[useServerContextRefresher] Network online, refreshing context');
      }
      refreshAuthContext().catch(err => {
        console.error('Error refreshing context on online event:', err);
      });
    };

    window.addEventListener('online', handleOnline);
    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, [enabled]);

  // Function to manually trigger a refresh
  const forceRefreshContext = async () => {
    try {
      setLoading(true);
      const context = await refreshAuthContext();
      return context;
    } catch (err) {
      console.error('[useServerContextRefresher] Error in forceRefreshContext:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    forceRefreshContext,
    isLoading: isLoading || isValidating,
    error,
    data: data || null
  };
}