## 3 — Move Optimistic Flag into Zustand
- [x] **3 a.** `organization-switcher.tsx` – replace every  
      `sessionStorage.setItem('organizationNavigating', …)`  
      with `setOptimisticNavigation(true|false)`.
- [x] **3 b.** `AuthContextProvider.tsx` – delete reads of  
      `organizationNavigating` & `lastOrgSwitchTimestamp`;  
      guard becomes:  
      ```ts
      if (!state.optimisticNavigation) hydrate(initialContext);
      ```
- [x] **3 c.** Remove helper constants / functions tied to those session keys and run `pnpm lint --fix`.

## 4 — Unify Event Pipeline
- [x] **4 a.** `eventBus/constants.ts` – keep **only**  
      `export const AUTH_CONTEXT_CHANGED = 'authContextChanged';`
- [x] **4 b.** All Supabase-realtime handlers **and** mutating API routes emit **just** `AUTH_CONTEXT_CHANGED`.
- [x] **4 c.** Delete legacy events & hooks (`useRoleEvents.ts`, `useStatusEvents.ts`, etc.).
- [x] **4 d.** Add `useAuthContextEvents()` hook:  
      ```ts
      export function useAuthContextEvents() {
        useBusEvent(
          AUTH_CONTEXT_CHANGED,
          throttle(refreshAuthContext, 150)
        );
        rbacCache.clear();          // also flush perms cache (task 6)
      }
      ```
- [x] **4 e.** Make sure `useAuthContextEvents` is mounted in DashboardProvider. Remove duplicate listeners elsewhere.

## 5 — Simplify Hydration Guard
- [x] **5 a.** In `AuthContextProvider.tsx` remove timestamp logic; only use `state.optimisticNavigation`.
- [x] **5 b.** Delete now-unused timestamp helpers.

## 6 — Instant RBAC Cache Flush
- [x] **6 a.** Ensure `rbacCache.clear()` is called inside `useAuthContextEvents()` (see 4 d).
- [x] **6 b.** Optionally drop the 60 s TTL check if cache is always cleared on change.

## 7 — Purge Residual MemoryCache Code
- [x] **7 a.** `grep -R "memoryCache" src/` → **no matches** (only a comment in auth-context.ts about memoryCache being removed).
- [x] **7 b.** `git rm src/lib/cache/memory-cache.ts` (file doesn't exist - already removed).
- [x] **7 c.** Remove commented-out `invalidateUserCache` imports in any file (no references found).

## 8 — SWR Refresher Sanity Pass
- [x] **8 a.** In `useServerContextRefresher.ts` wrap update call:  
      ```ts
      if (JSON.stringify(newCtx) !== JSON.stringify(storeCtx)) {
        updateFullContext(newCtx);
      }
      ```

## 9 — Remove Redundant Event Listeners
- [x] **9 a.** Remove the duplicate `AUTH_CONTEXT_CHANGED` listener in `OrganizationSwitcher.tsx`.
- [x] **9 b.** Remove the duplicate `AUTH_CONTEXT_CHANGED` listener in `DashboardScenarioManager.tsx`.
- [x] **9 c.** Keep legacy event handler functions but mark them with appropriate comments and eslint-disable directives.

## 10 — Prevent Duplicate Hook Mounts
- [x] **10 a.** Add global mount flag to `useAuthContextEvents` to prevent duplicate hook instances.
- [x] **10 b.** Reset the global mount flag when the hook unmounts.
- [x] **10 c.** Create `refreshAuthContextThrottled` to replace multiple raw `refreshAuthContext` calls.
- [x] **10 d.** Use the throttled refresh function in the auth context event handler.

---
