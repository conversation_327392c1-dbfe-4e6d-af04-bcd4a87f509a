'use client'

import { useState, useEffect } from 'react'
import { InvitationTable, InvitationTableBusinessRules, EmailInvitation } from '@/components/shared/invitation-table'
import { RoleId } from '@/lib/rbac/roles'
import { toast } from 'sonner'
import { fetchAllInvitationsWithProfiles, enrichInvitationsWithOrgNames } from '@/lib/invitations/invitation-utils'
import { useOrganizationsList } from '@/hooks/use-organizations-list'
import SendInvitationModal from './send-invitation-modal'
import { deleteEmailInvitation } from '@/app/actions/delete-email-invitation'
import { resendEmailInvitation } from '@/app/actions/resend-email-invitation'
import { useAuthContextStore } from '@/stores/useAuthContextStore'

interface DeveloperInvitationsClientProps {
  initialInvitations: EmailInvitation[]
}

export default function DeveloperInvitationsClient({ initialInvitations }: DeveloperInvitationsClientProps) {
  const [invitations, setInvitations] = useState<EmailInvitation[]>(initialInvitations)
  const [isLoading, setIsLoading] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  // Get current organization context for default selection
  const { orgId: currentOrgId } = useAuthContextStore()

  const [selectedOrgId, setSelectedOrgId] = useState<string>(currentOrgId || 'all') // Default to current org

  // Get organizations list for enriching invitation data
  const { organizations } = useOrganizationsList({})

  // Enrich initial invitations when organizations are loaded
  useEffect(() => {
    if (organizations && organizations.length > 0) {
      const enrichedInitial = enrichInvitationsWithOrgNames(initialInvitations, organizations)
      setInvitations(enrichedInitial)
    }
  }, [organizations, initialInvitations])

  // Update selectedOrgId when currentOrgId changes (for proper sync)
  useEffect(() => {
    if (currentOrgId && selectedOrgId !== currentOrgId) {
      setSelectedOrgId(currentOrgId)
    }
  }, [currentOrgId, selectedOrgId])

  // Define business rules for developer invitations
  const developerInviteBusinessRules: InvitationTableBusinessRules = {
    inviteMinRole: "supportAdmin", // supportAdmin+ can invite to any org
    deleteMinRole: "supportAdmin", // supportAdmin+ can delete invitations
    resendMinRole: "supportAdmin", // supportAdmin+ can resend failed invitations
    allowedRoles: ["orgClient", "orgAccounting", "orgMember", "orgAdmin"], // Can invite multiple roles
    maxAssignableRole: "orgAdmin", // Cannot assign superAdmin/supportAdmin roles
  }

  const refreshInvitations = async (orgIdFilter?: string) => {
    try {
      setIsLoading(true)

      // Apply organization filter if specified and not 'all'
      const filterOrgId = orgIdFilter || selectedOrgId

      const data = await fetchAllInvitationsWithProfiles(
        [RoleId.ORGCLIENT, RoleId.ORGACCOUNTING, RoleId.ORGMEMBER, RoleId.ORGADMIN],
        filterOrgId
      )

      // Enrich with organization names from the store
      const enrichedData = organizations ? enrichInvitationsWithOrgNames(data, organizations) : data
      setInvitations(enrichedData)
    } catch (error) {
      console.error('Error refreshing invitations:', error)
      toast.error('Failed to refresh invitations')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInviteUser = () => {
    setIsModalOpen(true)
  }

  const handleOrganizationChange = (orgId: string) => {
    setSelectedOrgId(orgId)
    refreshInvitations(orgId)
  }

  const handleDeleteInvitation = async (invitationId: string) => {
    try {
      const result = await deleteEmailInvitation(invitationId)

      if (result.success) {
        toast.success('Invitation deleted successfully')
        await refreshInvitations()
      } else {
        toast.error(result.error || 'Failed to delete invitation')
      }
    } catch (error) {
      console.error('Error deleting invitation:', error)
      toast.error('Failed to delete invitation')
    }
  }

  const handleResendInvitation = async (invitationId: string) => {
    try {
      const result = await resendEmailInvitation(invitationId)

      if (result.success) {
        toast.success('Invitation resent successfully')
        await refreshInvitations()
      } else {
        toast.error(result.error || 'Failed to resend invitation')
      }
    } catch (error) {
      console.error('Error resending invitation:', error)
      toast.error('Failed to resend invitation')
    }
  }

  return (
    <>
      <InvitationTable
        organizationIdScope="ALL_WITH_SELECTOR"
        businessRules={developerInviteBusinessRules}
        showOrganizationColumn={true} // Show organization column for cross-org view
        tableTitle="All User Invitations"
        invitations={invitations}
        isLoading={isLoading}
        onInviteUser={handleInviteUser}
        onDeleteInvitation={handleDeleteInvitation}
        onResendInvitation={handleResendInvitation}
        onOrganizationChange={handleOrganizationChange}
      />

      <SendInvitationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={refreshInvitations}
        allowedRoles={['orgClient', 'orgAccounting', 'orgMember', 'orgAdmin']}
        orgId={currentOrgId || undefined} // Default to current organization
        showOrgSelector={true}
      />
    </>
  )
}
