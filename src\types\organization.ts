export interface Organization {
  id: string
  name: string
  org_icon: string | null
  role?: string
  org_member_role?: number
  isActive: boolean
  isDefault?: boolean
  createdAt?: string
  updatedAt?: string
  org_member_is_active?: boolean
  is_current_context?: boolean // Added for dashboard layout
}

export interface OrganizationMember {
  userId: string
  organizationId: string
  role: string
  joinedAt: string
} 