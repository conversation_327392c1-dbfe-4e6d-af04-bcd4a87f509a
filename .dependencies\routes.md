# 🧭 Route Structure (App Router)

## Pages (`page.*`)
- `/` → `src/app/page.tsx`
- `/auth/login` → `src/app/auth/login/page.tsx`
- `/auth/signup` → `src/app/auth/signup/page.tsx`
- `/dashboard` → `src/app/dashboard/page.tsx`
- `/dashboard/account-disabled` → `src/app/dashboard/account-disabled/page.tsx`
- `/dashboard/admin` → `src/app/dashboard/admin/page.tsx`
- `/dashboard/admin/flows` → `src/app/dashboard/admin/flows/page.tsx`
- `/dashboard/admin/flowsteps` → `src/app/dashboard/admin/flowsteps/page.tsx`
- `/dashboard/admin/invites` → `src/app/dashboard/admin/invites/page.tsx`
- `/dashboard/admin/labels` → `src/app/dashboard/admin/labels/page.tsx`
- `/dashboard/admin/members` → `src/app/dashboard/admin/members/page.tsx`
- `/dashboard/admin/organization` → `src/app/dashboard/admin/organization/page.tsx`
- `/dashboard/admin/organization/[orgId]/invites` → `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx`
- `/dashboard/clients` → `src/app/dashboard/clients/page.tsx`
- `/dashboard/developer/create-organization` → `src/app/dashboard/developer/create-organization/page.tsx`
- `/dashboard/developer/organizations` → `src/app/dashboard/developer/organizations/page.tsx`
- `/dashboard/developer/test-event` → `src/app/dashboard/developer/test-event/page.tsx`
- `/dashboard/developer/users` → `src/app/dashboard/developer/users/page.tsx`
- `/dashboard/error` → `src/app/dashboard/error/page.tsx`
- `/dashboard/handbooks` → `src/app/dashboard/handbooks/page.tsx`
- `/dashboard/organization-disabled` → `src/app/dashboard/organization-disabled/page.tsx`
- `/dashboard/profile` → `src/app/dashboard/profile/page.tsx`
- `/dashboard/profile/billing` → `src/app/dashboard/profile/billing/page.tsx`
- `/dashboard/rbac-test` → `src/app/dashboard/rbac-test/page.tsx`
- `/dashboard/settings` → `src/app/dashboard/settings/page.tsx`

## API Routes (`route.*`)
- `/api/auth/context` → `src/app/api/auth/context/route.ts` (Methods: GET)
- `/api/auth/refresh-context` → `src/app/api/auth/refresh-context/route.ts` (Methods: POST)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\refresh-auth-context.ts` (Line 5): fetch call with method POST
- `/api/clear-cache` → `src/app/api/clear-cache/route.ts` (Methods: POST)
- `/api/organization-members` → `src/app/api/organization-members/route.ts` (Methods: GET)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 250): fetch call with method PATCH
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 266): fetch call with method PATCH
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 283): fetch call with method DELETE
- `/api/organization-members/remove` → `src/app/api/organization-members/remove/route.ts` (Methods: DELETE)
- `/api/organization-members/update-profile` → `src/app/api/organization-members/update-profile/route.ts` (Methods: PATCH)
- `/api/organization-members/update-role` → `src/app/api/organization-members/update-role/route.ts` (Methods: PATCH)
- `/api/organization-members/update-status` → `src/app/api/organization-members/update-status/route.ts` (Methods: PATCH)
- `/api/organizations/[id]` → `src/app/api/organizations/[id]/route.ts` (Methods: DELETE)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 115): fetch call with method GET
- `/api/organizations/active` → `src/app/api/organizations/active/route.ts` (Methods: GET)
- `/api/organizations/authorized` → `src/app/api/organizations/authorized/route.ts` (Methods: GET)
- `/api/organizations/complete` → `src/app/api/organizations/complete/route.ts` (Methods: GET)
- `/api/organizations/create` → `src/app/api/organizations/create/route.ts` (Methods: POST, OPTIONS)
- `/api/organizations/switch` → `src/app/api/organizations/switch/route.ts` (Methods: POST)
- `/api/user-profile/[userId]` → `src/app/api/user-profile/[userId]/route.ts` (Methods: PATCH)
- `/auth/callback` → `src/app/auth/callback/route.ts` (Methods: GET)

## Custom Hooks
- `useDashboard` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 12)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\Sidebar.tsx` (Line 25): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\user-avatar-menu.tsx` (Line 21): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 46): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\app\dashboard\admin\users\user-profile-dialog.tsx` (Line 48): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\app\dashboard\organization-disabled\page.tsx` (Line 8): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\app\dashboard\account-disabled\page.tsx` (Line 8): hook call
- `useOrganizationStorage` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-storage.ts` (Line 9)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\organization\organization-switcher.tsx` (Line 117): hook call
- `useOrganizationsList` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organizations-list.ts` (Line 93)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\Sidebar.tsx` (Line 27): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\dashboard-event-manager-core.tsx` (Line 101): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\organization\organization-switcher.tsx` (Line 79): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\client-dashboard-shell\index.tsx` (Line 26): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\shared\member-table.tsx` (Line 201): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-rbac-permission.ts` (Line 33): hook call
- `useOrganizationMembersEventBus` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 39)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\shared\member-table.tsx` (Line 286): hook call
- `useOrganizationContext` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-context.ts` (Line 36)
- `useAuth` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-auth.ts` (Line 7)
- `useBusEvent` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\useBusEvent.ts` (Line 6)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 58): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 67): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 76): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 87): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 294): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 298): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-members-event-bus.ts` (Line 302): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useContextEvents.ts` (Line 13): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useOrganizationEvents.ts` (Line 15): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useOrganizationEvents.ts` (Line 33): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useRoleEvents.ts` (Line 13): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useStatusEvents.ts` (Line 13): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useNameEvents.ts` (Line 13): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useAllMemberChanges.ts` (Line 13): hook call
- `useServerContextRefresher` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts` (Line 23)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\dashboard-scenario-manager.tsx` (Line 41): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\dashboard-event-manager-core.tsx` (Line 100): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\client-dashboard-shell\index.tsx` (Line 23): hook call
- `useRealtimeSubscription` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-realtime-subscription.ts` (Line 18)
- `useRbacPermission` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-rbac-permission.ts` (Line 26)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\MenuItem.tsx` (Line 13): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\Sidebar.tsx` (Line 30): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\shared\member-table.tsx` (Line 202): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\rbac\restricted.tsx` (Line 39): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\app\dashboard\developer\organizations\organization-table.tsx` (Line 195): hook call
- `useOrganizationContextEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useContextEvents.ts` (Line 9)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\developer\test-event-system.tsx` (Line 84): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\providers\organization-check-provider.tsx` (Line 100): hook call
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-organization-context.ts` (Line 143): hook call
- `useOrganizationEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useOrganizationEvents.ts` (Line 11)
- `useAllOrganizationEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useOrganizationEvents.ts` (Line 30)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\app\dashboard\developer\organizations\organization-table.tsx` (Line 288): hook call
- `useUserRoleEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useRoleEvents.ts` (Line 9)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\developer\test-event-system.tsx` (Line 85): hook call
- `useMemberStatusEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useStatusEvents.ts` (Line 9)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\developer\test-event-system.tsx` (Line 86): hook call
- `useOrgNameEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useNameEvents.ts` (Line 9)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\developer\test-event-system.tsx` (Line 87): hook call
- `useAuthEventEmitters` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useAuthEvents.ts` (Line 11)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 139): hook call
- `useAllMemberChanges` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\hooks\useAllMemberChanges.ts` (Line 9)
- `useAuthContextEvents` → `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-auth-context-events.ts` (Line 75)
  - **Used in:**
    - `d:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\providers\dashboard-provider.tsx` (Line 135): hook call


*Note: Route paths are derived from the directory structure within `app/` or `src/app/`, ignoring route groups `(...)`.*
*Usage information is detected from fetch calls, form actions, and hook calls throughout the codebase.*
