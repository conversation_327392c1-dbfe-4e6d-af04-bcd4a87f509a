"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Restricted } from "@/components/rbac/restricted"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

export default function RbacTestPage() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>RBAC Testing Dashboard</CardTitle>
          <CardDescription>
            This page demonstrates the RBAC system capabilities. You can use this to test
            permission functionality across different user roles.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Basic Read Operation */}
          <section>
            <h3 className="text-lg font-semibold mb-4">Read Operation (Standalone)</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">By Role Level</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                  <Restricted rMinRole="orgClient">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgClient+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rMinRole="orgMember">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgMember+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rMinRole="orgAccounting">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAccounting+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rMinRole="orgAdmin">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAdmin+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rMinRole="supportAdmin">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">supportAdmin+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rMinRole="superAdmin">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">superAdmin+</Badge>
                    </Button>
                  </Restricted>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">By Specific Roles: Only shows the buttons that the user has access to</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                  <Restricted rRoles={["orgClient"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgClient</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rRoles={["orgMember"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgMember</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rRoles={["orgAccounting"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAccounting</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rRoles={["orgAdmin"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAdmin</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rRoles={["supportAdmin"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">supportAdmin</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted rRoles={["superAdmin"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">superAdmin</Badge>
                    </Button>
                  </Restricted>
                </div>
              </div>
            </div>
          </section>
          
          <Separator />
          
          {/* Dual Operations */}
          <section>
            <h3 className="text-lg font-semibold mb-4">Dual Operations (with Read)</h3>
            <div className="space-y-6">
              {/* Create + Read */}
              <div className="space-y-4">
                <h4 className="font-medium">Create + Read</h4>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Role Level</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <Restricted crMinRole="orgClient">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgClient+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crMinRole="orgMember">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crMinRole="orgAccounting">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAccounting+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crMinRole="orgAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crMinRole="supportAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crMinRole="superAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin+</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Specific Roles: Only shows the buttons that the user has access to</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <Restricted crRoles={["orgMember", "orgAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember, orgAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crRoles={["orgAdmin", "supportAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin, supportAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crRoles={["superAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
              </div>
              
              {/* Read + Update */}
              <div className="space-y-4">
                <h4 className="font-medium">Read + Update</h4>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Role Level</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <Restricted ruMinRole="orgClient">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgClient+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruMinRole="orgMember">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruMinRole="orgAccounting">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAccounting+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruMinRole="orgAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruMinRole="supportAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruMinRole="superAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin+</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Specific Roles: Only shows the buttons that the user has access to</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <Restricted ruRoles={["orgMember", "orgAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember, orgAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruRoles={["orgAdmin", "supportAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin, supportAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted ruRoles={["superAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
              </div>
              
              {/* Read + Delete */}
              <div className="space-y-4">
                <h4 className="font-medium">Read + Delete</h4>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Role Level</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <Restricted rdMinRole="orgClient">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgClient+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdMinRole="orgMember">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdMinRole="orgAccounting">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAccounting+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdMinRole="orgAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdMinRole="supportAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdMinRole="superAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin+</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Specific Roles</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <Restricted rdRoles={["orgAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdRoles={["supportAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rdRoles={["superAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          <Separator />
          
          {/* Triple Operations */}
          <section>
            <h3 className="text-lg font-semibold mb-4">Triple Operations (with Read)</h3>
            <div className="space-y-6">
              {/* Create + Read + Update */}
              <div className="space-y-4">
                <h4 className="font-medium">Create + Read + Update</h4>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Role Level</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <Restricted cruMinRole="orgClient">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgClient+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruMinRole="orgMember">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruMinRole="orgAccounting">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAccounting+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruMinRole="orgAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruMinRole="supportAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruMinRole="superAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin+</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Specific Roles: Only shows the buttons that the user has access to</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <Restricted cruRoles={["orgMember", "orgAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember, orgAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruRoles={["orgAdmin", "supportAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin, supportAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted cruRoles={["superAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
              </div>
              
              {/* Create + Read + Delete */}
              <div className="space-y-4">
                <h4 className="font-medium">Create + Read + Delete</h4>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Role Level</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <Restricted crdMinRole="orgClient">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgClient+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdMinRole="orgMember">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdMinRole="orgAccounting">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAccounting+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdMinRole="orgAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdMinRole="supportAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdMinRole="superAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin+</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Specific Roles: Only shows the buttons that the user has access to</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <Restricted crdRoles={["orgAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdRoles={["supportAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted crdRoles={["superAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
              </div>
              
              {/* Read + Update + Delete */}
              <div className="space-y-4">
                <h4 className="font-medium">Read + Update + Delete</h4>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Role Level</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                    <Restricted rudMinRole="orgClient">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgClient+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudMinRole="orgMember">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgMember+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudMinRole="orgAccounting">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAccounting+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudMinRole="orgAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudMinRole="supportAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin+</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudMinRole="superAdmin">
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin+</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm text-muted-foreground mb-2">By Specific Roles: Only shows the buttons that the user has access to</h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <Restricted rudRoles={["orgAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">orgAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudRoles={["supportAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">supportAdmin</Badge>
                      </Button>
                    </Restricted>
                    
                    <Restricted rudRoles={["superAdmin"]}>
                      <Button variant="outline" className="justify-center">
                        <Badge variant="outline">superAdmin</Badge>
                      </Button>
                    </Restricted>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          <Separator />
          
          {/* Full CRUD Access */}
          <section>
            <h3 className="text-lg font-semibold mb-4">Full CRUD Access</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">By Role Level</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                  <Restricted crudMinRole="orgClient">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgClient+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudMinRole="orgMember">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgMember+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudMinRole="orgAccounting">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAccounting+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudMinRole="orgAdmin">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAdmin+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudMinRole="supportAdmin">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">supportAdmin+</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudMinRole="superAdmin">
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">superAdmin+</Badge>
                    </Button>
                  </Restricted>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">By Specific Roles: Only shows the buttons that the user has access to</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Restricted crudRoles={["orgAdmin"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">orgAdmin</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudRoles={["supportAdmin"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">supportAdmin</Badge>
                    </Button>
                  </Restricted>
                  
                  <Restricted crudRoles={["superAdmin"]}>
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">superAdmin</Badge>
                    </Button>
                  </Restricted>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">With Fallback Content: Shows greyed out buttons that the user does not have access to</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <Restricted 
                    crudMinRole="superAdmin" 
                    fallback={
                      <Button variant="outline" className="justify-center" disabled>
                        <Badge variant="outline">Requires superAdmin</Badge>
                      </Button>
                    }
                  >
                    <Button variant="outline" className="justify-center">
                      <Badge variant="outline">superAdmin</Badge>
                    </Button>
                  </Restricted>
                </div>
              </div>
            </div>
          </section>
          
          {/* RBAC Design Info */}
          <div className="mt-8 p-4 bg-muted rounded-lg space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Role Hierarchy (from highest to lowest):</h4>
              <ol className="list-decimal list-inside space-y-1">
                <li>Superadmin (ID: 1) - Full system access</li>
                <li>Support Admin (ID: 2) - Can manage all organizations</li>
                <li>Organization Admin (ID: 3) - Can manage their organization</li>
                <li>Organization Member (ID: 4) - Basic member access</li>
                <li>Organization Accounting (ID: 5) - View-only access</li>
                <li>Organization Client (ID: 6) - Most restricted access</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">RBAC Design Notes:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Read (r) is the only standalone operation</li>
                <li>Create (c), Update (u), and Delete (d) are always combined with Read</li>
                <li>Operation combinations follow logical access patterns (e.g., cr, ru, rd)</li>
                <li>MinRole props follow the role hierarchy (higher roles inherit lower roles&apos; permissions)</li>
                <li>Roles props provide explicit access to specific roles only</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 