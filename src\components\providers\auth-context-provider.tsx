'use client';

import { useEffect } from 'react';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import type { ApiRefreshedAuthContext } from '@/hooks/use-server-context-refresher';

export function AuthContextProvider({
  initialContext,
  children,
}: {
  initialContext: ApiRefreshedAuthContext;
  children: React.ReactNode;
}) {
  const hydrate = useAuthContextStore((state) => state.updateFullContext);

  // Hydrate the store with server-side data on mount
  useEffect(() => {
    const storeState = useAuthContextStore.getState();
    
    // Check if we're in an optimistic navigation or update process
    if (storeState.optimisticNavigation || storeState.optimisticLoading) {
      return; // Skip hydration during optimistic updates to prevent flicker/override
    }

    // Check if this is a redundant hydration (same org context)
    if (storeState.orgId === initialContext?.orgId &&
        storeState.roleId === initialContext?.roleId) {
      return; // Skip redundant hydration
    }

    // Normal hydration flow - reduced logging
    if (process.env.NODE_ENV === 'development' &&
        !sessionStorage.getItem('currentOrgSwitchId')) {
      console.debug('[AuthContextProvider] Hydrating store with initial context:', initialContext);
    }
    
    // Only update if we have actual data to hydrate with
    if (initialContext) {
      hydrate(initialContext);
    }
  }, [hydrate, initialContext]);

  return <>{children}</>;
}
