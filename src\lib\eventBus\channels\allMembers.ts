// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import type { OrganizationMemberInsertedEvent, OrganizationMemberUpdatedEvent, OrganizationMemberDeletedEvent, AnyMemberChangeEvent } from '@/lib/eventTypes'

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organization_members:* events and emit organization:member:changed events as appropriate.
 */
export function registerAllMembersInterpreter() {
  if (isRegistered) {
    // console.log("[AllMembersInterpreter] Already registered, skipping.");
    return
  }
  // REMOVE OR COMMENT OUT THE LINE BELOW:
  // console.log("[AllMembersInterpreter] Registered (stub)", typeof window !== "undefined" ? "in browser" : "on server");
  isRegistered = true
  
  // Handle INSERT
  emitter.on('db:organization_members:inserted', (event: OrganizationMemberInsertedEvent) => {
    const { orgId, userId, data, timestamp } = event
    if (!orgId || !userId) return
    const memberEvent: AnyMemberChangeEvent = {
      eventType: 'INSERT',
      orgId,
      userId,
      data,
      timestamp,
    }
    clientLog(`[EventBus] Member inserted in org ${orgId}: user ${userId}`)
    emitter.emit('organization:member:changed', memberEvent)
  })

  // Handle UPDATE (only emit if something actually changed)
  emitter.on('db:organization_members:updated', (event: OrganizationMemberUpdatedEvent) => {
    const { orgId, userId, data, timestamp } = event
    if (!orgId || !userId) return
    // Only emit if something actually changed (role, status, is_default_org, etc.)
    if (
      typeof data.role === 'number' && typeof data.old_role === 'number' && data.role !== data.old_role ||
      typeof data.is_active === 'boolean' && typeof data.old_is_active === 'boolean' && data.is_active !== data.old_is_active ||
      typeof data.is_default_org === 'boolean' && typeof data.old_is_default_org === 'boolean' && data.is_default_org !== data.old_is_default_org
    ) {
      const memberEvent: AnyMemberChangeEvent = {
        eventType: 'UPDATE',
        orgId,
        userId,
        data,
        timestamp,
      }
      clientLog(`[EventBus] Member updated in org ${orgId}: user ${userId}`)
      emitter.emit('organization:member:changed', memberEvent)
    }
  })

  // Handle DELETE
  emitter.on('db:organization_members:deleted', (event: OrganizationMemberDeletedEvent) => {
    const { orgId, userId, data, timestamp } = event
    if (!orgId || !userId) return
    const memberEvent: AnyMemberChangeEvent = {
      eventType: 'DELETE',
      orgId,
      userId,
      data,
      timestamp,
    }
    clientLog(`[EventBus] Member deleted in org ${orgId}: user ${userId}`)
    emitter.emit('organization:member:changed', memberEvent)
  })
} 