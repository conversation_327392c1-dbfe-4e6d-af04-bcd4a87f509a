// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter } from '@/lib/eventBus'
import { clientLog } from '@/lib/eventBus/emitter'
import type { OrganizationMemberInsertedEvent, OrganizationMemberUpdatedEvent, MemberStatusChangedEvent, AuthEvent } from '@/lib/eventTypes'
import { AUTH_CONTEXT_CHANGED } from '../constants'

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organization_members:* events and emit member:status:changed events as appropriate.
 */
export function registerStatusInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    // console.log('[StatusInterpreter] Already registered, skipping duplicate registration')
    return
  }
  
  isRegistered = true
  
  // Handle INSERT (new member added)
  emitter.on('db:organization_members:inserted', (event: OrganizationMemberInsertedEvent) => {
    const { orgId, userId, data, timestamp } = event
    const isActive = data.org_member_is_active
    if (!orgId || !userId || typeof isActive !== 'boolean') return
    const statusEvent: MemberStatusChangedEvent = {
      userId,
      orgId,
      isActive,
      timestamp,
      eventType: 'INSERT',
    }
    clientLog(`[EventBus] Member status changed for user ${userId} in org ${orgId} to ${isActive ? 'active' : 'inactive'}`)
    emitter.emit('member:status:changed', statusEvent)
    
    // Also emit unified AUTH_CONTEXT_CHANGED event
    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'MEMBER_STATUS_CHANGE',
      data: {
        isActive
      }
    }
    console.log('[StatusInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  })

  // Handle UPDATE (status changed)
  emitter.on('db:organization_members:updated', (event: OrganizationMemberUpdatedEvent) => {
    const { orgId, userId, data, timestamp } = event
    const isActive = data.org_member_is_active
    
    console.log('[StatusInterpreter] db:organization_members:updated handler fired:', { orgId, userId, isActive, data })

    // Determine old value safely, with a series of fallbacks
    let oldIsActive: boolean | undefined;
    
    // Try direct property from enhanced payload
    if (typeof data.old_org_member_is_active === 'boolean') {
      oldIsActive = data.old_org_member_is_active;
    } 
    // Try to extract from the old record if it's present
    else if (data.old) {
      const oldData = data.old as Record<string, unknown>;
      if (typeof oldData['org_member_is_active'] === 'boolean') {
        oldIsActive = oldData['org_member_is_active'] as boolean;
      }
    }
    
    // If we still don't have an old value, use current as fallback
    if (oldIsActive === undefined && typeof isActive === 'boolean') {
      oldIsActive = isActive;
    }
    
    console.log('[StatusInterpreter] Status values:', { isActive, oldIsActive });
    
    if (!orgId || !userId || typeof isActive !== 'boolean' || typeof oldIsActive !== 'boolean') {
      console.warn('[StatusInterpreter] Missing required data for status change event:', { 
        hasOrgId: !!orgId, 
        hasUserId: !!userId, 
        isActiveType: typeof isActive, 
        oldIsActiveType: typeof oldIsActive 
      })
      return
    }
    
    if (isActive === oldIsActive) {
      console.log('[StatusInterpreter] Status did not change, skipping event emission')
      return
    }
    
    const statusEvent: MemberStatusChangedEvent = {
      userId,
      orgId,
      isActive,
      timestamp,
      eventType: 'UPDATE',
    }
    
    console.log(`[StatusInterpreter] Emitting member:status:changed event: User ${userId} in org ${orgId} is now ${isActive ? 'active' : 'inactive'}`)
    clientLog(`[EventBus] Member status changed for user ${userId} in org ${orgId} to ${isActive ? 'active' : 'inactive'}`)
    emitter.emit('member:status:changed', statusEvent)
    
    // Also emit unified AUTH_CONTEXT_CHANGED event
    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'MEMBER_STATUS_CHANGE',
      data: {
        isActive,
        oldIsActive
      }
    }
    console.log('[StatusInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  })

  // Handle DELETE (member removed)
  emitter.on('db:organization_members:deleted', () => {
    // Optionally emit a status change event for cleanup if needed
  })
}
