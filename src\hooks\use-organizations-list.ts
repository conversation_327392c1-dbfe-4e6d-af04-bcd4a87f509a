import { useCallback, useState, useMemo } from 'react';
import useS<PERSON> from 'swr';
import { Organization } from '@/types/organization';
import { handleAuthenticationError } from '@/lib/auth-error-handler';

export interface PaginatedOrganizationsResponse {
  organizations: Organization[];
  totalCount: number;
  page: number;
  pageSize: number;
}

const API_ORGANIZATIONS_KEY = '/api/organizations/authorized';

// Fetch function with simple retries and error handling
async function fetcher(url: string): Promise<Organization[] | PaginatedOrganizationsResponse | null> {
  const MAX_ATTEMPTS = 3;
  const RETRY_DELAY = 500;

  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.debug(`[useOrganizationsList] Fetcher attempt ${attempt}/${MAX_ATTEMPTS}...`);
      }

      // Parse URL to determine if pagination params are included
      const urlObj = new URL(url, window.location.origin);
      const isPaginated = urlObj.searchParams.has('page') || urlObj.searchParams.has('pageSize');

      const response = await fetch(url, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        cache: 'no-store',
        next: { revalidate: 0, tags: ['organizations-list'] },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[useOrganizationsList] Fetch error: ${response.status}, Body: ${errorText}`);
        const error = new Error(`API error: ${response.status} ${errorText || response.statusText}`);
        handleAuthenticationError(error, response.status);
        throw error; // This line won't be reached if it's a 401, but satisfies TypeScript
      }

      const data = await response.json();

      if (!data) {
        console.warn('[useOrganizationsList] API response OK, but body was empty or invalid.');

        if (attempt < MAX_ATTEMPTS) {
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          continue;
        }

        return null;
      }

      // Check if the response is paginated or a direct array
      if (isPaginated && 'organizations' in data && 'totalCount' in data) {
        return data as PaginatedOrganizationsResponse;
      } else {
        return data as Organization[];
      }

    } catch (error) {
      console.error(`[useOrganizationsList] Fetch operation failed on attempt ${attempt}:`, error);

      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt < MAX_ATTEMPTS) {
        const backoffDelay = RETRY_DELAY * Math.pow(2, attempt-1);

        if (process.env.NODE_ENV === 'development') {
          console.debug(`[useOrganizationsList] Retrying in ${backoffDelay}ms`);
        }
        await new Promise(resolve => setTimeout(resolve, backoffDelay));
      } else {
        throw lastError;
      }
    }
  }

  if (lastError) throw lastError;
  return null;
}

interface UseOrganizationsListOptions {
  page?: number;
  pageSize?: number;
  initialData?: Organization[];
  usePagination?: boolean;
}

export function useOrganizationsList(options: UseOrganizationsListOptions = {}) {
  const { initialData, page = 1, pageSize = 10 } = options;

  // Keep local state for pagination
  const [currentPage, setCurrentPage] = useState(page);
  const [currentPageSize, setCurrentPageSize] = useState(pageSize);

  // Construct the API URL based on pagination settings
  const apiUrl = options.usePagination
    ? `${API_ORGANIZATIONS_KEY}?page=${currentPage}&pageSize=${currentPageSize}`
    : API_ORGANIZATIONS_KEY;

  // Use SWR with revalidateOnFocus for automatic updates when tab becomes active
  const {
    data: fetchedData,
    error,
    isLoading,
    isValidating,
    mutate
  } = useSWR<Organization[] | PaginatedOrganizationsResponse | null, Error>(
    apiUrl,
    fetcher,
    {
      fallbackData: initialData as Organization[] | PaginatedOrganizationsResponse | null,
      revalidateOnMount: false,      // Don't refresh on mount (use initialData)
      revalidateOnFocus: false,     // Prevent organization list from being refreshed on tab focus.
      revalidateIfStale: false,      // Don't revalidate stale data automatically
      dedupingInterval: 60000,       // Deduplicate requests within 1 minute
      errorRetryCount: 3,            // Retry 3 times on error
      onError: (error) => {
        console.error('[useOrganizationsList] SWR error:', error);
        // Handle authentication errors
        try {
          handleAuthenticationError(error);
        } catch (e) {
          // If it's not a 401 error, the handleAuthenticationError will throw
          // No need to restore from localStorage, the Zustand store will handle persistence
        }
      }
    }
  );

  // Extract the organizations from the response using useMemo to avoid new references on each render
  const organizations = useMemo(() => {
    if (Array.isArray(fetchedData)) {
      return fetchedData;
    } else if (fetchedData?.organizations) {
      return fetchedData.organizations;
    } else {
      return [];
    }
  }, [fetchedData]);

  // Extract pagination info if available
  const totalCount = !Array.isArray(fetchedData) && fetchedData ? fetchedData.totalCount : organizations.length;
  const totalPages = Math.ceil(totalCount / currentPageSize);

  // Handle pagination changes
  const goToPage = useCallback((newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
  }, [totalPages]);

  const setPageSize = useCallback((newPageSize: number) => {
    if (newPageSize < 1) return;
    setCurrentPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  }, []);

  // Force refresh function (simplified)
  const refreshOrganizations = useCallback(async (): Promise<Organization[]> => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[useOrganizationsList] Manual refresh triggered');
    }

    try {
      // Clear cache and revalidate
      const refreshedData = await mutate();

      if (!refreshedData) {
        return [];
      }

      return Array.isArray(refreshedData)
        ? refreshedData
        : refreshedData.organizations ?? [];
    } catch (error) {
      console.error('[useOrganizationsList] Error refreshing organizations:', error);
      return [];
    }
  }, [mutate]);

  return {
    organizations,
    isLoading,
    isValidating,
    error,
    refreshOrganizations,

    // Pagination related fields and functions
    totalCount,
    page: currentPage,
    pageSize: currentPageSize,
    totalPages,
    goToPage,
    setPageSize,
  };
}