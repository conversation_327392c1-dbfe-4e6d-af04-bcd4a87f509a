// src/lib/eventBus/channels/manager.ts

import type { SupabaseClient, RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import { emitter } from '@/lib/eventBus/emitter'

// Define only for internal type checks
interface OrganizationMember {
  org_id: string
  user_id: string
  is_current_context?: boolean
  org_member_is_active?: boolean
  org_member_role?: string | number
  is_default_org?: boolean
  [key: string]: unknown
}

interface Organization {
  id: string
  org_name?: string
  is_active?: boolean
  [key: string]: unknown
}

type OrgMemberPayload = RealtimePostgresChangesPayload<OrganizationMember>
type OrgPayload = RealtimePostgresChangesPayload<Organization>

// ---- Channel Registry Setup ----

type ChannelRegistryEntry = {
  channel: RealtimeChannel
  refCount: number
}

const channelRegistry: Record<string, ChannelRegistryEntry> = {}

// Fields in organization_members that, if they are the *only* ones changed,
// should not trigger a full event emission or verbose logging.
// NOTE: is_current_context removed from ignorable fields to enable cross-window synchronization
const IGNORABLE_ORG_MEMBER_FIELDS: ReadonlyArray<string> = ['is_default_org'];

function log(...args: unknown[]) {
  if (process.env.NODE_ENV === 'development') {
    console.log('[ChannelManager]', ...args)
  }
}

// ---- Utility: Type Guards ----
function isOrgMember(obj: unknown): obj is OrganizationMember {
  return typeof obj === 'object' && obj !== null && 'org_id' in obj && 'user_id' in obj
}
function isOrg(obj: unknown): obj is Organization {
  return typeof obj === 'object' && obj !== null && 'id' in obj
}

// ---- Helper to determine if an org member update should be skipped ----
// Returns true if the update involves only changes to ignorable fields.
function shouldSkipOrgMemberUpdate(
  newRecord: Partial<OrganizationMember>, // Use Partial for broader compatibility
  oldRecord: Partial<OrganizationMember> | undefined, // Use Partial
  ignorableFields: ReadonlyArray<string>
): boolean {
  if (!oldRecord) return false; // Cannot determine if only ignorable changed without old record, so don't skip

  let nonIgnorableFieldDidChange = false;
  // Consider all keys present in either new or old record
  const allKeys = new Set([...Object.keys(newRecord), ...Object.keys(oldRecord)]) as Set<string>; // Keys are strings

  for (const key of allKeys) {
    if (!ignorableFields.includes(key)) { // key is string, ignorableFields is string[]
      if (newRecord[key as keyof OrganizationMember] !== oldRecord[key as keyof OrganizationMember]) {
        nonIgnorableFieldDidChange = true;
        break;
      }
    }
  }

  if (nonIgnorableFieldDidChange) {
    return false; // A non-ignorable field changed, so DO NOT skip
  }

  let anIgnorableFieldActuallyChanged = false;
  for (const key of ignorableFields) { // key here is from ignorableFields (string)
    if (newRecord[key as keyof OrganizationMember] !== oldRecord[key as keyof OrganizationMember]) {
      anIgnorableFieldActuallyChanged = true;
      break;
    }
  }
  return anIgnorableFieldActuallyChanged;
}


// ---- Event Mapping ----

function emitOrgMemberInserted(payload: OrgMemberPayload) {
  const newMember = payload.new
  if (!isOrgMember(newMember)) return
  emitter.emit('db:organization_members:inserted', {
    orgId: newMember.org_id,
    userId: newMember.user_id,
    data: newMember,
    timestamp: Date.now(),
  })
}

// Transaction grouping for organization context changes
const contextChangeGroups = new Map<string, {
  events: any[],
  timer: ReturnType<typeof setTimeout>
}>();

function emitOrgMemberUpdated(payload: OrgMemberPayload) {
  const newMember = payload.new as OrganizationMember // Cast since it passed the filter
  const oldMember = payload.old as OrganizationMember | undefined // oldMember should exist if it passed the filter

  // The more complex filtering is now done before this function is called.
  // We can assume if this function is reached, the event is significant.
  if (!isOrgMember(newMember)) return // Basic guard

  // Check if this is a context change event (organization switching)
  const isContextChange = newMember.is_current_context !== oldMember?.is_current_context;

  if (isContextChange) {
    // Group context changes by user to handle organization switching as a single operation
    const groupKey = `context_${newMember.user_id}`;
    const eventData = {
      orgId: newMember.org_id,
      userId: newMember.user_id,
      data: {
        ...newMember,
        old_org_member_role: oldMember?.org_member_role,
        old_org_member_is_active: oldMember?.org_member_is_active,
        old_is_current_context: oldMember?.is_current_context,
        old_is_default_org: oldMember?.is_default_org,
      },
      timestamp: Date.now(),
    };

    // Get or create group
    let group = contextChangeGroups.get(groupKey);
    if (!group) {
      group = { events: [], timer: null as any };
      contextChangeGroups.set(groupKey, group);
    }

    // Add event to group
    group.events.push(eventData);

    // Clear existing timer and set new one
    if (group.timer) clearTimeout(group.timer);

    group.timer = setTimeout(() => {
      // Process grouped events
      const events = group!.events;
      contextChangeGroups.delete(groupKey);

      // Emit only the final state event (the one with is_current_context = true)
      const finalEvent = events.find(e => e.data.is_current_context === true);
      if (finalEvent) {
        log(`[ChannelManager] Emitting grouped context change for user ${finalEvent.userId} to org ${finalEvent.orgId}`);
        emitter.emit('db:organization_members:updated', finalEvent);
      }
    }, 50); // 50ms grouping window

    return; // Don't emit immediately for context changes
  }

  // For non-context changes, emit immediately
  emitter.emit('db:organization_members:updated', {
    orgId: newMember.org_id,
    userId: newMember.user_id,
    data: {
      ...newMember,
      old_org_member_role: oldMember?.org_member_role,
      old_org_member_is_active: oldMember?.org_member_is_active,
      old_is_current_context: oldMember?.is_current_context,
      old_is_default_org: oldMember?.is_default_org,
    },
    timestamp: Date.now(),
  })
}

function emitOrgMemberDeleted(payload: OrgMemberPayload) {
  const oldMember = payload.old
  if (!isOrgMember(oldMember)) return
  emitter.emit('db:organization_members:deleted', {
    orgId: oldMember.org_id,
    userId: oldMember.user_id,
    data: oldMember,
    timestamp: Date.now(),
  })
}

function emitOrgDataChanged(payload: OrgPayload) {
  const newOrg = payload.new
  if (!isOrg(newOrg)) return
  emitter.emit('db:organizations:updated', {
    orgId: newOrg.id,
    changes: newOrg,
    oldRecord: payload.old,
    timestamp: Date.now(),
  })
}

// ---- Channel Config ----

// We need to type the handler per channel to ensure correct dispatch
const CHANNELS = [
  {
    key: 'organization_members',
    table: 'organization_members',
    schema: 'public',
    events: ['INSERT', 'UPDATE', 'DELETE'] as const,
    handler: (event: 'INSERT' | 'UPDATE' | 'DELETE', payload: OrgMemberPayload) => {
      if (event === 'INSERT') emitOrgMemberInserted(payload)
      else if (event === 'UPDATE') emitOrgMemberUpdated(payload) // emitOrgMemberUpdated no longer filters
      else if (event === 'DELETE') emitOrgMemberDeleted(payload)
    },
  },
  {
    key: 'organizations',
    table: 'organizations',
    schema: 'public',
    events: ['UPDATE'] as const,
    handler: (_event: 'UPDATE', payload: OrgPayload) => {
      emitOrgDataChanged(payload)
    },
  },
] as const

type ChannelKey = typeof CHANNELS[number]['key']

// ---- Main API ----

// Debug variable to track how many times we've attempted to subscribe
let subscriptionAttempts = 0;

export function subscribeToDashboardChannels(supabase: SupabaseClient) {
  subscriptionAttempts++;
  log(`Subscription attempt #${subscriptionAttempts}`)

  // First check if we already have active channels
  const existingChannelCount = Object.keys(channelRegistry).length;
  if (existingChannelCount > 0) {
    log(`INFO: Called subscribeToDashboardChannels but we already have ${existingChannelCount} active channels.`)

    // If channels are broken, we should clean up and resubscribe
    if (areChannelsBroken()) {
      log('Found broken channels - unsubscribing all before resubscribing')
      unsubscribeAllDashboardChannels()
    } else {
      // If channels are healthy, just increment refCount and return
      Object.keys(channelRegistry).forEach(key => {
        channelRegistry[key].refCount++
      })
      log('All channels appear healthy, incremented refCount and skipping new subscriptions')
      // Update the global flag to reflect current state
      if (typeof window !== 'undefined') {
        (window as any).__hasSubscribedDashboardChannels = true;
      }
      return;
    }
  }

  CHANNELS.forEach(config => {
    if (channelRegistry[config.key]) {
      channelRegistry[config.key].refCount++
      log(`[${config.key}] Already subscribed, refCount is now`, channelRegistry[config.key].refCount)
      return
    }

    const channel: RealtimeChannel = supabase.channel(`dashboard:${config.key}`)

    config.events.forEach(eventType => {
      channel.on(
        // @ts-expect-error: "postgres_changes" is runtime-supported but not typed by Supabase
        'postgres_changes',
        {
          event: eventType,
          schema: config.schema,
          table: config.table,
        },
        (payload: unknown) => {
          // Pre-filter for organization_members UPDATE events
          if (config.key === 'organization_members' && eventType === 'UPDATE') {
            const orgMemberPayload = payload as OrgMemberPayload;
            // Ensure new and old are treated as partial for the check function
            if (shouldSkipOrgMemberUpdate(
                orgMemberPayload.new as Partial<OrganizationMember>,
                orgMemberPayload.old as Partial<OrganizationMember> | undefined,
                IGNORABLE_ORG_MEMBER_FIELDS
              )
            ) {
              // Safely access properties for logging, ensuring they exist
              // const newUserId = (orgMemberPayload.new as OrganizationMember)?.user_id ?? 'unknown_user';
              // const newOrgId = (orgMemberPayload.new as OrganizationMember)?.org_id ?? 'unknown_org';
              // log(`[${config.key}] ${eventType} event for ignorable fields on user ${newUserId} in org ${newOrgId}, skipping verbose log & handler.`);
              return; // Skip verbose logging and handler for this specific update
            }
          }

          // Only log non-context change events to reduce verbosity
          const isContextChangeEvent = config.key === 'organization_members' &&
                                      eventType === 'UPDATE' &&
                                      isOrgMember((payload as OrgMemberPayload).new) &&
                                      ((payload as OrgMemberPayload).new as OrganizationMember).is_current_context !==
                                      ((payload as OrgMemberPayload).old as OrganizationMember | undefined)?.is_current_context;

          if (!isContextChangeEvent) {
            log(`[${config.key}] ${eventType} event`, payload);
          } else {
            log(`[${config.key}] ${eventType} context change event (will be grouped)`);
          }

          if (config.key === 'organization_members') {
            // The handler expects OrgMemberPayload
            (config.handler as (event: 'INSERT' | 'UPDATE' | 'DELETE', payload: OrgMemberPayload) => void)(
              eventType as 'INSERT' | 'UPDATE' | 'DELETE',
              payload as OrgMemberPayload
            )
          } else if (config.key === 'organizations') {
            // The handler expects OrgPayload
            (config.handler as (event: 'UPDATE', payload: OrgPayload) => void)(
              eventType as 'UPDATE',
              payload as OrgPayload
            )
          }
        }
      )
    })


    channel.subscribe((status: string) => {
      log(`[${config.key}] Channel status:`, status)
      // Update global flag when channels are successfully joined
      if (status === 'SUBSCRIBED' && typeof window !== 'undefined') {
        (window as any).__hasSubscribedDashboardChannels = true;
      }
    })

    channelRegistry[config.key] = {
      channel,
      refCount: 1,
    }
    log(`[${config.key}] Subscribed, refCount=1`)
  })

  // Set global flag immediately after setting up subscriptions
  if (typeof window !== 'undefined') {
    (window as any).__hasSubscribedDashboardChannels = true;
  }
}

export function unsubscribeAllDashboardChannels() {
  Object.keys(channelRegistry).forEach(key => {
    const entry = channelRegistry[key as ChannelKey]
    if (!entry) return
    entry.refCount--
    log(`[${key}] Unsubscribed, refCount is now`, entry.refCount)
    if (entry.refCount <= 0) {
      entry.channel.unsubscribe()
      delete channelRegistry[key]
      log(`[${key}] Fully unsubscribed and removed from registry`)
    }
  })

  // Clean up context change groups to prevent memory leaks
  contextChangeGroups.forEach((group) => {
    if (group.timer) clearTimeout(group.timer);
  });
  contextChangeGroups.clear();
  log('Cleaned up context change groups');

  // Update global flag when all channels are unsubscribed
  if (Object.keys(channelRegistry).length === 0) {
    if (typeof window !== 'undefined') {
      (window as any).__hasSubscribedDashboardChannels = false;
    }
    log('All channels unsubscribed, updated global flag')
  }
}

// Helper to safely read the runtime channel state (with a minimal cast)
function getChannelState(channel: RealtimeChannel): string | undefined {
  return (channel as unknown as { state?: string }).state
}

export function areChannelsBroken(): boolean {
  return Object.values(channelRegistry).some(entry => getChannelState(entry.channel) !== 'joined')
}

/**
 * Check if we have any active channel subscriptions
 */
export function hasActiveChannels(): boolean {
  return Object.keys(channelRegistry).length > 0;
}

/**
 * Get the current subscription state for debugging
 */
export function getSubscriptionState(): {
  hasChannels: boolean;
  channelCount: number;
  channelsHealthy: boolean;
  globalFlag: boolean;
} {
  const hasChannels = hasActiveChannels();
  const channelCount = Object.keys(channelRegistry).length;
  const channelsHealthy = hasChannels && !areChannelsBroken();
  const globalFlag = typeof window !== 'undefined' ?
    Boolean((window as any).__hasSubscribedDashboardChannels) : false;

  return {
    hasChannels,
    channelCount,
    channelsHealthy,
    globalFlag
  };
}
