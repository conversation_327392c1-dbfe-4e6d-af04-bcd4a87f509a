import { z } from 'zod'

export const RoleId = {
  SUPERADMIN: 1,
  SUPPORTADMIN: 2,
  ORGADMIN: 3,
  ORGMEMBER: 4,
  ORGACCOUNTING: 5,
  OR<PERSON><PERSON>IENT: 6,
} as const

export const RoleName = {
  [RoleId.SUPERADMIN]: 'superadmin',
  [RoleId.SUPPORTADMIN]: 'supportadmin',
  [RoleId.ORGADMIN]: 'orgadmin',
  [RoleId.ORGMEMBER]: 'orgmember',
  [RoleId.ORGACCOUNTING]: 'orgaccounting',
  [RoleId.ORGCLIENT]: 'orgclient',
} as const

// Zod schema for role validation
export const roleSchema = z.object({
  role_id: z.number().int().min(1).max(6),
  role_name: z.enum(Object.values(RoleName) as [string, ...string[]]),
})

// Type for role objects
export type Role = z.infer<typeof roleSchema>

// Helper function to check if a role is at least as powerful as another role
export function isAtLeastRole(userRoleId: number, requiredRoleId: number): boolean {
  return userRoleId <= requiredRoleId
} 