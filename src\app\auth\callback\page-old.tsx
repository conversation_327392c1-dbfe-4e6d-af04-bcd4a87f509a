'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createClient();

        // Get redirect destination
        const redirectTo = searchParams.get('redirectTo') || '/dashboard';

        console.log('[Auth Callback] Starting authentication process...');

        // First, let Supabase handle any pending auth state automatically
        // This is crucial for OAuth PKCE flows
        await new Promise(resolve => setTimeout(resolve, 200)); // Small delay to let auth state settle

        // Check if we already have a session (OAuth might have been handled automatically)
        const { data: { session: existingSession }, error: sessionError } = await supabase.auth.getSession();

        if (!sessionError && existingSession) {
          console.log('[Auth Callback] Found existing session, redirecting...');
          router.replace(redirectTo);
          return;
        }

        // Determine which authentication flow to use
        const code = searchParams.get('code');
        const hasUrlHash = typeof window !== 'undefined' && window.location.hash;

        // 1️⃣ URL fragment flow (magic link OR OAuth implicit)
        if (hasUrlHash && !code) {
          console.log('[Auth Callback] Processing URL fragment flow...');

          // Parse the URL hash for session data
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          const accessToken = hashParams.get('access_token');
          const refreshToken = hashParams.get('refresh_token');
          const type = hashParams.get('type');

          if (accessToken && refreshToken) {
            if (type === 'magiclink') {
              console.log('[Auth Callback] Magic link tokens found, setting session...');
            } else {
              console.log('[Auth Callback] OAuth implicit flow tokens found, setting session...');
            }

            // Set the session using the tokens from the URL
            const { data, error: sessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (!sessionError && data?.session) {
              const flowType = type === 'magiclink' ? 'Magic link' : 'OAuth implicit';
              console.log(`[Auth Callback] ${flowType} authentication successful`);

              // Clean the URL by removing the hash
              window.history.replaceState({}, document.title, window.location.pathname + window.location.search);

              // Redirect to destination
              router.replace(redirectTo);
              return;
            } else {
              console.error('[Auth Callback] Session creation error:', sessionError);
              setError('Failed to create session from authentication tokens');
              return;
            }
          } else {
            console.error('[Auth Callback] Invalid token format in URL fragment');
            setError('Invalid authentication token format');
            return;
          }
        }

        // 2️⃣ OAuth PKCE flow (URL parameter with code)
        else if (code && !hasUrlHash) {
          console.log('[Auth Callback] Processing OAuth PKCE flow...');
          console.log('[Auth Callback] OAuth code received:', code);

          // For OAuth PKCE, Supabase should handle this automatically
          // Let's wait a bit longer for the session to be established
          console.log('[Auth Callback] Waiting for OAuth session to be established...');

          let attempts = 0;
          const maxAttempts = 5;

          while (attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms

            const { data: sessionData, error: sessionCheckError } = await supabase.auth.getSession();

            if (!sessionCheckError && sessionData?.session) {
              console.log(`[Auth Callback] OAuth session found after ${attempts + 1} attempts`);
              router.replace(redirectTo);
              return;
            }

            attempts++;
            console.log(`[Auth Callback] OAuth session check attempt ${attempts}/${maxAttempts}`);
          }

          // If we get here, OAuth didn't complete successfully
          console.error('[Auth Callback] OAuth session was not established after multiple attempts');
          setError('OAuth authentication timed out. Please try again.');
          return;
        }

        // 3️⃣ No valid authentication flow detected
        else {
          console.error('[Auth Callback] No valid authentication flow detected');

          // Check for error parameters
          const errorParam = searchParams.get('error');
          const errorDescription = searchParams.get('error_description');

          if (errorParam) {
            setError(errorDescription || 'Authentication failed');
          } else {
            // Check for existing session as last resort
            console.log('[Auth Callback] Checking for existing session...');
            const { data: { session }, error: sessionCheckError } = await supabase.auth.getSession();

            if (!sessionCheckError && session) {
              console.log('[Auth Callback] Found existing session, redirecting...');
              router.replace(redirectTo);
              return;
            }

            setError('No valid authentication data found. Please try logging in again.');
          }

          // Redirect to login after a delay
          setTimeout(() => {
            router.replace('/auth/login?error=' + encodeURIComponent('Authentication failed'));
          }, 3000);
        }

      } catch (error) {
        console.error('[Auth Callback] Exception during authentication:', error);
        setError('An unexpected error occurred during authentication');

        setTimeout(() => {
          router.replace('/auth/login?error=' + encodeURIComponent('Unexpected error'));
        }, 3000);
      } finally {
        setIsLoading(false);
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Authentication Error
            </h2>
            <p className="mt-2 text-sm text-red-600">
              {error}
            </p>
            <p className="mt-4 text-sm text-gray-500">
              Redirecting to login page...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Signing you in...
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {isLoading ? 'Processing authentication...' : 'Almost done...'}
          </p>
        </div>
      </div>
    </div>
  );
}
