'use client'

import type { OrganizationDataChangedEvent } from '@/lib/eventTypes'
import { useBusEvent } from '@/lib/useBusEvent'

/**
 * Subscribe to organization data change events for a specific organization
 * This hook provides a centralized way to listen for organization updates
 * instead of using direct Supabase subscriptions in components.
 */
export function useOrganizationEvents(
  orgId: string | null,
  handler: (event: OrganizationDataChangedEvent) => void
): void {
  useBusEvent(
    'db:organizations:updated',
    (payload) => {
      // Filter by orgId if specified
      if (!orgId || payload.orgId !== orgId) return
      handler(payload)
    },
    [orgId, handler]
  )
}

/**
 * Subscribe to all organization events (for admin/developer views)
 * This version doesn't filter by organization ID
 */
export function useAllOrganizationEvents(
  handler: (event: OrganizationDataChangedEvent) => void
): void {
  useBusEvent(
    'db:organizations:updated',
    handler,
    [handler]
  )
}
