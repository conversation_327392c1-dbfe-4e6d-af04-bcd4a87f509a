export const handbooks = [
    {
        "service": {
            "service_name": "Application for Checking and Requesting for Arrival-Departure Record under Section 25 of the Official Information Act, B.E.2540",
            "service_case_id": "50",
            "service_case": ""
        },
        "criteria": {
            "criteria_notes": "",
            "criteria_items": {
                "header": "Types of Submitting an Application and Required Documents",
                "number_of_criteria": "3",
                "sub_criteria": [
                    {
                        "number": "1",
                        "text": "There are 2 types of submitting an application.",
                        "sub_items": [
                            {
                                "number": "1.1",
                                "text": "Submit in person."
                            },
                            {
                                "number": "1.2",
                                "text": "Authorize another person to submit an application."
                            }
                        ]
                    },
                    {
                        "number": "2",
                        "text": "Required documents for submitting an application",
                        "sub_items": [
                            {
                                "number": "2.1",
                                "text": "In case of submitting in person,"
                            },
                            {
                                "number": "2.1.1",
                                "text": "Application for travel record"
                            },
                            {
                                "number": "2.1.2",
                                "text": "Original ID card or passport"
                            },
                            {
                                "number": "2.2",
                                "text": "In case of authorizing someone to act on behalf,"
                            },
                            {
                                "number": "2.2.1",
                                "text": "Application for travel record"
                            },
                            {
                                "number": "2.2.2",
                                "text": "Original ID card of an attorney"
                            },
                            {
                                "number": "2.2.3",
                                "text": "Power of attorney signed by a grantor, an attorney, and a witness and affixed with a 10-baht revenue stamp."
                            }
                        ]
                    },
                    {
                        "number": "3",
                        "text": "Procedures for submitting an application",
                        "sub_items": [
                            {
                                "number": "3.1",
                                "text": "Fill out an application form."
                            },
                            {
                                "number": "3.2",
                                "text": "Submit an application to an officer for checking the accuracy."
                            },
                            {
                                "number": "3.3",
                                "text": "An officer searches for a travel record from Immigration Information System."
                            },
                            {
                                "number": "3.4",
                                "text": "An officer prepares a letter of travel record’s checking result."
                            }
                        ]
                    }
                ]
            },
            "criteria_remark": ""
        },
        "service_channels": {
            "places_of_service": [
                {
                    "place_of_service": "Bangkok",
                    "place_of_service_text": "Immigration Technology Center",
                    "place_of_service_remark": "",
                    "service_time_days": "Monday to Friday (except official holidays)",
                    "service_time_times": "08:30 - 16:30 (Lunch break included)"
                },
                {
                    "place_of_service": "Central Region",
                    "place_of_service_text": "Immigration Division 3",
                    "place_of_service_remark": "",
                    "service_time_days": "Monday to Friday (except official holidays)",
                    "service_time_times": "08:30 - 16:30 (Lunch break included)"
                },
                {
                    "place_of_service": "Northeast Region",
                    "place_of_service_text": "Immigration Division 4",
                    "place_of_service_remark": "",
                    "service_time_days": "Monday to Friday (except official holidays)",
                    "service_time_times": "08:30 - 16:30 (Lunch break included)"
                },
                {
                    "place_of_service": "North Region",
                    "place_of_service_text": "Immigration Division 5",
                    "place_of_service_remark": "",
                    "service_time_days": "Monday to Friday (except official holidays)",
                    "service_time_times": "08:30 - 16:30 (Lunch break included)"
                },
                {
                    "place_of_service": "South Region",
                    "place_of_service_text": "Immigration Division 6",
                    "place_of_service_remark": "",
                    "service_time_days": "Monday to Friday (except official holidays)",
                    "service_time_times": "08:30 - 16:30 (Lunch break included)"
                }
            ]
        },
        "procedures": {
            "total_time": "23 minutes",
            "rows": [
                {
                    "step": "1",
                    "title": "Receive an application and check the documents.",
                    "procedure_steps": [],
                    "procedure_remarks": "",
                    "time": "3 mins",
                    "responsible_section": "Technology Sub-Division"
                },
                {
                    "step": "2",
                    "title": "Search for a travel record from Immigration Information System.",
                    "procedure_steps": [],
                    "procedure_remarks": "",
                    "time": "15 mins",
                    "responsible_section": "Technology Sub-Division"
                },
                {
                    "step": "3",
                    "title": "Prepare a letter of travel record’s checking result.",
                    "procedure_steps": [],
                    "procedure_remarks": "",
                    "time": "5 mins",
                    "responsible_section": "Technology Sub-Division"
                }
            ]
        },
        "required_documents": {
            "rows": []
        },
        "fee": {
            "rows": []
        },
        "complaint_channels": {
            "rows": [
                {
                    "number": "1",
                    "channel_name": "Immigration Bureau",
                    "remarks": "",
                    "address": "507 Soi Suanplu, South Sathorn Road, Sathorn District, Bangkok 10120",
                    "pobox": "P.O. Box 1178 Suanplu, Bangkok 10120",
                    "hotline": "1178",
                    "telephone": "",
                    "email": "",
                    "website": ""
                }
            ]
        },
        "application_forms": {
            "rows": []
        },
        "bottom_remarks": {
            "header": "",
            "remarks": "",
            "sub_remarks": [
                {
                    "number": "1",
                    "text": "Please visit https://www.immigration.go.th for more information."
                }
            ]
        },
        "source": {
            "source-url": "https://www.immigration.go.th",
            "source-publication-date": ""
        }
    },
    {
        "service": {
            "service_name": "Application for Extension of Stay for an Alien Who Has Been Granted Permission to Temporarily Stay in the Kingdom in Accordance with the Order of Royal Thai Police No. 327/2557 Dated 30 June 2014 Regarding Criteria and Conditions for Consideration of an Alien’s Application for a Temporary Stay in the Kingdom",
            "service_case_id": "2.1",
            "service_case": "Business Necessity - An applicant must stay to carry out work for a company or partnership, etc."
        },
        "criteria": {
            "criteria_notes": "An alien must have all the qualifications in accordance with the Order of Royal Thai Police No.327/2557 dated 30 June 2014 regarding Criteria and Conditions for Consideration of an Alien’s Application for a Temporary Stay in the Kingdom and its additional amendments.",
            "criteria_items": {
                "header": "",
                "number_of_criteria": "",
                "sub_criteria": []
            },
            "criteria_remark": "An alien must contact an immigration checkpoint in the area where the alien works. If there is no immigration checkpoint in that area, please contact the responsible immigration checkpoint. Please visit https://www.immigration.go.th for more information."
        },
        "service_channels": {
            "places_of_service": [
                {
                    "place_of_service": "Local Immigration Checkpoint",
                    "place_of_service_text": "Please contact in person at a local immigration checkpoint.",
                    "place_of_service_remark": "Service time is subject to the office hours of each immigration checkpoint.",
                    "service_time_days": "Monday to Friday (except official holidays)",
                    "service_time_times": "08:30 - 16:30 (Lunch break included)"
                }
            ]
        },
        "procedures": {
            "total_time": "21 working days 90 minutes",
            "rows": [
                {
                    "step": "1",
                    "title": "Inspection of documents",
                    "procedure_steps": [
                        {
                            "number": "1",
                            "text": "An officer receives an application."
                        },
                        {
                            "number": "2",
                            "text": "An officer checks the documents and records the information in the Immigration Information System with a photo taken."
                        },
                        {
                            "number": "3",
                            "text": "Receive a fee and issue a receipt."
                        },
                        {
                            "number": "4",
                            "text": "An officer stamps a result hearing appointment and signs."
                        }
                    ],
                    "procedure_remarks": "",
                    "time": "60 mins",
                    "responsible_section": "Immigration Division 1 and Immigration Division 3 - 6"
                },
                {
                    "step": "2",
                    "title": "Consideration",
                    "procedure_steps": [
                        {
                            "number": "1",
                            "text": "Consider the qualifications of an alien to be in accordance with the criteria and conditions and seek supplementary evidence for consideration."
                        }
                    ],
                    "procedure_remarks": "",
                    "time": "21 working days",
                    "responsible_section": "Immigration Division 1 and Immigration Division 3 - 6"
                },
                {
                    "step": "3",
                    "title": "Sign for approval/Committee’s Resolutions",
                    "procedure_steps": [
                        {
                            "number": "1",
                            "text": "Submit the original passport."
                        },
                        {
                            "number": "2",
                            "text": "An officer stamps a visa and signs."
                        },
                        {
                            "number": "3",
                            "text": "Return the passport."
                        }
                    ],
                    "procedure_remarks": "",
                    "time": "30 mins",
                    "responsible_section": "Immigration Division 1 and Immigration Division 3 - 6"
                }
            ]
        },
        "required_documents": {
            "rows": [
                {
                    "number": "1",
                    "document_name": "Passport",
                    "original_documents": "1",
                    "copies": "1",
                    "remarks": "",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "2",
                    "document_name": "Work Permit",
                    "original_documents": "1",
                    "copies": "1",
                    "remarks": "",
                    "sub_remarks": [],
                    "authority": "Department of Employment"
                },
                {
                    "number": "3",
                    "document_name": "Application Form (TM.7)",
                    "original_documents": "1",
                    "copies": "0",
                    "remarks": "Photo Attached",
                    "sub_remarks": [],
                    "authority": "Immigration Bureau"
                },
                {
                    "number": "4",
                    "document_name": "The Acknowledgement of Penalties for a Visa Overstay",
                    "original_documents": "1",
                    "copies": "0",
                    "remarks": "",
                    "sub_remarks": [],
                    "authority": "Immigration Bureau"
                },
                {
                    "number": "5",
                    "document_name": "Evidence proving company registration",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "Certificate of company or partnership registration, certified by the registrar within the previous six months",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "6",
                    "document_name": "List of shareholders",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "Certified by the registrar within the previous six months",
                    "sub_remarks": [
                        {
                            "number": "1",
                            "text": "Not required for an international trade business (representative office), regional office, and overseas company (branch office)"
                        }
                    ],
                    "authority": ""
                },
                {
                    "number": "7",
                    "document_name": "Balance sheet and profit and loss statement",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "",
                    "sub_remarks": [
                        {
                            "number": "1",
                            "text": "Not required for an international trade business (representative office), regional office, and overseas company (branch office)"
                        }
                    ],
                    "authority": ""
                },
                {
                    "number": "8",
                    "document_name": "Income tax return for companies or juristic partnerships and payment receipt",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "",
                    "sub_remarks": [
                        {
                            "number": "1",
                            "text": "Not required for an international trade business (representative office), regional office, and overseas company (branch office)"
                        }
                    ],
                    "authority": ""
                },
                {
                    "number": "9",
                    "document_name": "Financial statement submission form (SorBorChor.3 or SorBorChor.3/1)",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "",
                    "sub_remarks": [
                        {
                            "number": "1",
                            "text": "Not required for an international trade business (representative office), regional office, and overseas company (branch office)"
                        }
                    ],
                    "authority": ""
                },
                {
                    "number": "10",
                    "document_name": "Latest monthly withholding income tax return",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "Specifying the name of employees and of an alien applicant together with a copy of the payment receipt (Phor.Ngor.Dor.1)",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "11",
                    "document_name": "Individual income tax return of an alien applicant",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "For the latest year together with a copy of the payment receipt (If any) (Phor.Ngor.Dor.90 or Phor.Ngor.Dor.1)",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "12",
                    "document_name": "Value-added tax return form for the latest month",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "(PhorPhor.30 or PhorPhor.36) together with a copy of the payment receipt",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "13",
                    "document_name": "Specific business tax return form for the latest month",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "(Phor.Thor.40) together with a copy of the payment receipt",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "14",
                    "document_name": "Social security contribution return for the latest month",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "Submitted to the Social Security Office (SorPorSor.1-10) together with a copy of payment receipt",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "15",
                    "document_name": "Document or evidence proving the necessity of the business to hire an alien",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "For example, in case a job opening was posted for Thai candidates but there were no applicants",
                    "sub_remarks": [
                        {
                            "number": "1",
                            "text": "Not required for an international trade business (representative office), regional office, and overseas company (branch office)"
                        }
                    ],
                    "authority": ""
                },
                {
                    "number": "16",
                    "document_name": "Location map showing an applicant's workplace",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "Including photographs of the interior and exterior of the workplace during business hours",
                    "sub_remarks": [],
                    "authority": ""
                },
                {
                    "number": "17",
                    "document_name": "Documents or other evidence as prescribed by the committee monitoring the work operations of competent officials of the Immigration Bureau",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "",
                    "sub_remarks": [
                        {
                            "number": "A",
                            "text": "Certification of an alien employment as prescribed by the Immigration Bureau (STM.1)"
                        },
                        {
                            "number": "B",
                            "text": "Acknowledgement of Terms and Conditions for Permit of Temporary Stay in the Kingdom of Thailand (STM.2)"
                        }
                    ],
                    "authority": ""
                },
                {
                    "number": "18",
                    "document_name": "A license for business operations",
                    "original_documents": "0",
                    "copies": "1",
                    "remarks": "Must have a license as prescribed by the law such as a hotel, factory, sightseeing business, restaurant, antique shop, nursery, etc.",
                    "sub_remarks": [
                        {
                            "number": "1",
                            "text": "Documents no. 6, 7, 8, 9 and 15 are not required for an international trade business (representative office), regional office, and overseas company (branch office)"
                        }
                    ],
                    "authority": ""
                }
            ]
        },
        "fee": {
            "rows": [
                {
                    "number": "1",
                    "fee_details": "Fee",
                    "fee_amount": "1900",
                    "remarks": ""
                }
            ]
        },
        "complaint_channels": {
            "rows": [
                {
                    "number": "1",
                    "channel_name": "Immigration Bureau",
                    "remarks": "",
                    "address": "507 Soi Suanplu, South Sathorn Road, Sathorn District, Bangkok 10120",
                    "pobox": "P.O. Box 1178 Suanplu, Bangkok 10120",
                    "hotline": "1178",
                    "telephone": "",
                    "email": "",
                    "website": ""
                },
                {
                    "number": "2",
                    "channel_name": "Local Immigration Checkpoint",
                    "remarks": "",
                    "address": "",
                    "pobox": "",
                    "hotline": "",
                    "telephone": "",
                    "email": "",
                    "website": ""
                },
                {
                    "number": "3",
                    "channel_name": "Center of Public Service, Office of the Permanent Secretary, The Prime Minister's Office",
                    "remarks": "",
                    "address": "1 Phitsanulok Road, Dusit District, Bangkok 10300",
                    "pobox": "P.O. Box 1111 Phitsanulok Road, Dusit District, Bangkok 10300",
                    "hotline": "1111",
                    "telephone": "",
                    "email": "",
                    "website": "https://www.1111.go.th"
                }
            ]
        },
        "application_forms": {
            "rows": [
                {
                    "number": "1",
                    "form_name": "Application Form (TM.7) APPLICATION FOR EXTENSION OF TEMPORARY STAY IN THE KINGDOM",
                    "remarks": ""
                },
                {
                    "number": "2",
                    "form_name": "The Acknowledgement of Penalties for a Visa Overstay",
                    "remarks": ""
                }
            ]
        },
        "bottom_remarks": {
            "header": "",
            "remarks": "",
            "sub_remarks": []
        },
        "source": {
            "source-url": "Backend.info.go.th",
            "source-publication-date": "08/11/2019"
        }
    },
    {
      service: {
        service_name: "Application for Checking and Additional Correcting of Alien Registration Document",
        service_case_id: "10",
        service_case: "",
      },
      criteria: {
        criteria_notes: "",
        criteria_items: {
          header: "",
          number_of_criteria: "2",
          sub_criteria: [
            {
              number: "1",
              text: "An alien registration document will be checked and corrected in case the document details are incorrect or change of nationality.",
              sub_items: [],
            },
            {
              number: "2",
              text: "An alien must submit an application in person.",
              sub_items: [],
            },
          ],
        },
        criteria_remark:
          "An alien must contact an immigration checkpoint in the area where the alien resides. If there is no immigration checkpoint in that area, please contact the responsible immigration checkpoint.",
      },
      service_channels: {
        places_of_service: [
          {
            place_of_service:
              "Alien Registration Section, Sub-Division 1, Immigration Division 1, Government Complex Commemorating His Majesty, 2nd Floor, Chaengwattana Road, Laksi District, Bangkok",
            place_of_service_text: "Please contact in person at the service place.",
            place_of_service_remark: "",
            service_time_days: "Monday to Friday (except official holidays)",
            service_time_times: "08:30 - 16:30 (Lunch break included)",
          },
        ],
      },
      procedures: {
        total_time: "7 working days",
        rows: [
          {
            step: "1",
            title: "Inspection of documents",
            procedure_steps: [
              {
                number: "1",
                text: "Submit an application, passport, and required documents.",
              },
              {
                number: "2",
                text: "An officer checks the documents.",
              },
              {
                number: "3",
                text: "Search for the counterfoil receipt of Alien Registration Book.",
              },
              {
                number: "4",
                text: "An officer interviews an alien.",
              },
              {
                number: "5",
                text: "Receive an appointment receipt and passport.",
              },
            ],
            procedure_remarks: "",
            time: "120 mins",
            responsible_section: "Sub-Division 1, Immigration Division 1",
          },
          {
            step: "2",
            title: "Consideration",
            procedure_steps: [
              {
                number: "1",
                text: "Check the information of Alien Registration Book with the counterfoil copy of Alien Registration Book and passport.",
              },
              {
                number: "2",
                text: "Proceed to supervisor for granting approval to inform a local registrar who is responsible for correcting the Alien Registration Book.",
              },
            ],
            procedure_remarks: "",
            time: "5 working days (after the application date)",
            responsible_section: "Sub-Division 1, Immigration Division 1",
          },
          {
            step: "3",
            title: "Result Hearing",
            procedure_steps: [
              {
                number: "1",
                text: "Submit an appointment receipt and passport.",
              },
              {
                number: "2",
                text: "Receive an Alien Registration Book and passport.",
              },
            ],
            procedure_remarks: "",
            time: "10 mins",
            responsible_section: "Sub-Division 1, Immigration Division 1",
          },
        ],
      },
      required_documents: {
        rows: [
          {
            number: "1",
            document_name: "Application Form",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "2",
            document_name: "Alien Registration Book",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Royal Thai Police",
          },
          {
            number: "3",
            document_name: "Certificate of Residence",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "4",
            document_name: "Certificate of Alien Identity",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "5",
            document_name: "Passport with Thai translation and a copy (Certified by the Ministry of Foreign Affairs)",
            original_documents: "1",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "6",
            document_name: "Photos, size 4X6 cm.",
            original_documents: "0",
            copies: "2",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "7",
            document_name: "House Registration (TorRor.14)",
            original_documents: "1",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "8",
            document_name: "Letter of certification with Thai translation",
            original_documents: "1",
            copies: "1",
            remarks: "(Issued by an embassy or consulate, certified by the Ministry of Foreign Affairs)",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "9",
            document_name: "Birth Certificate",
            original_documents: "1",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "10",
            document_name: "Work Permit",
            original_documents: "1",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
        ],
      },
      fee: {
        rows: [],
      },
      complaint_channels: {
        rows: [
          {
            number: "1",
            channel_name: "Immigration Bureau",
            remarks: "",
            address: "507 Soi Suanplu, South Sathorn Road, Sathorn District, Bangkok 10120",
            pobox: "P.O. Box 1178 Suanplu, Bangkok 10120",
            hotline: "1178",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "2",
            channel_name: "Sub-Division 1, Immigration Division 1",
            remarks: "",
            address: "",
            pobox: "",
            hotline: "",
            telephone: "",
            email: "",
            website: "",
          },
        ],
      },
      application_forms: {
        rows: [
          {
            number: "",
            form_name: "N/A",
            remarks: "",
          },
        ],
      },
      bottom_remarks: {
        header: "Related laws and regulations",
        remarks: "",
        sub_remarks: [
          {
            number: "1",
            text: "Alien Registration Act B.E.2493, Section 14",
          },
          {
            number: "2",
            text: "Code of Police Regulations not related to the case, Category 55, Chapter 6",
          },
        ],
      },
      source: {
        "source-url": "Backend.info.go.th",
        "source-publication-date": "08/11/2019",
      },
    },
    {
      service: {
        service_name:
          "Application for Extension of Stay in the Kingdom for an alien who has been granted privileges under the Industrial Estate Authority of Thailand Act, B.E. 2522",
        service_case_id: "11",
        service_case: "",
      },
      criteria: {
        criteria_notes: "",
        criteria_items: {
          header: "",
          number_of_criteria: "6",
          sub_criteria: [
            {
              number: "1",
              text: "An alien, who is a craftsman or expert, has been granted privileges under Section 45 of the Industrial Estate Authority of Thailand Act, B.E. 2522 (Amendment), including a spouse and a dependent of the said alien. (Amendment)",
              sub_items: [],
            },
            {
              number: "2",
              text: "Have a valid passport in good condition.",
              sub_items: [],
            },
            {
              number: "3",
              text: "Must have been granted a Non-Immigrant Visa.",
              sub_items: [],
            },
            {
              number: "4",
              text: "Must submit an Application for Extension of Temporary Stay in the Kingdom no more than 45 days prior to the expiration date of the previous visa. (Amendment)",
              sub_items: [],
            },
            {
              number: "5",
              text: "In case it is found that an alien overstays the visa or does not submit an application to notify of staying longer than 90 days (90-day reporting), such alien must be fined first.",
              sub_items: [],
            },
            {
              number: "6",
              text: "In case of permitted period of stay expiring on the same day as the passport expiration date, an alien must transfer the latest entry stamp, visa stamp, and re-entry permit stamp (if any) to a new passport and then re-submit an application for visa extension in accordance with the original right. (Amendment)",
              sub_items: [],
            },
          ],
        },
        criteria_remark: "",
      },
      service_channels: {
        places_of_service: [
          {
            place_of_service: "One Stop Service Center for Visa and Work Permit",
            place_of_service_text: "Chamchuri Square Building, 18th floor, Bangkok",
            place_of_service_remark: "",
            service_time_days: "Monday to Friday (except official holidays)",
            service_time_times: "08:30 - 16:30 (Have a lunch break)",
          },
          {
            place_of_service: "EEC Labor Administration Centre, Institute of Skill Development Region 3, Chonburi",
            place_of_service_text: "",
            place_of_service_remark: "",
            service_time_days: "Monday to Friday (except official holidays)",
            service_time_times: "08:30 - 16:30 (Have a lunch break)",
          },
          {
            place_of_service: "Local immigration checkpoint",
            place_of_service_text: "",
            place_of_service_remark: "Service time is subject to the office hours of each immigration checkpoint.",
            service_time_days: "Monday to Friday (except official holidays)",
            service_time_times: "08:30 - 16:30 (Have a lunch break)",
          },
        ],
      },
      procedures: {
        total_time: "25 minutes",
        rows: [
          {
            step: "1",
            title: "Inspection of documents",
            procedure_steps: [
              {
                number: "1",
                text: "An officer inspects an application and relevant documents.",
              },
              {
                number: "2",
                text: "An officer receives an application.",
              },
              {
                number: "3",
                text: "An officer checks the blacklist, records the information in the Immigration Information System, takes a photo, and stamps a visa on the passport.",
              },
            ],
            procedure_remarks: "",
            time: "15 mins",
            responsible_section: "Immigration Division 1 and Immigration Division 3 - 6",
          },
          {
            step: "2",
            title: "Consideration / Sign for approval",
            procedure_steps: [
              {
                number: "1",
                text: "An authority signs for approval.",
              },
            ],
            procedure_remarks: "",
            time: "5 mins",
            responsible_section: "Immigration Division 1 and Immigration Division 3 - 6",
          },
          {
            step: "3",
            title: "Fee payment",
            procedure_steps: [
              {
                number: "1",
                text: "Pay a fee and issue a receipt.",
              },
              {
                number: "2",
                text: "Return the passport.",
              },
            ],
            procedure_remarks: "",
            time: "5 mins",
            responsible_section: "Immigration Division 1 and Immigration Division 3 - 6",
          },
        ],
      },
      required_documents: {
        rows: [
          {
            number: "1",
            document_name: "Passport",
            original_documents: "1",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "2",
            document_name: "Work Permit",
            original_documents: "0",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "Department of Employment",
          },
          {
            number: "3",
            document_name: "Application Form (TM.7)",
            original_documents: "1",
            copies: "0",
            remarks: "(Photo attached)",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "4",
            document_name:
              "Letter of confirmation and request for a temporary stay from the Commission of the Industrial Estate Authority of Thailand",
            original_documents: "1",
            copies: "0",
            remarks: "Must be submitted within 60 days from the issuance date.",
            sub_remarks: [],
            authority: "The Industrial Estate Authority of Thailand",
          },
          {
            number: "5",
            document_name: "Sor.Tor.Mor.2 Form",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "6",
            document_name: "The Acknowledgement of Penalties for a Visa Overstay",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
        ],
      },
      fee: {
        rows: [
          {
            number: "1",
            fee_details: "Fee",
            fee_amount: "1900",
            remarks: "",
          },
        ],
      },
      complaint_channels: {
        rows: [
          {
            number: "1",
            channel_name: "Immigration Bureau",
            remarks: "",
            address: "507 Soi Suanplu, South Sathorn Road, Sathorn District, Bangkok 10120",
            pobox: "P.O. Box 1178 Suanplu, Bangkok 10120",
            hotline: "1178",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "2",
            channel_name: "Local Immigration Checkpoint",
            remarks: "",
            address: "",
            pobox: "",
            hotline: "",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "3",
            channel_name: "Center of Public Service, Office of the Permanent Secretary, The Prime Minister's Office",
            remarks: "",
            address: "1 Phitsanulok Road, Dusit District, Bangkok 10300",
            pobox: "P.O. Box 1111 Phitsanulok Road, Dusit District, Bangkok 10300",
            hotline: "1111",
            telephone: "",
            email: "",
            website: "https://www.1111.go.th",
          },
        ],
      },
      application_forms: {
        rows: [
          {
            number: "1",
            form_name: "Application Form (TM.7) Application for Extension of Temporary Stay in the Kingdom",
            remarks: "",
          },
          {
            number: "2",
            form_name: "Sor.Tor.Mor.2 Form",
            remarks: "",
          },
          {
            number: "3",
            form_name: "The Acknowledgement of Penalties for a Visa Overstay",
            remarks: "",
          },
        ],
      },
      bottom_remarks: {
        header: "Related laws and regulations",
        remarks: "",
        sub_remarks: [
          {
            number: "1",
            text: "Alien Registration Act B.E.2493, Section 14",
          },
          {
            number: "2",
            text: "Code of Police Regulations not related to the case, Category 55, Chapter 6",
          },
        ],
      },
      source: {
        "source-url": "Backend.info.go.th",
        "source-publication-date": "08/11/2019",
      },
    },
    {
      service: {
        service_name:
          "Application for Extension of Stay in the Kingdom for an alien who has been granted privileges under the Petroleum Act, B.E. 2514",
        service_case_id: "12",
        service_case: "",
      },
      criteria: {
        criteria_notes: "",
        criteria_items: {
          header: "",
          number_of_criteria: "6",
          sub_criteria: [
            {
              number: "1",
              text: "An alien, who is a craftsman, specialist, or expert has been granted privileges under Section 69 of the Petroleum Act, B.E. 2514, including a spouse and a dependent of the said alien. (Amendment)",
              sub_items: [],
            },
            {
              number: "2",
              text: "Have a valid passport in good condition.",
              sub_items: [],
            },
            {
              number: "3",
              text: "Must have been granted a Non-Immigrant visa.",
              sub_items: [],
            },
            {
              number: "4",
              text: "Must submit an Application for Extension of Temporary Stay in the Kingdom no more than 45 days prior to the expiration date of the previous visa.",
              sub_items: [],
            },
            {
              number: "5",
              text: "In case it is found that an alien overstays the visa or does not submit an application to notify of staying longer than 90 days (90-day reporting), such alien must be fined first.",
              sub_items: [],
            },
            {
              number: "6",
              text: "In case of permitted period of stay expires on the same day as the passport expiration date, an alien must transfer the latest entry stamp, visa stamp, and re-entry permit stamp (if any) to a new passport and then re-submits an application for visa extension in accordance with the original right.",
              sub_items: [],
            },
          ],
        },
        criteria_remark:
          "An alien must contact an immigration checkpoint in the area where the alien resides. If there is no immigration checkpoint in that area, please contact the responsible immigration checkpoint. Please visit https://www.immigration.go.th for more information.",
      },
      service_channels: {
        places_of_service: [
          {
            place_of_service: "One Stop Service Center for Visa and Work Permit",
            place_of_service_text: "Chamchuri Square Building, 18th floor, Bangkok",
            place_of_service_remark: "",
            service_time_days: "Monday to Friday (except official holidays)",
            service_time_times: "08:30 - 16:30 (Lunch break included)",
          },
          {
            place_of_service: "EEC Labor Administration Centre",
            place_of_service_text: "Institute of Skill Development Region 3, Chonburi",
            place_of_service_remark: "",
            service_time_header: "",
            service_time_days: "",
            service_time_times: "",
          },
          {
            place_of_service: "Local Immigration Checkpoint",
            place_of_service_text: "",
            place_of_service_remark: "Service time is subject to the office hours of each immigration checkpoint.",
            service_time_header: "",
            service_time_days: "",
            service_time_times: "",
          },
        ],
      },
      procedures: {
        total_time: "25 minutes",
        rows: [
          {
            step: "1",
            title: "Inspection of documents",
            procedure_steps: [
              {
                number: "1",
                text: "An officer inspects an application and relevant documents.",
              },
              {
                number: "2",
                text: "An officer receives an application.",
              },
              {
                number: "3",
                text: "An officer checks the blacklist and records the information in the Immigration Information System with a photo taken and stamps a visa on the passport.",
              },
            ],
            procedure_remarks: "",
            time: "15 mins.",
            responsible_section: "Immigration Division 1 and Immigration Division 3 - 6",
          },
          {
            step: "2",
            title: "Consideration/Sign for approval",
            procedure_steps: [
              {
                number: "1",
                text: "An authority signs for approval.",
              },
            ],
            procedure_remarks: "",
            time: "5 mins.",
            responsible_section: "Immigration Division 1 and Immigration Division 3 - 6",
          },
          {
            step: "3",
            title: "Fee payment",
            procedure_steps: [
              {
                number: "1",
                text: "Pay a fee and issue a receipt.",
              },
              {
                number: "2",
                text: "Return the passport.",
              },
            ],
            procedure_remarks: "",
            time: "5 mins.",
            responsible_section: "Immigration Division 1 and Immigration Division 3 - 6",
          },
        ],
      },
      required_documents: {
        rows: [
          {
            number: "1",
            document_name: "Passport",
            original_documents: "1",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "2",
            document_name: "Work Permit",
            original_documents: "0",
            copies: "1",
            remarks: "",
            sub_remarks: [],
            authority: "Department of Employment",
          },
          {
            number: "3",
            document_name: "Application Form (TM.7)",
            original_documents: "1",
            copies: "0",
            remarks: "Photo attached",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "4",
            document_name: "Letter of confirmation and request for a temporary stay from the Board of Petroleum",
            original_documents: "1",
            copies: "0",
            remarks: "Within 60 days from issuance date",
            sub_remarks: [],
            authority: "Department of Mineral Fuels",
          },
          {
            number: "5",
            document_name: "Sor.Tor.Mor.2 Form",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "6",
            document_name: "The Acknowledgement of Penalties for a Visa Overstay",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
        ],
      },
      fee: {
        rows: [
          {
            number: "1",
            fee_details: "Fee",
            fee_amount: "1900",
            remarks: "",
          },
        ],
      },
      complaint_channels: {
        rows: [
          {
            number: "1",
            channel_name: "Immigration Bureau",
            remarks: "",
            address: "507 Soi Suanplu, South Sathorn Road, Sathorn District, Bangkok 10120",
            pobox: "P.O. Box 1178 Suanplu, Bangkok 10120",
            hotline: "1178",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "2",
            channel_name: "Local Immigration Checkpoint",
            remarks: "",
            address: "",
            pobox: "",
            hotline: "",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "3",
            channel_name: "Center of Public Service, Office of the Permanent Secretary, The Prime Minister's Office",
            remarks: "",
            address: "1 Phitsanulok Road, Dusit District, Bangkok 10300",
            pobox: "P.O. Box 1111 Phitsanulok Road, Dusit District, Bangkok 10300",
            hotline: "Hotline: 1111",
            telephone: "",
            email: "",
            website: "https://www.1111.go.th",
          },
        ],
      },
      application_forms: {
        rows: [
          {
            number: "1",
            form_name: "Application Form (TM.7) Application for Extension of Temporary Stay in the Kingdom",
            remarks: "",
          },
          {
            number: "2",
            form_name: "Sor.Tor.Mor.2 Form",
            remarks: "",
          },
          {
            number: "3",
            form_name: "The Acknowledgement of Penalties for a Visa Overstay",
            remarks: "",
          },
        ],
      },
      bottom_remarks: {
        header: "",
        remarks: "",
        sub_remarks: [],
      },
      source: {
        "source-url": "Backend.info.go.th",
        "source-publication-date": "08/11/2019",
      },
    },
    {
      service: {
        service_name: "Application for Endorsement of Re-Entry Permit",
        service_case_id: "9",
        service_case: "",
      },
      criteria: {
        criteria_notes: "",
        criteria_items: {
          header: "",
          number_of_criteria: "2",
          sub_criteria: [
            {
              number: "1",
              text: "An alien wishes to leave the Kingdom.",
              sub_items: [],
            },
            {
              number: "2",
              text: "If an alien already has an endorsement stamped, the said alien must re-enter the Kingdom within the latest endorsement date.",
              sub_items: [],
            },
          ],
        },
        criteria_remark:
          "An alien must contact an immigration checkpoint in the area where the alien resides. If there is no immigration checkpoint in that area, please contact the responsible immigration checkpoint. Please visit https://www.immigration.go.th for more information.",
      },
      service_channels: {
        places_of_service: [
          {
            place_of_service: "Local Immigration Checkpoint",
            place_of_service_text: "Please contact in person at a local immigration checkpoint.",
            place_of_service_remark: "Service time is subject to the office hours of each immigration checkpoint.",
            service_time_days: "Monday to Friday (except official holidays)",
            service_time_times: "08:30 - 16:30 (Lunch break included)",
          },
        ],
      },
      procedures: {
        total_time: "140 minutes",
        rows: [
          {
            step: "1",
            title: "Inspection of document",
            procedure_steps: [
              {
                number: "1",
                text: "Submit an application form (TM.13).",
              },
              {
                number: "2",
                text: "An officer checks the documents.",
              },
              {
                number: "3",
                text: "An officer saves the information in the Immigration Information System and inspects the arrival-departure record.",
              },
              {
                number: "4",
                text: "Pay a fee.",
              },
            ],
            procedure_remarks: "",
            time: "60 mins.",
            responsible_section: "Immigration Division 1",
          },
          {
            step: "2",
            title: "Consideration",
            procedure_steps: [
              {
                number: "1",
                text: "An officer takes and inspects an alien's fingerprints.",
              },
              {
                number: "2",
                text: "An officer stamps an endorsement on the passport.",
              },
            ],
            procedure_remarks: "",
            time: "60 mins.",
            responsible_section: "Immigration Division 1",
          },
          {
            step: "3",
            title: "Signature/ Committee's Resolutions",
            procedure_steps: [
              {
                number: "1",
                text: "An officer stamps an endorsement on the passport.",
              },
              {
                number: "2",
                text: "Receive Certificate of Residence, passport, and Alien Registration Book.",
              },
            ],
            procedure_remarks: "",
            time: "20 mins.",
            responsible_section: "Immigration Division 1",
          },
        ],
      },
      required_documents: {
        rows: [
          {
            number: "1",
            document_name: "Application for Endorsement of Re-Entry Permit (TM.13)",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "2",
            document_name: "Passport or document used in lieu of passport",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "3",
            document_name: "Alien Registration Book",
            original_documents: "1",
            copies: "0",
            remarks: "Issued by the police station where the alien resides.",
            sub_remarks: [],
            authority: "",
          },
          {
            number: "4",
            document_name: "Certificate of Residence or Substitute of Certificate of Residence (TM.17)",
            original_documents: "1",
            copies: "0",
            remarks: "",
            sub_remarks: [],
            authority: "Immigration Bureau",
          },
          {
            number: "5",
            document_name: "Half-length, straight face photo without hat, size 4X6 cm.",
            original_documents: "1",
            copies: "0",
            remarks: "Taken no more than 6 months ago.",
            sub_remarks: [],
            authority: "",
          },
        ],
      },
      fee: {
        rows: [
          {
            number: "1",
            fee_details: "Fee",
            fee_amount: "1900",
            remarks: "",
          },
        ],
      },
      complaint_channels: {
        rows: [
          {
            number: "1",
            channel_name: "Immigration Bureau",
            remarks: "",
            address: "507 Soi Suanplu, South Sathorn Road, Sathorn District, Bangkok 10120",
            pobox: "P.O. Box 1178 Suanplu, Bangkok 10120",
            hotline: "1178",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "2",
            channel_name: "Local Immigration Checkpoint",
            remarks: "",
            address: "",
            pobox: "",
            hotline: "",
            telephone: "",
            email: "",
            website: "",
          },
          {
            number: "3",
            channel_name: "Center of Public Service, Office of the Permanent Secretary, The Prime Minister's Office",
            remarks: "",
            address: "1 Phitsanulok Road, Dusit District, Bangkok 10300",
            pobox: "P.O. Box 1111 Phitsanulok Road, Dusit District, Bangkok 10300",
            hotline: "Hotline: 1111",
            telephone: "",
            email: "",
            website: "https://www.1111.go.th",
          },
        ],
      },
      application_forms: {
        rows: [
          {
            number: "1",
            form_name: "Application Form (TM.13) Application for Endorsement of Re-Entry Permit",
            remarks: "",
          },
        ],
      },
      bottom_remarks: {
        header: "Related laws and regulations",
        remarks: "",
        sub_remarks: [
          {
            number: "1",
            text: "Immigration Act, B.E.2522, Section 50(1)",
          },
          {
            number: "2",
            text: "Code of Police Regulations not related to the case, Category 34, Chapter 15",
          },
          {
            number: "3",
            text: "Order of the Royal Thai Police no.228/2549 dated 12 September 2006, Article 2",
          },
          {
            number: "4",
            text: "Ministerial Regulations no.27 (B.E.2546) issued under the Immigration Act, B.E.2522",
          },
        ],
      },
      source: {
        "source-url": "Backend.info.go.th",
        "source-publication-date": "08/11/2019",
      },
    },
  ] 