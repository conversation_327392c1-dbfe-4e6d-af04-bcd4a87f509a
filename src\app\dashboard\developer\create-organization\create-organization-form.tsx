"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { ImagePlus } from "lucide-react";
import Image from "next/image";
import { Switch } from "@/components/ui/switch";
import { toastMessages } from "@/lib/toast-messages";
import { createOrganizationAction } from "./actions"; // This relative path should now be correct
import { useActionState } from "react";
import { useFormStatus } from "react-dom";

// Submit button with loading state
function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <Button
      type="submit"
      className="bg-[#194852] hover:bg-[#194852]/90"
      disabled={pending}
    >
      {pending ? "Creating..." : "Create Organization"}
    </Button>
  );
}

export function CreateOrganizationForm() {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isActive, setIsActive] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  
  // Use the renamed useActionState hook
  const [formState, formAction] = useActionState(createOrganizationAction, null);

  useEffect(() => {
    // Check for form submission results
    if (formState) {
      if ('error' in formState) {
        // Show error toast
        toastMessages.common.unexpectedError(formState.error);
      } else if ('success' in formState && formState.success) {
        // Show success toast if message exists
        if (formState.message) {
          toastMessages.organization.createSuccess();
        }
        // Redirect to organization list
        router.push('/dashboard/admin/organization'); // This redirect path might need review after moving the page.
        router.refresh();
      }
    }
  }, [formState, router]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Custom action to ensure status is properly set
  const handleFormAction = async (formData: FormData) => {
    // Make sure the isActive boolean is explicitly set in the FormData
    formData.set("is_active", isActive ? "true" : "false");
    
    // Call the server action with our modified form data
    return formAction(formData);
  };

  return (
    <Card className="bg-white">
      <form action={handleFormAction}>
        <div className="flex flex-col md:flex-row">
          {/* Organization Icon Upload Section */}
          <div className="md:w-[200px] md:border-r md:border-gray-200">
            <CardHeader>
              <CardTitle>Organization Icon</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center space-y-4">
                <div className="relative w-[150px] h-[150px] border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                  {imagePreview ? (
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      width={150}
                      height={150}
                      className="object-cover rounded-lg"
                      priority
                    />
                  ) : (
                    <div className="text-center">
                      <ImagePlus className="w-8 h-8 mx-auto text-gray-400" />
                      <p className="text-sm text-gray-500 mt-2">
                        Organization Icon
                      </p>
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    name="icon_file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    title="Upload icon"
                  />
                </div>
                <p className="text-xs text-gray-500 text-center">
                  Upload organization icon
                  <br />
                  150x150 recommended
                </p>
              </div>
            </CardContent>
          </div>

          {/* Form Content */}
          <div className="flex-1">
            <CardHeader>
              <CardTitle>Organization Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                {/* Organization Name */}
                <div className="space-y-2">
                  <Label htmlFor="orgName">
                    Organization Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="orgName"
                    name="org_name"
                    placeholder="Enter organization name"
                    required
                  />
                </div>

                {/* Is Active Switch */}
                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="is_active">Organization Status</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={isActive}
                      onCheckedChange={setIsActive}
                    />
                    <Label
                      htmlFor="is_active"
                      className="text-sm text-gray-500"
                    >
                      {isActive ? "Active" : "Inactive"}
                    </Label>
                  </div>
                  {/* Hidden input to ensure the value is included in form submission */}
                  <input 
                    type="hidden" 
                    name="is_active" 
                    value={isActive ? "true" : "false"} 
                  />
                </div>
              </div>
            </CardContent>

            <div className="px-6 pb-6">
              <div className="flex justify-end">
                <SubmitButton />
              </div>
            </div>
          </div>
        </div>
      </form>
    </Card>
  );
} 