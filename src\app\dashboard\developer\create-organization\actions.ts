'use server'

import { withPermissionAction } from '@/lib/rbac/server-action'
import { createClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'

export interface CreateOrganizationData {
  org_name: string
  is_active: boolean
  icon_file?: File | null
}

// Define the type for the state returned by the form action
export type ActionState = { success: boolean; message?: string } | { error: string } | null;

/**
 * Server action to create a new organization
 * Protected by RBAC to allow only superadmin and supportadmin roles
 */
export const createOrganization = withPermissionAction(
  async (data: CreateOrganizationData) => {
    try {
      const supabase = await createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('Authentication error: Unable to get current user')
      }
      
      let org_icon_url = null
      
      // Handle file upload if provided
      // Check if file exists and has size
      if (data.icon_file && data.icon_file.size > 0) {
        // Convert the File object to ArrayBuffer for server-side upload
        const fileBuffer = await data.icon_file.arrayBuffer()
        
        // Ensure we have a valid file extension
        const fileExt = data.icon_file.name.split('.').pop()
        if (!fileExt) {
          throw new Error('Invalid file: missing file extension')
        }
        
        const fileName = `${Math.random()}.${fileExt}`
        const filePath = `${fileName}`
        
        const { error: uploadError } = await supabase.storage
          .from('org_icon')
          .upload(filePath, fileBuffer, {
            contentType: data.icon_file.type
          })
          
        if (uploadError) {
          throw new Error(`Error uploading image: ${uploadError.message}`)
        }
        
        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('org_icon')
          .getPublicUrl(filePath)
          
        org_icon_url = publicUrl
      }
      
      // Create the organization directly in the database
      const { data: newOrg, error: orgError } = await supabase
        .from('organizations')
        .insert([
          {
            org_name: data.org_name,
            is_active: data.is_active,
            org_icon: org_icon_url,
          },
        ])
        .select()
        .single()
        
      if (orgError || !newOrg) {
        throw new Error(`Failed to create organization: ${orgError?.message}`)
      }
      
      // Revalidate the organizations page path to refresh the data
      revalidatePath('/dashboard/admin/organization') // This path might need an update to the new developer section or a more general path if orgs are listed elsewhere for admins.
      
      return { success: true, organization: newOrg }
    } catch (error) {
      console.error('Organization creation error:', error)
      throw error
    }
  },
  {
    // Only superadmins and supportadmins can create organizations
    crRoles: ['superAdmin', 'supportAdmin']
  }
)

/**
 * Form action wrapper to handle form submission and provide appropriate redirects
 * This function signature matches what useActionState expects
 */
export async function createOrganizationAction(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  try {
    // Extract form data
    const org_name = formData.get('org_name') as string
    
    // Properly parse the boolean value
    const is_active_value = formData.get('is_active') as string
    // Make sure it's a proper boolean value, not just truthy/falsy string conversion
    const is_active = is_active_value === 'true'
    
    const icon_file = formData.get('icon_file') as File | null
    
    console.log('Creating organization with status:', is_active, 'Original value:', is_active_value)
    
    // Call the protected action
    await createOrganization({
      org_name,
      is_active,
      icon_file
    })
    
    // Return success first, so the client can update its state
    return { 
      success: true, 
      message: 'Organization created successfully'
    }
  } catch (error) {
    console.error('Form submission error:', error)
    // Return the error for the client component to handle
    return { error: (error as Error).message }
  }
} 