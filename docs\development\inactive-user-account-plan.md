# Plan: Refine Inactive User Account Logic

## Analysis:

Based on the provided logs and file list, the key files involved in the current flow for handling user status changes appear to be:

*   `src/app/dashboard/layout.tsx`: This component likely handles the main dashboard layout, fetching initial context, determining the active organization, and controlling the overall structure and navigation based on user/organization status.
*   `src/lib/auth-context.ts`: This file probably manages the user's authentication state and organization memberships, providing context about their organizations and status within them.
*   `src/components/organization/organization-switcher.tsx`: This component displays the list of organizations and allows switching. It needs updates to correctly show organizations where the user is disabled and handle navigation upon selection.
*   `src/components/dashboard/dashboard-event-manager.tsx`: This component seems to manage real-time events related to user and organization status, triggering refreshes.
*   `src/middleware.ts` and `src/lib/rbac/middleware.ts`: These files are responsible for setting initial context (like `authIsUserActive`, `authIsOrgActive`) based on the request, used by `layout.tsx`.

The current issue seems to be that the redirect to `/dashboard/account-disabled` occurs incorrectly when a user is disabled in an organization *other than* the currently active one. This suggests the check for `authIsUserActive` might not be correctly scoped to the active organization context within `layout.tsx`, or the middleware isn't providing the precise context needed.

## Plan:

1.  **Refine User Account Disabled Check:**
    *   Examine `src/app/dashboard/layout.tsx` to understand how it uses context from middleware headers (`authUserId`, `authOrgId`, `authIsUserActive`, `authIsOrgActive`).
    *   Locate the logic that triggers the redirect to `/dashboard/account-disabled`.
    *   Modify this logic to ensure the redirect only happens if `authIsUserActive` is `false` AND the `authOrgId` from the middleware headers matches the `orgId` of the currently active organization being rendered by the layout.
    *   Review `src/middleware.ts` and `src/lib/rbac/middleware.ts` to confirm that `authIsUserActive` in the headers accurately reflects the user's status *within the organization specified by `authOrgId`*. Adjust middleware logic if necessary.

2.  **Control Navigation Visibility:**
    *   Identify where the main dashboard navigation is rendered in `src/app/dashboard/layout.tsx` or a component it uses (e.g., `Sidebar.tsx`, `TopBar.tsx`).
    *   Implement conditional rendering for navigation elements (excluding the organization switcher). Navigation should only display if the user's account is *not* disabled in the currently active organization, using the refined check from step 1.

3.  **Update Organization Switcher Behavior:**
    *   Examine `src/components/organization/organization-switcher.tsx`.
    *   Ensure the list of organizations displayed includes those where the user's account is disabled.
    *   Modify the click handler for organization items. If a user selects an organization where their account is disabled, navigate them to the `/dashboard/account-disabled` page, similar to handling disabled organizations, but based on the user's status within that specific organization.

## Detailed Task List:

Based on the required precedence (Authentication > Organization Disabled > User Disabled in Organization), here are the steps to implement the refined logic:

1.  **Review Authentication and Organization Disabled Checks:**
    *   Examine `src/middleware.ts`, `src/lib/rbac/middleware.ts`, and `src/app/dashboard/layout.tsx` to understand how the existing authentication and organization disabled checks are performed.
    *   Note where the `authIsOrgActive` and superadmin checks are handled in relation to determining the active organization and potential redirects.

2.  **Refine Middleware Context for User Status (if needed):**
    *   Based on the review in step 1 and the initial analysis, determine if the `authIsUserActive` context being set in the middleware accurately reflects the user's status *within the organization identified by `authOrgId`*.
    *   If not, modify `src/middleware.ts` or `src/lib/rbac/middleware.ts` to ensure this context is correctly set.

3.  **Update Dashboard Layout Redirect Logic:**
    *   In `src/app/dashboard/layout.tsx`, locate the logic that handles redirects based on organization or user status.
    *   Integrate the check for `authIsUserActive` being `false` specifically for the *currently active organization* being rendered by the layout. This check should come *after* the organization disabled check.
    *   Ensure the redirect to `/dashboard/account-disabled` only occurs if the user is inactive in the active organization.

4.  **Implement Conditional Navigation Rendering:**
    *   In `src/app/dashboard/layout.tsx` or related navigation components (`Sidebar.tsx`, `TopBar.tsx`), add conditional logic to hide the main navigation (keeping only the organization switcher) when the user is inactive in the active organization. This logic should use the status determined in step 3.

5.  **Modify Organization Switcher Display and Navigation:**
    *   In `src/components/organization/organization-switcher.tsx`, ensure that organizations where the user is disabled are included in the list displayed to the user.
    *   Update the click handler for organization items. If a user selects an organization where their account is disabled, navigate them to the `/dashboard/account-disabled` page, similar to the existing handling for disabled organizations, but based on the user's status within that specific organization.

This detailed list provides a step-by-step guide for implementation, respecting the required precedence and incorporating the existing logic.

## Scenario Handling based on Precedence:

Here's how the system should behave based on the user's status and organization status, following the precedence: Authentication > Organization Disabled > User Disabled in Organization.

**Disabled Organization:**

*   **Enabled user (non-superadmin) in organization context:** Authentication check passes. Organization disabled check fails (organization is disabled). Redirect to disabled organization page. No access to dashboard navigation or any pages under dashboard except organization switcher.
*   **Enabled user (superadmin) in organization context:** Authentication check passes. Organization disabled check passes (superadmins are exempt). User disabled check passes (user is enabled). Full access to dashboard navigation and all pages under dashboard.
*   **Disabled user (non-superadmin) in organization context:** Authentication check passes. Organization disabled check fails (organization is disabled). Redirect to disabled organization page. No access to dashboard navigation or any pages under dashboard except organization switcher. (Note: User disabled status is not reached due to higher precedence of organization disabled).
*   **Disabled user (superadmin) in organization context:** Authentication check passes. Organization disabled check passes (superadmins are exempt). User disabled check fails (user is disabled). Redirect to disabled account page. No access to dashboard navigation or any pages under dashboard except organization switcher.

**Enabled Organization:**

*   **Enabled user (non-superadmin) in organization context:** Authentication check passes. Organization disabled check passes (organization is enabled). User disabled check passes (user is enabled). Regular RBAC rules and business logic apply.
*   **Enabled user (superadmin) in organization context:** Authentication check passes. Organization disabled check passes (organization is enabled). User disabled check passes (user is enabled). Regular RBAC rules and business logic apply (superadmin privileges may grant additional access).
*   **Disabled user (non-superadmin) in organization context:** Authentication check passes. Organization disabled check passes (organization is enabled). User disabled check fails (user is disabled). Redirect to disabled account page. No access to dashboard navigation or any pages under dashboard except organization switcher.
*   **Disabled user (superadmin) in organization context:** Authentication check passes. Organization disabled check passes (organization is enabled). User disabled check fails (user is disabled). Redirect to disabled account page. No access to dashboard navigation or any pages under dashboard except organization switcher.

## Flow Diagram:

```mermaid
graph TD
    A[User Action: Change User Status to Inactive] --> B(Event Bus: member:status:changed)
    B --> C{Dashboard Event Manager & Org Switcher}
    C --> D(Trigger Refresh/Context Update)
    D --> E[Middleware]
    E --> F{Determine Auth Context: authUserId, authOrgId, authIsUserActive, authIsOrgActive}
    F --> G[Dashboard Layout (layout.tsx)]
    G --> H{Check User Status in Active Org}
    H -- User Active in Active Org --> I[Render Full Dashboard]
    H -- User Inactive in Active Org --> J[Redirect to /dashboard/account-disabled]
    J --> K[Render Account Disabled Page]
    G --> L[Render Navigation]
    L -- User Active in Active Org --> M[Show Full Navigation]
    L -- User Inactive in Active Org --> N[Show Only Org Switcher]
    G --> O[Render Organization Switcher]
    O --> P{Display Organizations}
    P -- Includes Orgs with User Disabled --> Q[Allow Selection]
    Q -- Select Disabled Org --> J