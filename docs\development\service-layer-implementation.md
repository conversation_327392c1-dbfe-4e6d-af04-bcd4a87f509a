# Service Layer Implementation Plan

## Executive Summary

This document outlines the migration plan to centralize Supabase `createClient` usage through a service layer architecture. Currently, 39 non-route files directly import and use `createClient`, creating scattered database access patterns that are difficult to monitor, test, and secure.

## Current State Assessment

### Files Using createClient (39 total)

#### Server Actions (5 files)
- `app/actions/accept-email-invitation.ts`
- `app/actions/delete-email-invitation.ts`
- `app/actions/resend-email-invitation.ts`
- `app/actions/send-email-invitation.ts`
- `app/actions/switch-organization.ts`

#### Auth Pages (2 files)
- `app/auth/callback/page-old.tsx`
- `app/auth/callback/page.tsx`

#### Dashboard Pages & Layouts (12 files)
- `app/dashboard/layout.tsx`
- `app/dashboard/admin/layout.tsx`
- `app/dashboard/admin/members/invites/page.tsx`
- `app/dashboard/admin/organization/page.tsx`
- `app/dashboard/admin/users/user-profile-dialog.tsx`
- `app/dashboard/clients/invites/page.tsx`
- `app/dashboard/developer/create-organization/actions.ts`
- `app/dashboard/developer/organizations/page.tsx`
- `app/dashboard/developer/test-event/page.tsx`
- `app/dashboard/developer/users/page.tsx`
- `app/dashboard/developer/users/invites/page.tsx`
- `app/dashboard/profile/page.tsx`

#### Test Pages (1 file)
- `app/test-auth/page.tsx`

#### Components (8 files)
- `components/user-avatar-menu.tsx`
- `components/auth/google-auth-button.tsx`
- `components/auth/user-auth-form.tsx`
- `components/dashboard/dashboard-event-manager-core.tsx`
- `components/dashboard/dashboard-header.tsx`
- `components/developer/test-event-system.tsx`
- `components/organization/organization-switcher.tsx`
- `components/providers/global-dashboard-provider.tsx`

#### Hooks (4 files)
- `hooks/use-auth.ts`
- `hooks/use-organization-context.ts`
- `hooks/use-organization-members-event-bus.ts`
- `hooks/use-realtime-subscription.ts`

#### Lib Utilities (6 files)
- `lib/auth-utils.ts`
- `lib/get-user-active-organization.ts`
- `lib/organization-utils-server.ts`
- `lib/invitations/invitation-utils.ts`
- `lib/rbac/permissions-server.ts`
- `lib/supabase/admin.ts`

#### Providers (1 file)
- `providers/organization-check-provider.tsx`

## Migration Strategy

### Phase 1: Create Service Layer Foundation
1. Create base service classes and interfaces
2. Implement core services (Auth, Organization, Invitation)
3. Add centralized logging and error handling

### Phase 2: Migrate High-Impact Areas
1. Server actions (highest security impact)
2. RBAC and permissions (security critical)
3. Core utilities (widely used)

### Phase 3: Migrate UI Components
1. Hooks and providers
2. Dashboard components
3. Auth components

### Phase 4: Cleanup and Validation
1. Remove direct createClient usage
2. Add linting rules
3. Update documentation

## Detailed File Assessment

### 🔴 HIGH PRIORITY - Server Actions (5 files)

#### `app/actions/send-email-invitation.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: Medium
**Current Usage**: Direct Supabase for RBAC, user auth, invitation creation, email sending
**Security Impact**: High - handles sensitive invitation data and email operations
**Migration Effort**: 4 hours

**Assessment**:
- Complex business logic with RBAC validation
- Email integration with Resend
- Secure token generation and hashing
- Multiple database operations

**Migration Plan**:
- Create `InvitationService.createInvitation()`
- Move RBAC validation to service layer
- Centralize email sending logic
- Add comprehensive logging

#### `app/actions/delete-email-invitation.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: Low
**Current Usage**: Direct Supabase for RBAC, invitation lookup, deletion
**Security Impact**: High - handles invitation deletion with RBAC
**Migration Effort**: 2 hours

**Assessment**:
- Simple CRUD operation with RBAC
- Status validation logic
- Organization context validation

**Migration Plan**:
- Create `InvitationService.deleteInvitation()`
- Move RBAC validation to service layer
- Add audit logging for deletions

#### `app/actions/resend-email-invitation.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: Medium
**Current Usage**: Direct Supabase for invitation lookup, token generation, email sending
**Security Impact**: High - regenerates secure tokens
**Migration Effort**: 3 hours

**Assessment**:
- Token regeneration logic
- Email resending with Resend
- Status and expiry updates

**Migration Plan**:
- Create `InvitationService.resendInvitation()`
- Centralize token generation
- Add email delivery tracking

#### `app/actions/accept-email-invitation.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: Low
**Current Usage**: Direct Supabase for database function calls
**Security Impact**: Medium - uses database functions
**Migration Effort**: 1 hour

**Assessment**:
- Simple wrapper around database functions
- Minimal business logic
- Already uses secure patterns

**Migration Plan**:
- Create `InvitationService.acceptInvitation()`
- Maintain database function usage
- Add logging for acceptance events

#### `app/actions/switch-organization.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: Low
**Current Usage**: Direct Supabase for organization context switching
**Security Impact**: High - changes user context
**Migration Effort**: 2 hours

**Assessment**:
- Critical for organization context management
- Uses database functions
- Simple but security-sensitive

**Migration Plan**:
- Create `OrganizationService.switchContext()`
- Add context validation
- Enhance logging for context changes

### 🟡 MEDIUM PRIORITY - RBAC & Core Utilities (6 files)

#### `lib/rbac/permissions-server.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: High
**Current Usage**: Core RBAC validation logic
**Security Impact**: Critical - foundation of security system
**Migration Effort**: 6 hours

**Assessment**:
- Central to entire security model
- Complex permission evaluation logic
- Used by all protected operations

**Migration Plan**:
- Create `RbacService` with permission checking
- Maintain existing API for backward compatibility
- Add comprehensive audit logging
- Enhance caching mechanisms

#### `lib/organization-utils-server.ts`
**Risk Level**: 🟡 MEDIUM
**Complexity**: High
**Current Usage**: Organization context resolution, user organization management
**Security Impact**: Medium - affects organization context
**Migration Effort**: 5 hours

**Assessment**:
- Complex organization context logic
- Multiple database queries
- Critical for multi-tenant functionality

**Migration Plan**:
- Create `OrganizationService` for context management
- Centralize organization queries
- Add caching for frequently accessed data

#### `lib/invitations/invitation-utils.ts`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: Invitation data fetching and enrichment
**Security Impact**: Medium - handles invitation data
**Migration Effort**: 3 hours

**Assessment**:
- Data fetching utilities
- Profile enrichment logic
- Used by invitation components

**Migration Plan**:
- Integrate with `InvitationService`
- Standardize data fetching patterns
- Add caching for profile data

#### `lib/auth-utils.ts`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Low
**Current Usage**: Authentication utilities
**Security Impact**: Medium - auth-related operations
**Migration Effort**: 2 hours

**Assessment**:
- Simple auth utility functions
- Limited database operations
- Low complexity

**Migration Plan**:
- Create `AuthService` for auth operations
- Centralize auth-related queries
- Add session management utilities

#### `lib/get-user-active-organization.ts`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: User organization resolution
**Security Impact**: Medium - affects user context
**Migration Effort**: 3 hours

**Assessment**:
- Organization context resolution
- User membership queries
- Context initialization logic

**Migration Plan**:
- Merge with `OrganizationService`
- Standardize context resolution
- Add error handling and logging

#### `lib/supabase/admin.ts`
**Risk Level**: 🔴 HIGH
**Complexity**: Low
**Current Usage**: Admin client creation and management
**Security Impact**: Critical - service role access
**Migration Effort**: 2 hours

**Assessment**:
- Admin client management
- Service role operations
- Security-critical functionality

**Migration Plan**:
- Enhance with service layer patterns
- Add operation logging
- Improve error handling

### 🟢 LOW PRIORITY - Dashboard Pages & Layouts (12 files)

#### `app/dashboard/layout.tsx`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: User profile fetching, organization data
**Security Impact**: Low - read-only operations
**Migration Effort**: 3 hours

**Assessment**:
- Server-side data fetching for layout
- User profile and organization queries
- Critical for dashboard initialization

**Migration Plan**:
- Create `UserService.getProfile()`
- Use `OrganizationService` for org data
- Maintain SSR performance

#### `app/dashboard/admin/layout.tsx`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Low
**Current Usage**: Admin-specific layout data
**Security Impact**: Low - layout data only
**Migration Effort**: 2 hours

**Assessment**:
- Simple admin layout logic
- Minimal database operations
- Low complexity

**Migration Plan**:
- Integrate with existing services
- Add admin-specific data fetching
- Maintain layout performance

#### Dashboard Pages (10 files)
**Risk Level**: 🟢 LOW
**Complexity**: Low-Medium
**Current Usage**: Page-specific data fetching
**Security Impact**: Low - mostly read operations
**Migration Effort**: 2-3 hours each

**Assessment**:
- Server-side data fetching for pages
- Invitation and organization queries
- Standard SSR patterns

**Migration Plan**:
- Replace direct queries with service calls
- Maintain SSR performance
- Standardize error handling

### 🟢 LOW PRIORITY - Auth Pages (2 files)

#### `app/auth/callback/page.tsx` & `page-old.tsx`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: Auth callback handling, session management
**Security Impact**: Medium - auth flow critical
**Migration Effort**: 3 hours each

**Assessment**:
- Auth callback processing
- Session validation
- OAuth flow handling

**Migration Plan**:
- Create `AuthService.handleCallback()`
- Centralize session management
- Improve error handling

### 🟢 LOW PRIORITY - Components (9 files)

#### `components/auth/user-auth-form.tsx`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: Authentication form logic
**Security Impact**: Medium - handles auth
**Migration Effort**: 3 hours

**Assessment**:
- Client-side auth operations
- Form validation and submission
- OAuth integration

**Migration Plan**:
- Use `AuthService` for auth operations
- Maintain client-side performance
- Improve error handling

#### `components/auth/google-auth-button.tsx`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Low
**Current Usage**: Google OAuth integration
**Security Impact**: Medium - OAuth flow
**Migration Effort**: 2 hours

**Assessment**:
- Simple OAuth button
- Google auth integration
- Minimal complexity

**Migration Plan**:
- Use `AuthService` for OAuth
- Maintain existing UX
- Add error handling

#### Dashboard & Organization Components (7 files)
**Risk Level**: 🟢 LOW
**Complexity**: Low-Medium
**Current Usage**: UI data fetching, real-time updates
**Security Impact**: Low - UI operations
**Migration Effort**: 2-3 hours each

**Assessment**:
- Client-side data operations
- Real-time subscriptions
- UI state management

**Migration Plan**:
- Replace direct Supabase calls with service calls
- Maintain real-time functionality
- Improve error handling

### 🟢 LOW PRIORITY - Hooks (4 files)

#### `hooks/use-realtime-subscription.ts`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: Real-time database subscriptions
**Security Impact**: Low - read-only subscriptions
**Migration Effort**: 4 hours

**Assessment**:
- Real-time subscription management
- Connection handling
- Error recovery

**Migration Plan**:
- Create `RealtimeService` for subscriptions
- Centralize connection management
- Improve error handling

#### `hooks/use-organization-members-event-bus.ts`
**Risk Level**: 🟡 MEDIUM
**Complexity**: High
**Current Usage**: Member data fetching, API calls
**Security Impact**: Medium - member operations
**Migration Effort**: 5 hours

**Assessment**:
- Complex member management logic
- API integration
- Event bus coordination

**Migration Plan**:
- Use existing API routes (already centralized)
- Maintain event bus integration
- Improve error handling

#### Other Hooks (2 files)
**Risk Level**: 🟢 LOW
**Complexity**: Low-Medium
**Current Usage**: Auth state, organization context
**Security Impact**: Low - state management
**Migration Effort**: 2-3 hours each

**Assessment**:
- Simple state management hooks
- Auth and context operations
- Standard patterns

**Migration Plan**:
- Use appropriate services
- Maintain hook APIs
- Improve error handling

### 🟢 LOW PRIORITY - Test & Development (2 files)

#### `app/test-auth/page.tsx`
**Risk Level**: 🟢 LOW
**Complexity**: Low
**Current Usage**: Auth testing utilities
**Security Impact**: None - test only
**Migration Effort**: 1 hour

**Assessment**:
- Simple test page
- Auth testing functions
- Development only

**Migration Plan**:
- Use `AuthService` for consistency
- Maintain test functionality
- Low priority

#### `components/developer/test-event-system.tsx`
**Risk Level**: 🟢 LOW
**Complexity**: Low
**Current Usage**: Event system testing
**Security Impact**: None - test only
**Migration Effort**: 1 hour

**Assessment**:
- Development testing component
- Event system validation
- Non-production code

**Migration Plan**:
- Update for consistency
- Maintain test functionality
- Low priority

### 🟢 LOW PRIORITY - Providers (1 file)

#### `providers/organization-check-provider.tsx`
**Risk Level**: 🟡 MEDIUM
**Complexity**: Medium
**Current Usage**: Organization context validation
**Security Impact**: Medium - context management
**Migration Effort**: 3 hours

**Assessment**:
- Organization context provider
- Context validation logic
- User experience critical

**Migration Plan**:
- Use `OrganizationService`
- Maintain provider patterns
- Improve error handling

---

## Migration Scratchpad

### ✅ COMPLETED
- [x] Assessment of all 54 files using createClient
- [x] Risk and complexity analysis
- [x] Migration effort estimation
- [x] Priority classification

### 🔄 IN PROGRESS
- [ ] None currently

### 📋 TODO - Phase 1: Service Layer Foundation (Week 1)

#### Task 1.1: Create Base Service Infrastructure
**Files**: `lib/services/base/`
**Estimated Time**: 8 hours
**Priority**: 🔴 CRITICAL
**Dependencies**: None

**Checklist**:
- [ ] Create `BaseService` abstract class
- [ ] Implement centralized logging system
- [ ] Create error handling framework
- [ ] Add service configuration management
- [ ] Create service factory pattern
- [ ] Add TypeScript interfaces and types
- [ ] Implement caching layer foundation
- [ ] Add monitoring and metrics hooks

**Key Components**:
```typescript
// lib/services/base/base-service.ts
export abstract class BaseService {
  protected supabase: SupabaseClient
  protected logger: ServiceLogger

  constructor(client?: SupabaseClient) {
    this.supabase = client || createClient()
    this.logger = new ServiceLogger(this.constructor.name)
  }

  protected async logOperation(operation: string, data?: any): Promise<void>
  protected handleError(error: any, operation: string): never
  protected validateInput<T>(schema: ZodSchema<T>, data: unknown): T
}
```

#### Task 1.2: Create Core Services
**Files**: `lib/services/auth/`, `lib/services/organization/`, `lib/services/invitation/`
**Estimated Time**: 12 hours
**Priority**: 🔴 CRITICAL
**Dependencies**: Task 1.1

**Checklist**:
- [ ] Create `AuthService` class
- [ ] Create `OrganizationService` class
- [ ] Create `InvitationService` class
- [ ] Create `RbacService` class
- [ ] Implement service interfaces
- [ ] Add comprehensive error handling
- [ ] Create service unit tests
- [ ] Add JSDoc documentation

### 📋 TODO - Phase 2: High-Priority Migrations (Week 2)

#### Task 2.1: Migrate Server Actions
**Files**: All 5 server action files
**Estimated Time**: 12 hours
**Priority**: 🔴 HIGH
**Dependencies**: Task 1.2

**Checklist**:
- [ ] Migrate `send-email-invitation.ts` → `InvitationService.createInvitation()`
- [ ] Migrate `delete-email-invitation.ts` → `InvitationService.deleteInvitation()`
- [ ] Migrate `resend-email-invitation.ts` → `InvitationService.resendInvitation()`
- [ ] Migrate `accept-email-invitation.ts` → `InvitationService.acceptInvitation()`
- [ ] Migrate `switch-organization.ts` → `OrganizationService.switchContext()`
- [ ] Update all server action imports
- [ ] Test all invitation flows
- [ ] Verify RBAC functionality

#### Task 2.2: Migrate RBAC System
**Files**: `lib/rbac/permissions-server.ts`
**Estimated Time**: 6 hours
**Priority**: 🔴 HIGH
**Dependencies**: Task 1.2

**Checklist**:
- [ ] Create `RbacService.checkPermission()`
- [ ] Migrate `checkRbacPermission()` function
- [ ] Add comprehensive audit logging
- [ ] Enhance caching mechanisms
- [ ] Update all RBAC imports
- [ ] Test permission validation
- [ ] Verify security model integrity

#### Task 2.3: Migrate Core Utilities
**Files**: 5 lib utility files
**Estimated Time**: 15 hours
**Priority**: 🔴 HIGH
**Dependencies**: Task 1.2

**Checklist**:
- [ ] Migrate `organization-utils-server.ts` → `OrganizationService`
- [ ] Migrate `invitation-utils.ts` → `InvitationService`
- [ ] Migrate `auth-utils.ts` → `AuthService`
- [ ] Migrate `get-user-active-organization.ts` → `OrganizationService`
- [ ] Enhance `lib/supabase/admin.ts` with service patterns
- [ ] Update all utility imports
- [ ] Test core functionality
- [ ] Verify performance impact

### 📋 TODO - Phase 3: UI Component Migrations (Week 3)

#### Task 3.1: Migrate Auth Components
**Files**: Auth pages and components
**Estimated Time**: 10 hours
**Priority**: 🟡 MEDIUM
**Dependencies**: Task 2.2

**Checklist**:
- [ ] Migrate `app/auth/callback/page.tsx` → `AuthService`
- [ ] Migrate `app/auth/callback/page-old.tsx` → `AuthService`
- [ ] Migrate `components/auth/user-auth-form.tsx` → `AuthService`
- [ ] Migrate `components/auth/google-auth-button.tsx` → `AuthService`
- [ ] Test auth flows
- [ ] Verify OAuth functionality
- [ ] Update error handling

#### Task 3.2: Migrate Dashboard Components
**Files**: Dashboard layouts and components
**Estimated Time**: 15 hours
**Priority**: 🟡 MEDIUM
**Dependencies**: Task 2.3

**Checklist**:
- [ ] Migrate `app/dashboard/layout.tsx` → Services
- [ ] Migrate `app/dashboard/admin/layout.tsx` → Services
- [ ] Migrate dashboard page components → Services
- [ ] Migrate organization components → `OrganizationService`
- [ ] Migrate dashboard event components → Services
- [ ] Test dashboard functionality
- [ ] Verify SSR performance
- [ ] Update error handling

#### Task 3.3: Migrate Hooks and Providers
**Files**: 4 hooks and 1 provider
**Estimated Time**: 12 hours
**Priority**: 🟡 MEDIUM
**Dependencies**: Task 2.3

**Checklist**:
- [ ] Migrate `hooks/use-realtime-subscription.ts` → `RealtimeService`
- [ ] Migrate `hooks/use-organization-members-event-bus.ts` → Services
- [ ] Migrate `hooks/use-auth.ts` → `AuthService`
- [ ] Migrate `hooks/use-organization-context.ts` → `OrganizationService`
- [ ] Migrate `providers/organization-check-provider.tsx` → Services
- [ ] Test hook functionality
- [ ] Verify real-time subscriptions
- [ ] Update provider patterns

### 📋 TODO - Phase 4: Cleanup and Validation (Week 4)

#### Task 4.1: Remove Direct createClient Usage
**Files**: All migrated files
**Estimated Time**: 8 hours
**Priority**: 🟡 MEDIUM
**Dependencies**: All previous tasks

**Checklist**:
- [ ] Remove direct `createClient` imports
- [ ] Update import statements to use services
- [ ] Add ESLint rules to prevent direct usage
- [ ] Create migration guide documentation
- [ ] Update code review guidelines
- [ ] Add service usage examples

#### Task 4.2: Testing and Validation
**Files**: All migrated files
**Estimated Time**: 12 hours
**Priority**: 🔴 HIGH
**Dependencies**: Task 4.1

**Checklist**:
- [ ] Run comprehensive test suite
- [ ] Test all invitation flows
- [ ] Test all auth flows
- [ ] Test RBAC functionality
- [ ] Test organization switching
- [ ] Test real-time subscriptions
- [ ] Performance testing
- [ ] Security validation

#### Task 4.3: Documentation and Training
**Files**: Documentation
**Estimated Time**: 6 hours
**Priority**: 🟡 MEDIUM
**Dependencies**: Task 4.2

**Checklist**:
- [ ] Update architecture documentation
- [ ] Create service usage guide
- [ ] Document migration patterns
- [ ] Create troubleshooting guide
- [ ] Update development guidelines
- [ ] Create team training materials

## Risk Assessment Summary

### 🔴 HIGH RISK (11 files)
- **Server Actions (5)**: Critical security operations
- **RBAC System (1)**: Foundation of security model
- **Admin Client (1)**: Service role operations
- **Auth Pages (2)**: Authentication flows
- **Auth Components (2)**: OAuth and form handling

### 🟡 MEDIUM RISK (15 files)
- **Core Utilities (5)**: Widely used functions
- **Dashboard Layouts (2)**: SSR performance critical
- **Organization Components (3)**: Context management
- **Hooks (4)**: State management and real-time
- **Provider (1)**: Context validation

### 🟢 LOW RISK (28 files)
- **Dashboard Pages (10)**: Standard SSR patterns
- **UI Components (7)**: Client-side operations
- **Test Files (2)**: Development only
- **Other Components (9)**: Standard UI patterns

## Success Metrics

1. **Security**: All database operations go through centralized services
2. **Monitoring**: 100% of operations are logged and auditable
3. **Testing**: All services have comprehensive unit tests
4. **Performance**: No degradation in SSR or client performance
5. **Maintainability**: Reduced complexity and improved code organization
6. **Documentation**: Complete service usage documentation

## Rollback Plan

1. **Phase-by-phase rollback**: Each phase can be independently reverted
2. **Service toggles**: Feature flags to switch between old and new patterns
3. **Gradual migration**: Services can coexist with direct usage during transition
4. **Backup branches**: Maintain stable branches for each phase
5. **Monitoring**: Real-time monitoring to detect issues early

## Estimated Timeline

- **Phase 1**: 1 week (Foundation)
- **Phase 2**: 1 week (High-priority migrations)
- **Phase 3**: 1 week (UI migrations)
- **Phase 4**: 1 week (Cleanup and validation)

**Total**: 4 weeks for complete migration

## Resource Requirements

- **Development**: 1 senior developer full-time
- **Testing**: QA support for comprehensive testing
- **Review**: Architecture review and approval
- **Documentation**: Technical writing support

