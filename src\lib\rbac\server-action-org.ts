import { checkRbacPermission } from './permissions-server'
import { OrgActionConfig, ExtractRbacConditions } from '@/types/lib/rbac'

/**
 * Higher-order function to protect organization-specific server actions with RBAC permissions
 * First parameter of the wrapped function must be the organization ID
 * @param action The server action function to protect (first arg must be orgId)
 * @param config RBAC configuration for authorization checks
 * @returns Protected action function
 */
export function withOrgAdmin<TArgs extends [string, ...unknown[]], TReturn>(
  action: (...args: TArgs) => Promise<TReturn>,
  config: OrgActionConfig = {}
) {
  return async (...args: TArgs): Promise<TReturn> => {
    try {
      const [orgId] = args
      
      // Extract RBAC conditions from config
      const rbacConditions = config as ExtractRbacConditions<OrgActionConfig>
      
      // Check permission using RBAC system, adding the organization context
      const hasAccess = await checkRbacPermission({ 
        ...rbacConditions,
        resourceOrgId: orgId,
        // Default to requiring org admin level if no specific RBAC conditions
        ...(Object.keys(rbacConditions).length === 0 ? { rMinRole: "orgAdmin" } : {})
      }, { silentFail: true })

      if (!hasAccess) {
        throw new Error('Unauthorized: Insufficient permissions for this organization')
      }

      return await action(...args)
    } catch (error) {
      if (config.onError && error instanceof Error) {
        config.onError(error)
      }
      throw error
    }
  }
}

// Example usage:
// const updateOrgSettings = withOrgAdmin(
//   async (orgId: string, settings: OrgSettings) => {
//     // Update organization settings logic
//     return { success: true }
//   },
//   {
//     // Optional RBAC conditions, defaults to rMinRole: "orgAdmin" if not specified
//     ruMinRole: "orgAdmin" // Require org admin for read/update operations
//   }
// ) 