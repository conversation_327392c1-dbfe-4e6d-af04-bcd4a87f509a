"use client";

import { useState, useEffect, useMemo, memo } from "react";
import { useOrganizationMembersEventBus, type UseOrganizationMembersProps } from "@/hooks/use-organization-members-event-bus";
import { Role<PERSON><PERSON> } from "@/lib/rbac/rbac-utils";
import { useRbacPermission } from "@/hooks/use-rbac-permission";
import { roleUtils } from "@/lib/rbac/role-utils";
import type { OrganizationMemberFull } from "@/types/organization/";
import {
  canModifyUser,
  canToggleMemberStatus,
  canChangeUserRole,
  canDeleteMember,
  getAssignableRoles
} from "@/lib/permission-utils";
// Member table specific types (moved from deleted hook)
export interface MemberTableBusinessRules {
  canEditRole?: boolean;
  profileEditMinRole?: RoleKey;
  roleEditMinRole?: RoleKey;
  maxAssignableRole?: RoleKey;
  statusChangeMinRole?: RoleK<PERSON>;
  deleteMinRole?: <PERSON><PERSON><PERSON>;
  inviteMinRole?: Role<PERSON>ey | null;
}

export type MemberTablePermissionModel = 'business-rules' | 'role-based' | 'full-access' | 'legacy';

export interface MemberTablePagePermissions {
  canViewProfile?: boolean;
  canEditRole?: boolean;
  canChangeStatus?: boolean;
  canRemoveMember?: boolean;
  canInvite?: boolean;
}
import type { OrganizationMemberBasic } from "@/types/organization/OrganizationMember";
import { toastMessages } from "@/lib/toast-messages";
import { format } from "date-fns";
import Image from "next/image";
import {
  Search, PlusCircle, Loader2, Trash2,
  ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserProfileDialog } from "@/app/dashboard/admin/users/user-profile-dialog";
import { TableSkeleton } from "@/app/dashboard/admin/users/table-skeleton";
import { useAuthContextStore } from "@/stores/useAuthContextStore";
import { useOrganizationsList } from "@/hooks/use-organizations-list";
import type { Organization } from "@/types/organization";

// AvailableRole interface moved to centralized permission utilities

interface OrganizationListItem {
  id: string;
  name: string;
}

// Types moved to src/hooks/use-member-permissions.ts for better separation of concerns

interface MemberTableProps {
  organizationIdScope: 'CURRENT_CONTEXT' | 'ALL_WITH_SELECTOR' | string;
  roleFilters?: RoleKey[];
  pagePermissions?: MemberTablePagePermissions; // Made optional for backward compatibility
  permissionModel?: MemberTablePermissionModel;
  businessRules?: MemberTableBusinessRules;
  showOrganizationColumn?: boolean;
  onInviteUser?: () => void;
  tableTitle?: string;
}

interface TablePaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalMembers: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  availablePageSizes?: number[];
}

function TablePagination({
  currentPage,
  totalPages,
  pageSize,
  totalMembers,
  onPageChange,
  onPageSizeChange,
  availablePageSizes = [10, 20, 50, 100],
}: TablePaginationProps) {
  if (totalPages === 0) return null;

  return (
    <div className="flex items-center justify-between px-2 py-4 border-t bg-white dark:bg-gray-800 rounded-b-lg shadow">
      <div className="flex-1 text-sm text-gray-600 dark:text-gray-300">
        Showing {Math.min((currentPage - 1) * pageSize + 1, totalMembers)} - {Math.min(currentPage * pageSize, totalMembers)} of {totalMembers} members
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Rows per page</p>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize.toString()} />
            </SelectTrigger>
            <SelectContent side="top">
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium text-gray-600 dark:text-gray-300">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export const MemberTable = memo(function MemberTable({
  organizationIdScope,
  roleFilters,
  pagePermissions: legacyPagePermissions,
  permissionModel = 'legacy',
  businessRules,
  showOrganizationColumn = true,
  onInviteUser,
  tableTitle = "Members"
}: MemberTableProps) {
  const { userId, orgId } = useAuthContextStore();
  const { organizations: organizationsList, isLoading: isLoadingOrgsList } = useOrganizationsList({});
  const rbac = useRbacPermission();

  // Calculate effective permissions based on permission model
  const pagePermissions = useMemo((): MemberTablePagePermissions => {
    // If legacy pagePermissions are provided, use them (backward compatibility)
    if (legacyPagePermissions) {
      return legacyPagePermissions;
    }

    // Calculate permissions based on permission model
    switch (permissionModel) {
      case 'business-rules':
        if (!businessRules) {
          console.warn('business-rules permission model requires businessRules prop');
          return { canViewProfile: false, canEditRole: false, canChangeStatus: false, canRemoveMember: false, canInvite: false };
        }
        return {
          canViewProfile: businessRules.profileEditMinRole ? rbac.checkPermission({ ruMinRole: businessRules.profileEditMinRole }) : false,
          canEditRole: businessRules.canEditRole === false ? false : (businessRules.roleEditMinRole ? rbac.checkPermission({ ruMinRole: businessRules.roleEditMinRole }) : false),
          canChangeStatus: businessRules.statusChangeMinRole ? rbac.checkPermission({ ruMinRole: businessRules.statusChangeMinRole }) : false,
          canRemoveMember: businessRules.deleteMinRole ? rbac.checkPermission({ rdMinRole: businessRules.deleteMinRole }) : false,
          canInvite: businessRules.inviteMinRole ? rbac.checkPermission({ crMinRole: businessRules.inviteMinRole }) : false,
        };

      case 'role-based':
        if (!businessRules) {
          console.warn('role-based permission model requires businessRules prop');
          return { canViewProfile: false, canEditRole: false, canChangeStatus: false, canRemoveMember: false, canInvite: false };
        }
        return {
          canViewProfile: businessRules.profileEditMinRole ? rbac.checkPermission({ ruMinRole: businessRules.profileEditMinRole }) : false,
          canEditRole: businessRules.roleEditMinRole ? rbac.checkPermission({ ruMinRole: businessRules.roleEditMinRole }) : false,
          canChangeStatus: businessRules.statusChangeMinRole ? rbac.checkPermission({ ruMinRole: businessRules.statusChangeMinRole }) : false,
          canRemoveMember: businessRules.deleteMinRole ? rbac.checkPermission({ rdMinRole: businessRules.deleteMinRole }) : false,
          canInvite: businessRules.inviteMinRole ? rbac.checkPermission({ crMinRole: businessRules.inviteMinRole }) : false,
        };

      case 'full-access':
        return {
          canViewProfile: true,
          canEditRole: true,
          canChangeStatus: true,
          canRemoveMember: true,
          canInvite: true,
        };

      case 'legacy':
      default:
        // Fallback to safe defaults if no legacy permissions provided
        return legacyPagePermissions || { canViewProfile: false, canEditRole: false, canChangeStatus: false, canRemoveMember: false, canInvite: false };
    }
  }, [legacyPagePermissions, permissionModel, businessRules, rbac]);
  const [selectedOrgIdForSelectorState, setSelectedOrgIdForSelectorState] = useState<string>(
    organizationIdScope === 'ALL_WITH_SELECTOR' ? 'all' : orgId || 'all'
  );
  const [selectedMember, setSelectedMember] = useState<OrganizationMemberFull | null>(null);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [currentTableSearchQuery, setCurrentTableSearchQuery] = useState("");

  const hookProps: UseOrganizationMembersProps = {
    organizationIdScope: organizationIdScope,
    initialRoleFilters: roleFilters || [],
    initialSearchQuery: currentTableSearchQuery,
    ...(organizationIdScope === 'ALL_WITH_SELECTOR' && selectedOrgIdForSelectorState && { initialSelectedOrgIdForScope: selectedOrgIdForSelectorState }),
  };

  const {
    members,
    isLoading: isLoadingMembers,
    error,
    organizations,
    availableRoles,
    userRoles,
    updateMemberRole,
    updateMemberStatus,
    removeMember,
    updatingStates,
    currentPage,
    pageSize,
    totalMembers,
    setCurrentPage,
    setPageSize,
    setSearchQuery: setHookSearchQuery,
    setSelectedOrgIdForSelector: setHookSelectedOrgId,
    isRefreshingFromReactivation,
  } = useOrganizationMembersEventBus(hookProps);

  const currentUserId = userId;
  const activeOrganization = useMemo(() => {
    if (!orgId || !organizationsList) return null;
    return organizationsList.find((org: Organization) => org.id === orgId) || null;
  }, [orgId, organizationsList]);

  const isLoading = isLoadingMembers || !currentUserId || isLoadingOrgsList;

  useEffect(() => {
    setHookSearchQuery(currentTableSearchQuery);
  }, [currentTableSearchQuery, setHookSearchQuery]);

  useEffect(() => {
    if (organizationIdScope === 'ALL_WITH_SELECTOR') {
      setHookSelectedOrgId(selectedOrgIdForSelectorState);
    }
  }, [organizationIdScope, selectedOrgIdForSelectorState, setHookSelectedOrgId]);

  useEffect(() => {
    if (organizationIdScope === 'CURRENT_CONTEXT' && orgId) {
    } else if (organizationIdScope === 'ALL_WITH_SELECTOR' && !orgId && selectedOrgIdForSelectorState !== 'all'){
    }
  }, [organizationIdScope, orgId, selectedOrgIdForSelectorState]);

  const orgSelectItems = useMemo(() => organizations as OrganizationListItem[], [organizations]);

  // getAssignableRolesForOrg logic moved to centralized permission hook

  if (error) {
    return <div className="text-center py-4 text-red-500">Error: {error.message}</div>;
  }

  const handleOrgFilterChange = (orgId: string) => {
    setSelectedOrgIdForSelectorState(orgId);
  };

  const handleTableSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentTableSearchQuery(e.target.value);
  };

  const handleRoleChange = async (member: OrganizationMemberFull, newRoleIdStr: string) => {
    if (!currentUserId || !pagePermissions.canEditRole || !activeOrganization) return;
    const updateKey = `${member.org_id}-${member.user_id}`;
    if (updatingStates[updateKey]?.role) return;

    const actingUserRoleInTargetOrg = userRoles.get(member.org_id);
    if (!actingUserRoleInTargetOrg) {
      toastMessages.error("Your permissions for this organization are unclear.");
      return;
    }

    // CRITICAL SECURITY: Use centralized permission check
    const modificationCheck = canModifyUser(actingUserRoleInTargetOrg, member.org_member_role);
    if (!modificationCheck.allowed) {
      toastMessages.error(modificationCheck.reason || "Insufficient permissions to modify this account.");
      return;
    }

    const targetRoleId = parseInt(newRoleIdStr, 10);
    const memberBasicData: OrganizationMemberBasic = { user_id: member.user_id, org_id: member.org_id, org_member_role: member.org_member_role, org_member_is_active: member.org_member_is_active };

    if (!canChangeUserRole(memberBasicData, actingUserRoleInTargetOrg, currentUserId, targetRoleId)) {
      toastMessages.error("Insufficient permissions to change role.");
      return;
    }
    await updateMemberRole(member.org_id, member.user_id, newRoleIdStr);
  };

  const handleStatusChange = async (member: OrganizationMemberFull, newStatus: boolean) => {
    if (!currentUserId || !pagePermissions.canChangeStatus || !activeOrganization) return;
    const updateKey = `${member.org_id}-${member.user_id}`;
    if (updatingStates[updateKey]?.status) return;

    const actingUserRoleInTargetOrg = userRoles.get(member.org_id);
    if (!actingUserRoleInTargetOrg) {
      toastMessages.error("Your permissions for this organization are unclear.");
      return;
    }

    // CRITICAL SECURITY: Use centralized permission check
    const modificationCheck = canModifyUser(actingUserRoleInTargetOrg, member.org_member_role);
    if (!modificationCheck.allowed) {
      toastMessages.error(modificationCheck.reason || "Insufficient permissions to modify this account.");
      return;
    }

    const memberBasicData: OrganizationMemberBasic = { user_id: member.user_id, org_id: member.org_id, org_member_role: member.org_member_role, org_member_is_active: member.org_member_is_active };

    if (!canToggleMemberStatus(memberBasicData, actingUserRoleInTargetOrg, currentUserId)) {
      toastMessages.error("Insufficient permissions to change status.");
      return;
    }
    await updateMemberStatus(member.org_id, member.user_id, newStatus);
  };

  const handleRemoveMemberClick = async (member: OrganizationMemberFull) => {
    if (!currentUserId || !pagePermissions.canRemoveMember || !activeOrganization) return;
    const updateKey = `${member.org_id}-${member.user_id}`;
    if (updatingStates[updateKey]?.remove) return;

    const actingUserRoleInTargetOrg = userRoles.get(member.org_id);
    if (!actingUserRoleInTargetOrg) {
      toastMessages.error("Your permissions for this organization are unclear.");
      return;
    }

    // CRITICAL SECURITY: Use centralized permission check
    const modificationCheck = canModifyUser(actingUserRoleInTargetOrg, member.org_member_role);
    if (!modificationCheck.allowed) {
      toastMessages.error(modificationCheck.reason || "Insufficient permissions to modify this account.");
      return;
    }

    const memberBasicData: OrganizationMemberBasic = { user_id: member.user_id, org_id: member.org_id, org_member_role: member.org_member_role, org_member_is_active: member.org_member_is_active };

    if (!canDeleteMember(memberBasicData, actingUserRoleInTargetOrg, currentUserId)) {
      toastMessages.error("Insufficient permissions to remove member.");
      return;
    }
    await removeMember(member.org_id, member.user_id);
  };

  const handleViewProfile = (member: OrganizationMemberFull) => {
    if (!pagePermissions.canViewProfile) return;
    setSelectedMember(member);
    setIsProfileOpen(true);
  };

  const formatDateSafe = (dateString: string | undefined | null): string => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  const totalPages = Math.ceil(totalMembers / pageSize) || 1;

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-200">{tableTitle}</h2>
          {isRefreshingFromReactivation && (
            <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Refreshing...</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none" />
            <Input
              type="search"
              placeholder="Search members..."
              className="w-full sm:w-[300px] pl-10"
              value={currentTableSearchQuery}
              onChange={handleTableSearchChange}
            />
          </div>
           {organizationIdScope === 'ALL_WITH_SELECTOR' && (
            <Select value={selectedOrgIdForSelectorState} onValueChange={handleOrgFilterChange}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Select Organization" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Organizations</SelectItem>
                {orgSelectItems.map((org) => (
                  <SelectItem key={org.id} value={org.id}>{org.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          {pagePermissions.canInvite && onInviteUser && (
             <Button onClick={onInviteUser}>
                <PlusCircle className="h-4 w-4 mr-2" /> Invite User
             </Button>
          )}
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              {showOrganizationColumn && <TableHead>Organization</TableHead>}
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Joined</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead className="text-right">Delete</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading && members.length === 0 ? (
              <TableSkeleton />
            ) : members.length === 0 ? (
              <TableRow><TableCell colSpan={showOrganizationColumn ? 7 : 6} className="h-24 text-center">No members found.</TableCell></TableRow>
            ) : (
              members.map((member) => {
                const updateKey = `${member.org_id}-${member.user_id}`;
                const isUpdatingRole = updatingStates[updateKey]?.role;
                const isUpdatingStatus = updatingStates[updateKey]?.status;
                const isRemoving = updatingStates[updateKey]?.remove;
                const loggedInUserRoleInThisOrg = userRoles.get(member.org_id);

                // Calculate permissions using centralized utilities (not hooks inside map)
                const memberBasicData: OrganizationMemberBasic = {
                  user_id: member.user_id,
                  org_id: member.org_id,
                  org_member_role: member.org_member_role,
                  org_member_is_active: member.org_member_is_active
                };

                // Use centralized permission check
                const modificationCheck = canModifyUser(loggedInUserRoleInThisOrg, member.org_member_role);
                const assignableRoles = getAssignableRoles(loggedInUserRoleInThisOrg, availableRoles, businessRules?.maxAssignableRole);

                const canEditThisMemberRole = modificationCheck.allowed &&
                  loggedInUserRoleInThisOrg !== undefined &&
                  userId &&
                  canChangeUserRole(memberBasicData, loggedInUserRoleInThisOrg, userId, undefined) &&
                  !!pagePermissions.canEditRole;

                const canChangeThisMemberStatus = modificationCheck.allowed &&
                  loggedInUserRoleInThisOrg !== undefined &&
                  userId &&
                  canToggleMemberStatus(memberBasicData, loggedInUserRoleInThisOrg, userId) &&
                  !!pagePermissions.canChangeStatus;

                const canRemoveThisMember = modificationCheck.allowed &&
                  loggedInUserRoleInThisOrg !== undefined &&
                  userId &&
                  canDeleteMember(memberBasicData, loggedInUserRoleInThisOrg, userId) &&
                  !!pagePermissions.canRemoveMember;

                const isSelf = member.user_id === currentUserId;
                let statusTooltip = '';
                if (!canChangeThisMemberStatus) {
                  if (isSelf) statusTooltip = 'You cannot change your own status.';
                  else statusTooltip = 'Insufficient permissions to change status.';
                }

                return (
                  <TableRow key={`${member.org_id}-${member.user_id}`}>
                    <TableCell>
                      <div
                        className={`flex items-center gap-3 ${pagePermissions.canViewProfile ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md p-2 -m-2' : ''}`}
                        onClick={() => pagePermissions.canViewProfile && handleViewProfile(member)}
                      >
                        <Avatar className="h-9 w-9">
                          <AvatarImage src={member.avatar_url || undefined} alt={member.user_full_name} />
                          <AvatarFallback>{member.user_full_name?.charAt(0)?.toUpperCase() || 'U'}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.user_full_name || 'N/A'}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{member.user_email || 'No email'}</div>
                        </div>
                      </div>
                    </TableCell>
                    {showOrganizationColumn && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {member.organization.org_icon && <Image src={member.organization.org_icon} alt={member.organization.org_name} width={20} height={20} className="rounded-sm"/>}
                          <span className="text-sm">{member.organization.org_name}</span>
                        </div>
                      </TableCell>
                    )}
                    <TableCell>
                      {isUpdatingRole ? (
                        <div className="flex items-center justify-center">
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : canEditThisMemberRole ? (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Badge
                              variant="default"
                              className="bg-[#194852] text-white hover:bg-[#194852]/90 cursor-pointer"
                            >
                              {roleUtils.getRoleName(member.org_member_role as any)}
                            </Badge>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="start">
                            <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuRadioGroup
                              value={member.org_member_role.toString()}
                              onValueChange={(newRoleId) => handleRoleChange(member, newRoleId)}
                            >
                              {assignableRoles.map(role => (
                                <DropdownMenuRadioItem key={role.role_id} value={role.role_id.toString()} disabled={Boolean(isUpdatingRole)}>
                                  {roleUtils.getRoleName(role.role_id as any)}
                                </DropdownMenuRadioItem>
                              ))}
                            </DropdownMenuRadioGroup>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      ) : (
                        <span className="inline-block opacity-50 cursor-not-allowed">
                          <Badge
                            variant="default"
                            className="bg-primary text-primary-foreground pointer-events-none select-none"
                          >
                            {roleUtils.getRoleName(member.org_member_role as any)}
                          </Badge>
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      {isUpdatingStatus ? <Loader2 className="h-4 w-4 animate-spin" /> :
                        <span
                          title={statusTooltip}
                          className={
                            `inline-block ${!canChangeThisMemberStatus ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} `
                          }
                        >
                          <Badge
                            variant={member.org_member_is_active ? "default" : "destructive"}
                            className={`capitalize ${
                              member.org_member_is_active && canChangeThisMemberStatus
                                ? 'bg-[#194852] text-white hover:bg-[#194852]/90'
                                : member.org_member_is_active && !canChangeThisMemberStatus
                                ? 'bg-primary text-primary-foreground'
                                : ''
                            } ${!canChangeThisMemberStatus ? 'pointer-events-none select-none' : ''}`}
                            onClick={() => canChangeThisMemberStatus && handleStatusChange(member, !member.org_member_is_active)}
                          >
                            {member.org_member_is_active ? "Active" : "Inactive"}
                          </Badge>
                        </span>
                      }
                    </TableCell>
                    <TableCell>{formatDateSafe(member.created_at)}</TableCell>
                    <TableCell>{formatDateSafe(member.updated_at)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        disabled={isRemoving || isUpdatingStatus || !canRemoveThisMember || member.org_member_is_active}
                        onClick={() => canRemoveThisMember && !member.org_member_is_active && !isUpdatingStatus && handleRemoveMemberClick(member)}
                        className={`${
                          member.org_member_is_active || !canRemoveThisMember || isUpdatingStatus
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20'
                        }`}
                        title={
                          !canRemoveThisMember
                            ? 'Insufficient permissions to remove member'
                            : isUpdatingStatus
                            ? 'Please wait for status update to complete'
                            : member.org_member_is_active
                            ? 'Member must be inactive to be removed'
                            : 'Remove member'
                        }
                      >
                        {isRemoving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
        {totalMembers > 0 && (
           <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalMembers={totalMembers}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
          />
        )}
      </div>

      {selectedMember && pagePermissions.canViewProfile && (
        <UserProfileDialog
          isOpen={isProfileOpen}
          member={selectedMember}
          onCloseAction={() => setIsProfileOpen(false)}
          onProfileUpdate={() => {
            // Optionally, you might want to trigger a refresh or refetch members
            // if a profile update could affect the table data directly.
            // For now, just closing the dialog.
          }}
        />
      )}
    </div>
  );
});