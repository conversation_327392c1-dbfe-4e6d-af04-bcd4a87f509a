import CountrySelector from "@/components/country-selector";
import FAQSection from "@/components/faq-section";
import { FrontpageHeader } from "@/components/frontpage-header";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import Image from "next/image";

export default function HomePage() {
  return (
    <div className="relative">
      <FrontpageHeader />
      {/* Full width green background with grid */}
      <div className="w-full bg-[#0A2A2A] relative">
        {/* Grid Background */}
        <div
          className="absolute inset-0 opacity-90"
          style={{
            backgroundImage: "url(/images/grid.png)",
            backgroundSize: "800px",
            backgroundRepeat: "repeat",
          }}
        />

        {/* Ellipse Background */}
        <div
          className="absolute left-1/2 top-[70%] -translate-x-1/2 -translate-y-1/2 w-[900px] h-[800px]"
          style={{
            backgroundImage: "url(/images/ellipse.png)",
            backgroundSize: "contain",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            filter: "blur(1px)",
          }}
        />

        {/* Rest of the content positioned above Aurora */}
        <div className="relative z-[2]">
          {/* Content */}
          <div className="relative z-10">
            {/* Hero Section */}
            <div className="w-full">
              <div className="max-w-[1140px] mx-auto px-6 py-36 text-center">
                <h1 className="text-4xl md:text-7xl text-gray-300 leading-tight">
                  <span className="text-[#31707E] animate-text-pulse">G</span>
                  enerate y
                  <span className="text-[#31707E] animate-text-pulse">o</span>ur
                  l<span className="text-[#31707E] animate-text-pulse">e</span>
                  gal d
                  <span className="text-[#31707E] animate-text-pulse">o</span>
                  cuments
                  <br />
                  <span className="text-[#31707E] animate-text-pulse">w</span>
                  ith th
                  <span className="text-[#31707E] animate-text-pulse">
                    e
                  </span>{" "}
                  sp
                  <span className="text-[#31707E] animate-text-pulse">e</span>ed
                  <span className="text-[#31707E] animate-text-pulse"> o</span>f
                  l<span className="text-[#31707E] animate-text-pulse">i</span>
                  ght
                </h1>
              </div>
            </div>

            {/* Feature Cards */}
            <div className="w-full pb-40">
              <div className="max-w-[1140px] mx-auto px-4 grid md:grid-cols-3 gap-6">
                <Button
                  variant="ghost"
                  className="h-auto p-6 bg-[#194852]/50 border border-teal-500/20 hover:bg-[#194852]/70 w-full"
                >
                  <div className="flex items-center gap-6 text-[#377E8E] w-full">
                    <div className="w-12 h-12 rounded-lg border border-teal-500/20 flex items-center justify-center flex-shrink-0">
                      <Send className="w-6 h-6" />
                    </div>
                    <span className="text-lg">Scan documents</span>
                  </div>
                </Button>

                <Button
                  variant="ghost"
                  className="h-auto p-6 bg-[#194852]/50 border border-teal-500/20 hover:bg-[#194852]/70 w-full"
                >
                  <div className="flex items-center gap-6 text-[#377E8E] w-full">
                    <div className="w-12 h-12 rounded-lg border border-teal-500/20 flex items-center justify-center flex-shrink-0">
                      <Send className="w-6 h-6" />
                    </div>
                    <span className="text-lg">Process documents</span>
                  </div>
                </Button>

                <Button
                  variant="ghost"
                  className="h-auto p-6 bg-[#194852]/50 border border-teal-500/20 hover:bg-[#194852]/70 w-full"
                >
                  <div className="flex items-center gap-6 text-[#377E8E] w-full">
                    <div className="w-12 h-12 rounded-lg border border-teal-500/20 flex items-center justify-center flex-shrink-0">
                      <Send className="w-6 h-6" />
                    </div>
                    <span className="text-lg">Enjoy your vacation</span>
                  </div>
                </Button>
              </div>
            </div>
          </div>

          {/* Bottom Gradient */}
          <div className="w-full absolute bottom-0 left-0">
            <Image
              src="/images/bottomGradient.png"
              alt="Bottom gradient"
              width={1440}
              height={120}
              className="w-full h-auto"
              loading="lazy"
            />
          </div>
        </div>
        {/* Wave Divider - Full Width */}
        <div className="w-full relative -mb-[200px] z-10">
          <Image
            src="/images/waves.png"
            alt="Wave divider"
            width={1440}
            height={120}
            className="w-full h-auto"
            loading="lazy"
          />
        </div>
      </div>

      {/* Immigration Section */}
      <div className="bg-white w-full relative pt-32">
        <div className="max-w-[1140px] mx-auto px-6 py-20">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="absolute -right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-[#0A2A2A] rounded-lg flex items-center justify-center">
                <Send className="w-6 h-6 text-white" />
              </div>
              <Image
                src="/images/image.png"
                alt="Immigration Services"
                width={600}
                height={400}
                className="rounded-3xl"
              />
            </div>
            <div className="space-y-6">
              <h2 className="text-4xl font-medium text-[#0A2A2A]">
                Best Immigration & <br />
                Visa Consultation.
              </h2>
              <p className="text-gray-600">
                There are many variations of passages of but the majority have
                in some form, words which don&apos;t look even slightly
                believable of but the majority have suffered majority have in
                some variations of passages
              </p>
              <ul className="space-y-4">
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-6 h-6 rounded-full bg-teal-100 flex items-center justify-center">
                    <Send className="w-3 h-3 text-teal-600" />
                  </div>
                  We strongly support best practice
                </li>
                <li className="flex items-center gap-2 text-gray-600">
                  <div className="w-6 h-6 rounded-full bg-teal-100 flex items-center justify-center">
                    <Send className="w-3 h-3 text-teal-600" />
                  </div>
                  Our operations around the world and across
                </li>
              </ul>
              <Button className="bg-[#0A2A2A] hover:bg-[#0A2A2A]/90">
                Read More
              </Button>
            </div>
          </div>
        </div>
      </div>

      <CountrySelector />
      <FAQSection />
    </div>
  );
}
