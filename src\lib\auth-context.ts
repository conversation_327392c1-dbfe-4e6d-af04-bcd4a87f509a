import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { evaluateRbac } from '@/lib/rbac/rbac-utils'
import { RoleName } from '@/lib/rbac/roles'
import React from 'react'

/**
 * Auth context interface that contains all user authentication and authorization data
 */
export interface AuthContext {
  userId: string
  orgId: string
  orgName: string
  orgRole: string
  orgRoleId: number
  isSuperAdmin: boolean
  isOrgActive: boolean
  isUserActive: boolean
  timestamp: number
}

// Legacy event listeners removed - all events now processed through unified AUTH_CONTEXT_CHANGED system
// See useAuthContextEvents hook for the new unified event handling

/**
 * Get auth context for a user/organization combination
 * @param userId The user ID
 * @param orgId The organization ID (optional - will use the active org if not provided)
 * @returns The auth context or null if not found
 */
export async function resolveUserAuthContext(userId: string, orgId?: string): Promise<AuthContext | null> {
  if (!userId) return null

  const fetchAuthContext = async (): Promise<AuthContext | null> => {
    // Create the Supabase client
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )

    // If no orgId provided, get the active organization from the database using is_current_context
    if (!orgId) {
      const { data: currentContextOrg, error: contextError } = await supabase
        .from('organization_members')
        .select(`
          org_id
        `)
        .eq('user_id', userId)
        .eq('is_current_context', true)
        .single()

      if (!contextError && currentContextOrg) {
        orgId = currentContextOrg.org_id
        console.log(`[resolveUserAuthContext] Retrieved current context org from DB: ${orgId}`)
      } else if (contextError) {
        console.error('[resolveUserAuthContext] Error getting current context:', contextError)
      }
    }

    // If we still don't have an orgId, initialize a context
    if (!orgId) {
      try {
        console.log('[resolveUserAuthContext] No context found, initializing one')
        const { data: initOrgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
          p_user_id: userId
        })

        if (initError || !initOrgId) {
          console.error('[resolveUserAuthContext] Failed to initialize context:', initError || 'No organization returned')
          return null
        }

        orgId = initOrgId
        console.log(`[resolveUserAuthContext] Initialized context to organization: ${orgId}`)
      } catch (err) {
        console.error('[resolveUserAuthContext] Error initializing context:', err)
        return null
      }
    }

    // Get organization and user's role from the database
    const { data: organization, error } = await supabase
      .from('organizations')
      .select(`
        id,
        org_name,
        is_active,
        organization_members!inner(
          org_member_role,
          org_member_is_active
        )
      `)
      .eq('id', orgId)
      .eq('organization_members.user_id', userId)
      .single()

    if (error || !organization) {
      console.error('Error fetching organization data:', error)
      return null
    }

    // Extract the role ID from the organization
    const roleId = organization.organization_members[0].org_member_role

    // Check if the user is a superadmin
    const isSuperAdmin = evaluateRbac(roleId, { rRoles: ["superAdmin"] })

    // Build the complete context
    const authContext: AuthContext = {
      userId,
      orgId: organization.id,
      orgName: organization.org_name,
      orgRole: RoleName[roleId as keyof typeof RoleName] || 'unknown',
      orgRoleId: roleId,
      isSuperAdmin,
      isOrgActive: organization.is_active,
      isUserActive: organization.organization_members[0].org_member_is_active,
      timestamp: Date.now()
    }

    return authContext
  }

  return fetchAuthContext(); // ALWAYS CALL THIS DIRECTLY
}

/**
 * Get auth context with background refresh if cache is stale
 * @param userId The user ID
 * @param orgId The organization ID (optional)
 * @returns The auth context or null if not found
 */
export async function getAuthContextWithBackgroundRefresh(userId: string, orgId?: string): Promise<AuthContext | null> {
  if (!userId) return null

  // Directly call resolveUserAuthContext as memoryCache is removed for AuthContext
  return resolveUserAuthContext(userId, orgId);
}

/**
 * Get all organizations for a user with their roles
 * @param userId The user ID
 * @returns Array of organizations with role information
 */
export const getUserOrganizations = React.cache(async (userId: string) => {
  if (!userId) return []

  const cookieStore = await cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  // Log the beginning of the function
  console.log(`[getUserOrganizations] Getting organizations for user ${userId}`)

  // Define the type for the organization member records with proper joined structure
  interface OrgMemberRecord {
    org_id: string
    org_member_role: number
    org_member_is_active: boolean
    is_default_org: boolean
    is_current_context: boolean // Added to match the select query
    organizations: {
      id: string
      org_name: string
      is_active: boolean
      org_icon: string
    }
  }

  // Execute a direct JOIN query that includes both tables
  const { data, error } = await supabase
    .from('organization_members')
    .select(`
      org_id,
      org_member_role,
      org_member_is_active,
      is_default_org,
      is_current_context,
      organizations!inner (
        id,
        org_name,
        is_active,
        org_icon
      )
    `)
    .eq('user_id', userId)
    .order('is_default_org', { ascending: false })

  if (error) {
    console.error('[getUserOrganizations] Error fetching user organizations:', error)
    return []
  }

  // Cast the data to the correct type
  const typedData = data as unknown as OrgMemberRecord[]

  // Log the raw data to debug
  // console.log(`[getUserOrganizations] Retrieved ${typedData?.length || 0} organizations`,
  //   typedData ? typedData.map(item => ({
  //     org_id: item.org_id,
  //     org_name: item.organizations?.org_name || 'Missing name',
  //     org_member_role: item.org_member_role
  //   })) : 'No data'
  // )

  // Transform the data into the expected format
  const organizations = typedData ? typedData.map(item => {
    // Extract the organization data
    const org = item.organizations

    if (!org || !org.org_name) {
      console.warn(`[getUserOrganizations] Missing organization data for org_id ${item.org_id}`)
    }

    return {
      id: item.org_id,
      name: org.org_name || `Organization ${item.org_id.substring(0, 8)}`,
      org_icon: org.org_icon || '',
      role: RoleName[item.org_member_role as keyof typeof RoleName] || 'unknown',
      roleId: item.org_member_role,
      isActive: org.is_active,
      isDefault: item.is_default_org,
      org_member_role: item.org_member_role,
      org_member_is_active: item.org_member_is_active,
      is_current_context: item.is_current_context // Include is_current_context in the mapped object
    }
  }) : []

  // Log the transformed data
  // console.log(`[getUserOrganizations] Transformed ${organizations.length} organizations:`,
  //   organizations.map(org => ({
  //     id: org.id,
  //     name: org.name,
  //     role: org.role
  //   }))
  // )

  return organizations
})