"use client";

import { useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Card } from "@/components/ui/card";

const continents = [
  "Europe",
  "North America",
  "Asea",
  "Latin America",
  "Oceania",
  "Africa",
  "Antarctica",
];

const countries = [
  { name: "Canada", code: "CA", continent: "North America" },
  { name: "Belgium", code: "BE", continent: "Europe" },
  { name: "Denmark", code: "DK", continent: "Europe" },
  { name: "Australia", code: "AU", continent: "Oceania" },
  { name: "France", code: "FR", continent: "Europe" },
  { name: "Germany", code: "DE", continent: "Europe" },
  { name: "Greece", code: "GR", continent: "Europe" },
  { name: "Hungary", code: "HU", continent: "Europe" },
  { name: "Iceland", code: "IS", continent: "Europe" },
  { name: "Ireland", code: "IE", continent: "Europe" },
  { name: "Italy", code: "IT", continent: "Europe" },
  { name: "Luxembourg", code: "LU", continent: "Europe" },
];

export default function CountrySelector() {
  const [selectedContinent, setSelectedContinent] = useState("Europe");

  return (
    <section className="bg-[#F8FAFC]">
      <div className="container mx-auto px-4 py-20">
        <div className="space-y-8 max-w-[1140px] mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <h1 className="text-4xl font-medium text-[#0A2A2A]">
              Make Your Choice for the <br />
              <span className="text-[#194852]">Preferred Nation</span>
            </h1>
            <p className="text-lg text-[#64748B] self-center">
              Choosing the ideal destination for immigration is a pivotal
              decision that can shape the trajectory of your Vacation
            </p>
          </div>

          <div className="bg-[#F8FAFC] -mx-4 px-4  rounded-3xl">
            <div className="flex gap-2 overflow-x-auto py-4">
              {continents.map((continent) => (
                <button
                  key={continent}
                  onClick={() => setSelectedContinent(continent)}
                  className={cn(
                    "px-8 py-3 rounded-lg whitespace-nowrap transition-all font-medium",
                    selectedContinent === continent
                      ? "bg-white text-[#1E293B] shadow-lg shadow-black/[0.03]"
                      : "text-[#64748B] hover:bg-white/50"
                  )}
                >
                  {continent}
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {countries
              .filter((country) => country.continent === selectedContinent)
              .map((country) => (
                <Card
                  key={country.name}
                  className="p-6 flex items-center gap-4 cursor-pointer hover:shadow-lg transition-shadow bg-white border-transparent"
                >
                  <div className="relative w-10 h-10 overflow-hidden rounded-full">
                    <Image
                      src={`https://flagsapi.com/${country.code}/flat/64.png`}
                      alt={`${country.name} flag`}
                      width={40}
                      height={40}
                      className="object-cover"
                    />
                  </div>
                  <span className="font-medium text-[#1E293B] text-lg">
                    {country.name}
                  </span>
                </Card>
              ))}
          </div>
        </div>
      </div>
    </section>
  );
}
