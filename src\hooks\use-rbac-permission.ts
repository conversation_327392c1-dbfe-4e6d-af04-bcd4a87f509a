"use client";

import { useMemo } from 'react';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { useOrganizationsList } from '@/hooks/use-organizations-list';
import type { Organization } from '@/types/organization';
import type { OrganizationMemberBasic } from '@/types/organization/OrganizationMember';
import { evaluateRbac } from '@/lib/rbac/rbac-utils';
import { canToggleMemberStatus, canChangeUserRole, canDeleteMember } from '@/lib/permission-utils';
import { RoleId } from '@/lib/rbac/roles';
import type { RbacConditions, PermissionResult, RoleKey, RoleId as RoleIdType } from '@/types/lib/rbac';
import { roleHelpers, createPermissionResult, roleComparison, roleUtils } from '@/lib/rbac/role-utils';

interface UseRbacPermissionProps {
  orgContext?: 'any' | 'specific' | string; // string for specific org ID
  targetOrgId?: string;
}

/**
 * Enhanced RBAC permission hook - the primary client-side permission checking interface
 * Consolidates functionality from use-permissions.ts and use-centralized-permissions.ts
 *
 * @param props Optional configuration for organization context
 * @returns Object with permission checking methods and context information
 */
export function useRbacPermission(props?: UseRbacPermissionProps) {
  const {
    roleId: currentOrgRoleId,
    orgId: currentOrgId,
    userId,
    isLoading: storeIsLoading
  } = useAuthContextStore();
  const { organizations: organizationsList } = useOrganizationsList({});

  const orgContext = props?.orgContext;
  const targetOrgId = props?.targetOrgId;

  return useMemo(() => {
    // Return loading state if store is still hydrating
    if (storeIsLoading) {
      return {
        checkPermission: () => false,
        roleCheck: () => false,
        canView: () => false,
        canCreate: () => false,
        canUpdate: () => false,
        canDelete: () => false,
        canToggleUserStatus: () => false,
        canChangeUserRole: () => false,
        canDeleteUser: () => false,
        isSuperAdmin: () => false,
        isSupportAdmin: () => false,
        isOrgAdmin: () => false,
        isOrgMember: () => false,
        isOrgClient: () => false,
        isAtLeastSupportAdmin: () => false,
        isAtLeastOrgAdmin: () => false,
        isAtLeastOrgMember: () => false,
        currentOrgRoleId: null,
        currentOrgId: null,
        userId: null,
        isLoading: true
      };
    }

    const checkPermission = (conditions: RbacConditions): boolean => {
      let effectiveRoleId: number | null = null;

      if (orgContext === "any" || !orgContext) {
        // SECURITY FIX: For 'any' context, check if user has the required role in *any* of their organizations
        // Find the highest role across all organizations that satisfies the conditions
        if (organizationsList && organizationsList.length > 0) {
          const hasPermissionInAnyOrg = organizationsList.some((org: Organization) => {
            const roleId = org.org_member_role;
            if (roleId === null || roleId === undefined) return false;
            return evaluateRbac(roleId, conditions);
          });

          if (hasPermissionInAnyOrg) {
            // Use the current role for consistency, but we've verified permission exists somewhere
            effectiveRoleId = currentOrgRoleId;
          } else {
            return false; // No permission in any organization
          }
        } else {
          // Fallback to current role if no organizations list available
          effectiveRoleId = currentOrgRoleId;
        }
      } else if (orgContext === 'specific' && targetOrgId) {
        const targetMembership = organizationsList?.find((org: Organization) => org.id === targetOrgId);
        effectiveRoleId = targetMembership?.org_member_role ?? null;
      } else if (typeof orgContext === 'string') {
        // Assumes orgContext is a specific org ID
        const targetMembership = organizationsList?.find((org: Organization) => org.id === orgContext);
        effectiveRoleId = targetMembership?.org_member_role ?? null;
      } else {
        // Default to current context if props are not sufficient
        effectiveRoleId = currentOrgRoleId;
      }

      if (effectiveRoleId === null || effectiveRoleId === undefined) {
        return false; // No role, no permission
      }
      return evaluateRbac(effectiveRoleId, conditions);
    };

    // Determine the effective role ID for current context
    const roleId = currentOrgRoleId ?? RoleId.ORGCLIENT;
    const orgId = currentOrgId;

    return {
      // --- Core Permission Checking ---

      /**
       * Primary permission check method using RBAC conditions
       * @param conditions RBAC conditions to evaluate
       */
      checkPermission,

      /**
       * Alias for checkPermission for backward compatibility
       * @param rbacConditions RBAC conditions to check against
       */
      roleCheck: (rbacConditions: RbacConditions): boolean => {
        // If we need to use the current organization context
        if (rbacConditions.orgContext === 'current' && !rbacConditions.orgId && orgId) {
          return evaluateRbac(roleId, { ...rbacConditions, orgId });
        }
        return evaluateRbac(roleId, rbacConditions);
      },

      // --- CRUD Operation Checks ---

      /** Checks if user can view a resource */
      canView: (rbacConditions: Omit<RbacConditions, 'rMinRole' | 'rRoles'> = {}): boolean =>
        evaluateRbac(roleId, { rMinRole: "orgClient", ...rbacConditions }),

      /** Checks if user can create a resource */
      canCreate: (rbacConditions: Omit<RbacConditions, 'crMinRole' | 'crRoles'> = {}): boolean =>
        evaluateRbac(roleId, { crMinRole: "orgMember", ...rbacConditions }),

      /** Checks if user can update a resource */
      canUpdate: (rbacConditions: Omit<RbacConditions, 'ruMinRole' | 'ruRoles'> = {}): boolean =>
        evaluateRbac(roleId, { ruMinRole: "orgMember", ...rbacConditions }),

      /** Checks if user can delete a resource */
      canDelete: (rbacConditions: Omit<RbacConditions, 'rdMinRole' | 'rdRoles'> = {}): boolean =>
        evaluateRbac(roleId, { rdMinRole: "orgAdmin", ...rbacConditions }),

      // --- Enhanced Permission Checks with Detailed Results ---

      /**
       * Enhanced permission check that returns detailed result with context
       * @param conditions RBAC conditions to evaluate
       * @param operation Optional operation name for context
       */
      checkPermissionDetailed: (conditions: RbacConditions, operation?: string): PermissionResult => {
        if (!roleHelpers.isValidRoleId(roleId)) {
          const context: { orgId?: string; operation?: string; resource?: string; } = {};
          if (currentOrgId) context.orgId = currentOrgId;
          if (operation) context.operation = operation;
          return createPermissionResult.invalidRole(roleId, Object.keys(context).length > 0 ? context : undefined);
        }

        const allowed = evaluateRbac(roleId, conditions);

        if (allowed) {
          const context: { orgId?: string; operation?: string; resource?: string; } = {};
          if (currentOrgId) context.orgId = currentOrgId;
          if (operation) context.operation = operation;
          return createPermissionResult.allowed(
            `Permission granted for ${operation || 'operation'}`,
            Object.keys(context).length > 0 ? context : undefined
          );
        }

        // Determine the reason for denial
        let requiredRole: RoleKey | undefined;
        if (conditions.rMinRole) requiredRole = conditions.rMinRole;
        else if (conditions.crMinRole) requiredRole = conditions.crMinRole;
        else if (conditions.ruMinRole) requiredRole = conditions.ruMinRole;
        else if (conditions.rdMinRole) requiredRole = conditions.rdMinRole;

        if (requiredRole) {
          const context: { orgId?: string; operation?: string; resource?: string; } = {};
          if (currentOrgId) context.orgId = currentOrgId;
          if (operation) context.operation = operation;
          return createPermissionResult.insufficientRole(
            roleId as RoleIdType,
            requiredRole,
            Object.keys(context).length > 0 ? context : undefined
          );
        }

        const context: { orgId?: string; operation?: string; resource?: string; } = {};
        if (currentOrgId) context.orgId = currentOrgId;
        if (operation) context.operation = operation;
        return createPermissionResult.denied(
          `Permission denied for ${operation || 'operation'}`,
          roleId as RoleIdType,
          undefined,
          Object.keys(context).length > 0 ? context : undefined
        );
      },

      /**
       * Type-safe role comparison methods
       */
      roleComparison: {
        /** Check if user's role is at least the specified role */
        isAtLeast: (requiredRole: RoleKey): boolean => {
          if (!roleHelpers.isValidRoleId(roleId)) return false;
          const requiredRoleId = roleHelpers.keyToId(requiredRole);
          return roleComparison.isAtLeast(roleId as RoleIdType, requiredRoleId);
        },

        /** Check if user's role is higher than the specified role */
        isHigherThan: (compareRole: RoleKey): boolean => {
          if (!roleHelpers.isValidRoleId(roleId)) return false;
          const compareRoleId = roleHelpers.keyToId(compareRole);
          return roleComparison.isHigherThan(roleId as RoleIdType, compareRoleId);
        },

        /** Check if user's role is exactly the specified role */
        isExactly: (compareRole: RoleKey): boolean => {
          if (!roleHelpers.isValidRoleId(roleId)) return false;
          const compareRoleId = roleHelpers.keyToId(compareRole);
          return roleComparison.isExactly(roleId as RoleIdType, compareRoleId);
        },
      },

      /**
       * Role information and utilities
       */
      roleInfo: {
        /** Get the current role key */
        getCurrentRoleKey: (): RoleKey | null => {
          return roleHelpers.isValidRoleId(roleId) ? roleHelpers.idToKey(roleId as RoleIdType) : null;
        },

        /** Get human-readable role name */
        getCurrentRoleName: (): string | null => {
          return roleHelpers.isValidRoleId(roleId) ? roleUtils.getRoleName(roleId as RoleIdType) : null;
        },

        /** Get role description */
        getCurrentRoleDescription: (): string | null => {
          return roleHelpers.isValidRoleId(roleId) ? roleUtils.getRoleDescription(roleId as RoleIdType) : null;
        },

        /** Get hierarchy level (1 = highest, 6 = lowest) */
        getHierarchyLevel: (): number | null => {
          return roleHelpers.isValidRoleId(roleId) ? roleComparison.getHierarchyLevel(roleId as RoleIdType) : null;
        },
      },

      // --- Domain-Specific Permission Helpers ---

      /**
       * Checks if the user can toggle the specified member's status
       * Uses centralized domain-specific permission logic
       */
      canToggleUserStatus: (member: OrganizationMemberBasic): boolean =>
        userId ? canToggleMemberStatus(member, roleId, userId) : false,

      /**
       * Checks if the user can change the specified member's role
       * Uses centralized domain-specific permission logic
       */
      canChangeUserRole: (member: OrganizationMemberBasic, newRoleId?: number): boolean =>
        userId ? canChangeUserRole(member, roleId, userId, newRoleId) : false,

      /**
       * Checks if the user can delete the specified member
       * Uses centralized domain-specific permission logic
       */
      canDeleteUser: (member: OrganizationMemberBasic): boolean =>
        userId ? canDeleteMember(member, roleId, userId) : false,

      // --- Role-Based Checks ---

      /** Checks if the user's role is exactly SuperAdmin */
      isSuperAdmin: (): boolean => evaluateRbac(roleId, { rRoles: ["superAdmin"] }),

      /** Checks if the user's role is exactly SupportAdmin */
      isSupportAdmin: (): boolean => evaluateRbac(roleId, { rRoles: ["supportAdmin"] }),

      /** Checks if the user's role is exactly OrgAdmin */
      isOrgAdmin: (): boolean => evaluateRbac(roleId, { rRoles: ["orgAdmin"] }),

      /** Checks if the user's role is exactly OrgMember */
      isOrgMember: (): boolean => evaluateRbac(roleId, { rRoles: ["orgMember"] }),

      /** Checks if the user's role is exactly OrgClient */
      isOrgClient: (): boolean => evaluateRbac(roleId, { rRoles: ["orgClient"] }),

      // --- Hierarchical Role Checks ---

      /** Checks if the user's role is SuperAdmin or SupportAdmin */
      isAtLeastSupportAdmin: (): boolean => evaluateRbac(roleId, { rMinRole: "supportAdmin" }),

      /** Checks if the user's role is SuperAdmin, SupportAdmin, or OrgAdmin */
      isAtLeastOrgAdmin: (): boolean => evaluateRbac(roleId, { rMinRole: "orgAdmin" }),

      /** Checks if the user's role is SuperAdmin, SupportAdmin, OrgAdmin, or OrgMember */
      isAtLeastOrgMember: (): boolean => evaluateRbac(roleId, { rMinRole: "orgMember" }),

      // --- Context Information ---

      /** Current user's role ID in the active organization */
      currentOrgRoleId,

      /** Current active organization ID */
      currentOrgId,

      /** Current user ID */
      userId,

      /** Whether the auth store is still loading */
      isLoading: false
    };
  }, [currentOrgRoleId, organizationsList, orgContext, targetOrgId, currentOrgId, userId, storeIsLoading]);
}