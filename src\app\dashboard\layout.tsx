import React from "react";
import { headers } from "next/headers";
import DashboardLayoutClient from "./DashboardLayoutClient";
import { createClient } from "@/lib/supabase/server";
import { getUserOrganizations } from "@/lib/auth-context";
import { ClientDashboardShell } from "@/components/dashboard/client-dashboard-shell";

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get headers set by middleware with auth context
  const headersList = await headers();
  const authUserId = headersList.get('x-auth-user-id');
  const authOrgId = headersList.get('x-auth-org-id');
  const authIsSuperAdmin = headersList.get('x-auth-is-superadmin') === 'true';
  const authIsOrgActive = headersList.get('x-auth-is-org-active') === 'true';
  const authIsUserActive = headersList.get('x-auth-is-user-active') === 'true';
  const authOrgRoleId = headersList.get('x-auth-org-role-id');

  // Log middleware context received in headers
  console.log('[DashboardLayout] Context from middleware headers:', {
    authUserId,
    authOrgId,
    authIsSuperAdmin,
    authIsOrgActive,
    authIsUserActive,
    authOrgRoleId,
  });

  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-red-500">Error: Unable to authenticate user</p>
      </div>
    );
  }

  // Fetch user profile including avatar_url
  const { data: profile } = await supabase
    .from("profiles")
    .select("avatar_url, full_name, email")
    .eq("id", user.id)
    .single();

  // Fetch all user organizations with necessary details in one query
  const organizations = await getUserOrganizations(user.id);

  // Find the active organization based on is_current_context
  let activeOrganization = organizations.find(org => org.is_current_context) || null;

  // Fallback: if no active organization found, use the first one if available
  if (!activeOrganization && organizations.length > 0) {
    activeOrganization = organizations[0];
    console.log('[DashboardLayout] No active organization found, using first available:', activeOrganization.id);
    // Note: In a real app, you might want to update the user's context in the DB here
  } else if (!activeOrganization) {
    console.log('[DashboardLayout] No organizations available for user');
    // Render a message indicating no organizations
    return (
      <div className="flex items-center justify-center h-screen">
        <p>No organizations available. Please join or create one.</p>
      </div>
    );
  }

  // Determine authorization status values from activeOrganization state
  // but trust the middleware for actual page access decisions
  const userRoleInActiveOrgId = authOrgRoleId ? parseInt(authOrgRoleId, 10) : 0; // Default to 0 if header is missing
  
  // Log the organization we're rendering for
  console.log('[DashboardLayout] Rendering with organization:', {
    orgId: activeOrganization.id,
    orgName: activeOrganization.name,
    orgActive: activeOrganization.isActive,
    userRole: userRoleInActiveOrgId,
  });

  // Prepare the initial context for the client shell
  const initialContext = {
    userId: authUserId,
    orgId: authOrgId,
    roleId: userRoleInActiveOrgId,
    isUserActiveInOrg: authIsUserActive,
    isOrgActive: authIsOrgActive,
    isSuperAdmin: authIsSuperAdmin,
    userEmail: user.email || profile?.email,
    userFullName: profile?.full_name,
    avatarUrl: profile?.avatar_url,
    activeOrgName: activeOrganization.name
  };

  return (
    <ClientDashboardShell 
      initialContext={initialContext}
      initialOrganizations={organizations}
    >
      <DashboardLayoutClient
        user={user}
        userRole={userRoleInActiveOrgId}
        avatarUrl={profile?.avatar_url ?? null}
        organizations={organizations}
        activeOrganization={activeOrganization}
      >
        {children}
      </DashboardLayoutClient>
    </ClientDashboardShell>
  );
}
