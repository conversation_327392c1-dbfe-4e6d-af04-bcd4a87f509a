// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import type { OrganizationDataChangedEvent, OrganizationActiveStatusChangedEvent } from '@/lib/eventTypes'

let isRegistered = false

/**
 * Listen for raw db:organizations:updated events and emit organization:statusChanged events.
 */
export function registerOrganizationStatusInterpreter() {
  if (isRegistered) {
    return
  }
  isRegistered = true

  emitter.on('db:organizations:updated', (event: OrganizationDataChangedEvent) => {
    const { orgId, changes, oldRecord, timestamp } = event

    if (!orgId || !changes || typeof changes.is_active === 'undefined') {
      // The 'is_active' field was not part of this specific update, or essential data is missing.
      return
    }

    const newIsActive = changes.is_active as boolean
    const oldIsActive = oldRecord?.is_active as boolean | undefined

    // Only emit if the status actually changed
    // And oldIsActive is defined (meaning we have a previous state to compare against from oldRecord)
    if (typeof oldIsActive !== 'undefined' && newIsActive !== oldIsActive) {
      // Emitting an event that signals a change in an organization's active status.
      // Consider if a new event type like `OrganizationActiveStatusChangedEvent` is better than reusing/aliasing MemberStatusChangedEvent.
      // For now, using a structure similar to MemberStatusChangedEvent for consistency in payload if applicable.
      // A dedicated type `OrganizationActiveStatusChangedEvent { orgId: string; isActive: boolean; oldIsActive: boolean; timestamp: number }` would be clearer.
      // Let's define and use a more appropriate event type for clarity.
      
      const statusChangeEvent: OrganizationActiveStatusChangedEvent = {
        orgId,
        isActive: newIsActive,
        oldIsActive: oldIsActive,
        timestamp,
      }
      clientLog(`[EventBus] Organization active status changed for org ${orgId} to ${newIsActive} (was ${oldIsActive})`)
      emitter.emit('organization:statusChanged', statusChangeEvent) 
    }
  })
} 