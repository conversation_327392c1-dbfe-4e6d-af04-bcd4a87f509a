// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import { AUTH_CONTEXT_CHANGED } from '../constants'
import { useAuthContextStore } from '@/stores/useAuthContextStore'
import type { OrganizationDataChangedEvent, AuthEvent } from '@/lib/eventTypes'

let isRegistered = false

/**
 * Listen for raw db:organizations:updated events and emit organization:statusChanged events.
 */
export function registerOrganizationStatusInterpreter() {
  if (isRegistered) {
    return
  }
  isRegistered = true

  emitter.on('db:organizations:updated', (event: OrganizationDataChangedEvent) => {
    const { orgId, changes, oldRecord, timestamp } = event

    if (!orgId || !changes || typeof changes.is_active === 'undefined') {
      // The 'is_active' field was not part of this specific update, or essential data is missing.
      return
    }

    const newIsActive = changes.is_active as boolean
    const oldIsActive = oldRecord?.is_active as boolean | undefined

    // Only emit if the status actually changed
    // And oldIsActive is defined (meaning we have a previous state to compare against from oldRecord)
    if (typeof oldIsActive !== 'undefined' && newIsActive !== oldIsActive) {
      // Only emit AUTH_CONTEXT_CHANGED for the current organization context
      const { orgId: currentOrgId, userId: currentUserId } = useAuthContextStore.getState();
      if (orgId !== currentOrgId || !currentUserId) {
        return; // Event is not for current organization context or no current user
      }

      const authEvent: AuthEvent = {
        userId: currentUserId,
        orgId,
        reason: `Organization status changed to ${newIsActive ? 'active' : 'inactive'}`,
        data: {
          isActive: newIsActive,
          oldIsActive: oldIsActive
        },
        timestamp,
      }

      clientLog(`[EventBus] Organization active status changed for org ${orgId} to ${newIsActive} (was ${oldIsActive})`)
      console.log('[OrganizationStatusInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
      emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
    }
  })
} 