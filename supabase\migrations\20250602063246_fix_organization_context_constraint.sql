-- Fix organization context function to respect unique constraint
-- The unique constraint idx_one_active_context_per_user requires that we
-- set the old context to false BEFORE setting the new context to true

CREATE OR REPLACE FUNCTION public.set_user_organization_context(
  p_user_id UUID,
  p_org_id UUID
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_current_context_org_id UUID;
BEGIN
  -- Check if the requested org is already the current context
  SELECT org_id INTO v_current_context_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  LIMIT 1;

  -- If the requested org is already the current context, do nothing
  IF v_current_context_org_id = p_org_id THEN
    RETURN;
  END IF;

  -- STEP 1: First, set the old context to false (if exists)
  -- This must happen BEFORE setting the new context to true due to unique constraint
  IF v_current_context_org_id IS NOT NULL THEN
    UPDATE public.organization_members
    SET is_current_context = false
    WHERE user_id = p_user_id
    AND org_id = v_current_context_org_id;
  END IF;

  -- STEP 2: Then set the new context to true
  -- This happens after the old context is cleared to avoid constraint violation
  UPDATE public.organization_members
  SET is_current_context = true
  WHERE user_id = p_user_id
  AND org_id = p_org_id;
END;
$$;

-- Grant access to authenticated users
GRANT EXECUTE ON FUNCTION public.set_user_organization_context(UUID, UUID) TO authenticated;