/**
 * Core organization member interface
 * Represents the essential membership data
 */
export interface OrganizationMember {
  // Primary keys and core data
  org_id: string;
  user_id: string;
  org_member_role: number;
  org_member_is_active: boolean;
  is_default_org: boolean;
  
  // Metadata
  created_at?: string;
  updated_at?: string;
  org_member_updated_by?: string | null;
}

/**
 * Minimal organization member data used for permission checks
 */
export interface OrganizationMemberBasic {
  org_id: string;
  user_id: string;
  org_member_role: number;
  org_member_is_active: boolean;
} 