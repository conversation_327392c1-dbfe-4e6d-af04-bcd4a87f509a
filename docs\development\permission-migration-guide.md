# Permission System Migration Guide

## Overview

This guide documents the migration from the legacy permission system to the modern centralized RBAC system. This migration was completed as part of the security update initiative to standardize permission checking across the application.

## Migration Summary

### Before Migration
- **Multiple permission systems** with inconsistent patterns
- **Legacy `checkPermission()`** function with custom caching
- **Mixed API permission patterns** (3 different approaches)
- **Hardcoded role IDs** in some API routes
- **Redundant permission services** with overlapping functionality

### After Migration
- **Unified RBAC system** with consistent patterns
- **Centralized `checkRbacPermission()`** for all server-side checks
- **Standardized API permission patterns** across all routes
- **Role-based access control** using role keys instead of IDs
- **Clean separation** between server and client permission services

## Migration Phases

### Phase 1: Critical Security Fixes ✅ COMPLETED
1. **Fixed orgContext validation** in permissions-server.ts
2. **Fixed client-side "any" context evaluation** in useRbacPermission

### Phase 2A: API Security Standardization ✅ COMPLETED
1. **Fixed hardcoded role ID** in user-profile API
2. **Standardized organizations delete API** permission checking
3. **Migrated admin invites page** to centralized RBAC
4. **Fixed dropdown role filtering** edge case

### Phase 2B: Permission Service Consolidation ✅ COMPLETED
1. **Cleaned up legacy permission service** (removed unused exports)
2. **Fixed developer organizations page** consistency
3. **Added deprecation warnings** to legacy functions

## API Migration Patterns

### Before: Inconsistent Patterns

#### Pattern A: Hardcoded Role IDs (DANGEROUS)
```typescript
// ❌ OLD - Hardcoded role ID
const { data: superAdminCheck } = await supabase
  .from('organization_members')
  .select('org_member_role')
  .eq('user_id', user.id)
  .eq('org_member_role', 1) // SuperAdmin role
```

#### Pattern B: Direct evaluateRbac (INCONSISTENT)
```typescript
// ❌ OLD - Direct evaluateRbac usage
if (!evaluateRbac(orgMember.org_member_role, { rRoles: ["superAdmin"] })) {
  return new NextResponse('Forbidden', { status: 403 })
}
```

#### Pattern C: Legacy checkPermission (DEPRECATED)
```typescript
// ❌ OLD - Legacy permission service
const hasAccess = await checkPermission(user.id, { 
  rRoles: ["superAdmin"],
  orgContext: "any"
});
```

### After: Unified Pattern

#### Centralized RBAC (RECOMMENDED)
```typescript
// ✅ NEW - Centralized RBAC permission checking
const hasAccess = await checkRbacPermission(
  { rRoles: ["superAdmin"], orgContext: "any" },
  { silentFail: true }
)

if (!hasAccess) {
  return new NextResponse('Forbidden', { status: 403 })
}
```

## Route Migration Patterns

### Before: Mixed Patterns
```typescript
// ❌ OLD - Manual auth check
const { data: { user }, error: authUserError } = await supabase.auth.getUser();
if (authUserError || !user) {
  return <Alert>Authentication Error</Alert>
}
```

### After: Consistent Wrapper
```typescript
// ✅ NEW - Consistent withRbacPermission wrapper
export default withRbacPermission(
  { rRoles: ["superAdmin", "supportAdmin"] },
  { redirectTo: "/dashboard" }
)(DeveloperOrganizationsPage);
```

## Component Migration Patterns

### Before: Hardcoded Permissions
```typescript
// ❌ OLD - Hardcoded permissions
const clientPagePermissions = {
  canViewProfile: false,
  canEditRole: false,
  canEditStatus: false,
  canDelete: false
};
```

### After: Dynamic RBAC
```typescript
// ✅ NEW - Dynamic RBAC-based permissions
const rbac = useRbacPermission();
const clientPagePermissions = {
  canViewProfile: rbac.canUpdate({ ruMinRole: "orgMember" }),
  canEditRole: false, // Business rule: No role changes allowed
  canEditStatus: rbac.canUpdate({ ruMinRole: "orgMember" }),
  canDelete: rbac.canDelete({ rudMinRole: "orgAdmin" })
};
```

## Permission Service Architecture

### Server-Side Services
| Service | Purpose | Status | Usage |
|---------|---------|--------|-------|
| `permissions-server.ts` | Modern RBAC with org context | ✅ PRIMARY | All server components, API routes |
| `permissions-service.ts` | Legacy caching service | ⚠️ DEPRECATED | Minimal usage, marked for removal |

### Client-Side Services
| Service | Purpose | Status | Usage |
|---------|---------|--------|-------|
| `permissions-service-client.ts` | Client-side RBAC with store integration | ✅ ACTIVE | Client components via hooks |
| `use-rbac-permission.ts` | React hook wrapper | ✅ PRIMARY | All client components |

## Migration Checklist

### For New API Routes
- [ ] Use `checkRbacPermission()` for permission checking
- [ ] Use role keys (e.g., "superAdmin") instead of role IDs
- [ ] Include appropriate `orgContext` ("current" or "any")
- [ ] Use CRUD-specific properties (crRoles, ruRoles, rdRoles)
- [ ] Handle `silentFail` option appropriately

### For New Pages/Components
- [ ] Use `withRbacPermission()` wrapper for server components
- [ ] Use `useRbacPermission()` hook for client components
- [ ] Apply business rules from `permission-utils.ts`
- [ ] Use `<Restricted>` component for conditional rendering

### For Existing Code Updates
- [ ] Replace hardcoded role IDs with role keys
- [ ] Replace direct `evaluateRbac()` with `checkRbacPermission()`
- [ ] Replace legacy `checkPermission()` with `checkRbacPermission()`
- [ ] Update imports to use centralized services

## Security Best Practices

### Server-Side Validation
1. **Always validate permissions server-side** - client checks are for UX only
2. **Use specific CRUD properties** - prefer `crRoles` over generic `rRoles`
3. **Include organization context** - specify "current" or "any" explicitly
4. **Handle edge cases** - use `silentFail` for non-critical checks

### Client-Side Implementation
1. **Use centralized hooks** - prefer `useRbacPermission()` over custom logic
2. **Apply business rules** - use functions from `permission-utils.ts`
3. **Handle loading states** - check `isLoading` before rendering
4. **Provide fallbacks** - gracefully handle permission failures

## Common Migration Issues

### Issue 1: Role ID vs Role Key Confusion
```typescript
// ❌ WRONG - Using role ID
{ rRoles: [1] }

// ✅ CORRECT - Using role key
{ rRoles: ["superAdmin"] }
```

### Issue 2: Missing Organization Context
```typescript
// ❌ INCOMPLETE - No org context specified
{ rRoles: ["superAdmin"] }

// ✅ COMPLETE - Explicit org context
{ rRoles: ["superAdmin"], orgContext: "any" }
```

### Issue 3: Generic vs Specific CRUD Properties
```typescript
// ❌ GENERIC - Less secure
{ rMinRole: "orgAdmin" }

// ✅ SPECIFIC - More secure
{ rdMinRole: "orgAdmin" } // For delete operations
```

## Testing Migration

### Verification Steps
1. **Test permission boundaries** - verify users can't access unauthorized resources
2. **Test role hierarchies** - ensure higher roles inherit lower role permissions
3. **Test organization context** - verify "current" vs "any" behavior
4. **Test edge cases** - disabled orgs, inactive users, etc.

### Automated Testing
- Permission consistency tests validate RBAC patterns
- Integration tests verify API endpoint security
- Component tests ensure proper permission rendering

## Future Considerations

### Planned Improvements
1. **Performance optimizations** - database query consolidation
2. **Enhanced logging** - security audit trail
3. **Permission caching** - React.memo optimizations
4. **Monitoring** - permission check performance metrics

### Deprecation Timeline
- **permissions-service.ts** - Marked deprecated, removal planned for next major version
- **Legacy patterns** - All legacy patterns have been migrated
- **Documentation** - This guide will be updated as the system evolves

## Support

For questions about the permission system migration:
1. Review this migration guide
2. Check the security update documentation
3. Examine existing implementations for patterns
4. Consult the RBAC system documentation

---

*Last updated: Phase 2B completion*
*Migration status: ✅ COMPLETE*
