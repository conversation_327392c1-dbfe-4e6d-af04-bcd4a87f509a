import { useAuthContextStore } from '@/stores/useAuthContextStore';

// Global flag to prevent multiple simultaneous redirects
let isRedirectingToLogin = false;

/**
 * Global authentication error handler that detects 401 errors and redirects to login
 * while clearing the auth state to prevent retry loops.
 * 
 * This should be used in all API calls that might return 401 errors.
 */
export function handleAuthenticationError(error: Error, statusCode?: number): never | void {
  // Check if this is a 401 authentication error
  const is401Error = statusCode === 401 || 
                     error.message.includes('401') || 
                     error.message.includes('Authentication required') ||
                     error.message.includes('Unauthorized') ||
                     error.message.includes('Auth session missing');

  if (is401Error) {
    console.warn('[AuthErrorHandler] Authentication error detected, redirecting to login');
    
    // Prevent multiple simultaneous redirects
    if (isRedirectingToLogin) {
      return;
    }
    isRedirectingToLogin = true;
    
    // Clear auth state to prevent further API calls
    try {
      useAuthContextStore.getState().clearAuthContext();
    } catch (e) {
      console.error('[AuthErrorHandler] Error clearing auth context:', e);
    }
    
    // Clear any cached data that might trigger more requests
    if (typeof window !== 'undefined') {
      try {
        // Clear localStorage items that might contain stale auth data
        localStorage.removeItem('auth-context');
        sessionStorage.clear();
      } catch (e) {
        console.error('[AuthErrorHandler] Error clearing storage:', e);
      }
    }
    
    // Redirect to login after a short delay to allow state cleanup
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        // Use window.location.href for a full page reload to ensure clean state
        window.location.href = '/auth/login?error=' + encodeURIComponent('Session expired');
      }
    }, 100);
    
    return;
  }
  
  // For other errors, just throw them to be handled by the caller
  throw error;
}

/**
 * Reset the redirect flag - useful for testing or if the redirect fails
 */
export function resetRedirectFlag() {
  isRedirectingToLogin = false;
}

/**
 * Check if we're currently redirecting to login
 */
export function isCurrentlyRedirecting() {
  return isRedirectingToLogin;
}
