import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { ProfileForm } from "@/components/profile/ProfileForm";

export default async function ProfilePage() {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (!user || userError) {
    redirect("/auth/login");
  }

  // Fetch profile data
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .single();

  // Fetch personal information
  const { data: personalInfo } = await supabase
    .from("user_personal_information")
    .select("*")
    .eq("id", user.id)
    .single();

  async function updateProfile(formData: FormData) {
    "use server";

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Not authenticated");
    }

    // Get existing personal info first
    const { data: existingInfo } = await supabase
      .from("user_personal_information")
      .select("*")
      .eq("id", user.id)
      .single();

    const heightCm = formData.get("height_cm");
    const dobValue = formData.get("dob");
    const passportIssueDateValue = formData.get("passport_date_issue");
    const passportExpiryDateValue = formData.get("passport_date_expiry");

    const formatDate = (dateValue: FormDataEntryValue | null) =>
      dateValue
        ? new Date(dateValue as string).toISOString().split("T")[0]
        : null;

    // Merge existing data with new form data
    const personalInfoData = {
      ...(existingInfo || {}),
      id: user.id,
      first_name:
        formData.get("first_name") || existingInfo?.first_name || null,
      middle_name:
        formData.get("middle_name") || existingInfo?.middle_name || null,
      last_name: formData.get("last_name") || existingInfo?.last_name || null,
      passport_name:
        formData.get("passport_name") || existingInfo?.passport_name || null,
      birth_name:
        formData.get("birth_name") || existingInfo?.birth_name || null,
      previous_name:
        formData.get("previous_name") || existingInfo?.previous_name || null,
      nickname: formData.get("nickname") || existingInfo?.nickname || null,
      non_latin_full_name:
        formData.get("non_latin_full_name") ||
        existingInfo?.non_latin_full_name ||
        null,
      dob: formatDate(dobValue) || existingInfo?.dob || null,
      nationality:
        formData.get("nationality") || existingInfo?.nationality || null,
      nationality_birth:
        formData.get("nationality_birth") ||
        existingInfo?.nationality_birth ||
        null,
      sex: formData.get("sex") || existingInfo?.sex || null,
      place_of_birth:
        formData.get("place_of_birth") || existingInfo?.place_of_birth || null,
      height_cm: heightCm
        ? parseInt(heightCm as string, 10) || existingInfo?.height_cm || null
        : existingInfo?.height_cm || null,
      passport_doc_id:
        formData.get("passport_doc_id") ||
        existingInfo?.passport_doc_id ||
        null,
      passport_date_issue:
        formatDate(passportIssueDateValue) ||
        existingInfo?.passport_date_issue ||
        null,
      passport_date_expiry:
        formatDate(passportExpiryDateValue) ||
        existingInfo?.passport_date_expiry ||
        null,
      passport_country:
        formData.get("passport_country") ||
        existingInfo?.passport_country ||
        null,
      updated_by: user.id,
    };

    // Update or insert personal information
    const { error: personalInfoError } = await supabase
      .from("user_personal_information")
      .upsert(personalInfoData, {
        onConflict: "id",
      });

    if (personalInfoError) {
      console.error("Supabase error:", personalInfoError);
      throw new Error(
        `Failed to update personal information: ${personalInfoError.message}`
      );
    }

    // Handle avatar update if provided
    const avatarFile = formData.get("avatar") as File;
    if (avatarFile && avatarFile.size > 0) {
      const fileExt = avatarFile.name.split(".").pop();
      const filePath = `${user.id}/${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, avatarFile);

      if (!uploadError) {
        const {
          data: { publicUrl },
        } = supabase.storage.from("avatars").getPublicUrl(filePath);

        await supabase
          .from("profiles")
          .update({ avatar_url: publicUrl })
          .eq("id", user.id);
      }
    }

    revalidatePath("/dashboard/profile");
  }

  return (
    <ProfileForm
      profile={profile}
      personalInfo={personalInfo}
      updateProfile={updateProfile}
    />
  );
}
