"use client";

import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { handbooks } from "@/data/handbooks";
import { HandbookSelectorProps } from "@/types/components/HandbookSelectorProps";

export default function ClientHandbookSelector({
  currentId,
}: HandbookSelectorProps) {
  const router = useRouter();

  return (
    <div className="mb-6 w-full mx-auto">
      <Select
        value={currentId || handbooks[0].service.service_case_id}
        onValueChange={(value) =>
          router.push(`/dashboard/handbooks?id=${value}`)
        }
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select a handbook..." />
        </SelectTrigger>
        <SelectContent className="w-[var(--radix-select-trigger-width)] max-h-[300px]">
          {handbooks.map((handbook) => (
            <SelectItem
              key={handbook.service.service_case_id}
              value={handbook.service.service_case_id}
            >
              {handbook.service.service_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
