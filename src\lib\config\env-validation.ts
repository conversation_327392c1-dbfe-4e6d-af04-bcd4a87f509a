/**
 * Environment Variable Validation for Magic Link Hybrid Implementation
 *
 * This module validates all required environment variables for the magic link system
 * and provides helpful error messages for missing or invalid configurations.
 */

import { z } from 'zod';

/**
 * Schema for validating environment variables
 */
const envSchema = z.object({
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: z
    .string()
    .url('NEXT_PUBLIC_SUPABASE_URL must be a valid URL')
    .min(1, 'NEXT_PUBLIC_SUPABASE_URL is required'),

  NEXT_PUBLIC_SUPABASE_ANON_KEY: z
    .string()
    .min(1, 'NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
    .refine(
      (key) => key.startsWith('eyJ'),
      'NEXT_PUBLIC_SUPABASE_ANON_KEY must be a valid JWT token (should start with "eyJ")'
    ),

  NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY: z
    .string()
    .min(1, 'NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY is required for admin operations')
    .refine(
      (key) => key.startsWith('eyJ'),
      'NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY must be a valid JWT token (should start with "eyJ")'
    ),

  // Email Configuration (Resend)
  RESEND_API_KEY: z
    .string()
    .min(1, 'RESEND_API_KEY is required for email sending')
    .refine(
      (key) => key.startsWith('re_'),
      'RESEND_API_KEY must be a valid Resend API key (should start with "re_")'
    ),

  EMAIL_DOMAIN: z
    .string()
    .min(1, 'EMAIL_DOMAIN is required for email sending')
    .refine(
      (domain) => /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(domain),
      'EMAIL_DOMAIN must be a valid domain name'
    ),

  // Application Configuration
  NEXT_PUBLIC_SITE_URL: z
    .string()
    .url('NEXT_PUBLIC_SITE_URL must be a valid URL')
    .min(1, 'NEXT_PUBLIC_SITE_URL is required for magic link redirects'),

  // CAPTCHA Configuration
  NEXT_PUBLIC_TURNSTILE_SITE_KEY: z
    .string()
    .min(1, 'NEXT_PUBLIC_TURNSTILE_SITE_KEY is required for CAPTCHA verification')
    .refine(
      (key) => key.startsWith('0x'),
      'NEXT_PUBLIC_TURNSTILE_SITE_KEY must be a valid Turnstile site key (should start with "0x")'
    ),

  // Feature Flags
  USE_CUSTOM_MAGIC_LINKS: z
    .string()
    .optional()
    .default('false')
    .refine(
      (value) => ['true', 'false'].includes(value || 'false'),
      'USE_CUSTOM_MAGIC_LINKS must be either "true" or "false"'
    ),
});

/**
 * Validated environment variables type
 */
export type ValidatedEnv = z.infer<typeof envSchema>;

/**
 * Validation result interface
 */
interface ValidationResult {
  success: boolean;
  data?: ValidatedEnv;
  errors?: string[];
  warnings?: string[] | undefined;
}

/**
 * Validate environment variables
 *
 * @param env - Environment variables object (defaults to process.env)
 * @returns ValidationResult with success status and any errors/warnings
 */
export function validateEnvironment(env: Record<string, string | undefined> = process.env): ValidationResult {
  try {
    const result = envSchema.safeParse(env);

    if (!result.success) {
      const errors = result.error.errors.map(error =>
        `${error.path.join('.')}: ${error.message}`
      );

      return {
        success: false,
        errors,
      };
    }

    // Additional validation warnings
    const warnings: string[] = [];

    // Check if using custom magic links in production
    if (result.data.USE_CUSTOM_MAGIC_LINKS === 'true' &&
        result.data.NEXT_PUBLIC_SITE_URL.includes('localhost')) {
      warnings.push('USE_CUSTOM_MAGIC_LINKS is enabled in development environment');
    }

    // Check if service role key and anon key are different
    if (result.data.NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY === result.data.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      warnings.push('Service role key and anon key appear to be the same - this is a security risk');
    }

    return {
      success: true,
      data: result.data,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  } catch (error) {
    return {
      success: false,
      errors: [`Environment validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
    };
  }
}

/**
 * Validate environment and throw on failure
 *
 * @param env - Environment variables object (defaults to process.env)
 * @returns ValidatedEnv - Validated environment variables
 * @throws Error if validation fails
 */
export function validateEnvironmentOrThrow(env: Record<string, string | undefined> = process.env): ValidatedEnv {
  const result = validateEnvironment(env);

  if (!result.success) {
    const errorMessage = [
      '❌ Environment validation failed:',
      '',
      ...(result.errors || []).map(error => `  • ${error}`),
      '',
      '💡 Please check your .env file and ensure all required variables are set.',
    ].join('\n');

    console.error(errorMessage);
    throw new Error('Environment validation failed');
  }

  // Log warnings if any
  if (result.warnings && result.warnings.length > 0) {
    console.warn('⚠️  Environment validation warnings:');
    result.warnings.forEach(warning => console.warn(`  • ${warning}`));
  }

  return result.data!;
}

/**
 * Check if custom magic links are enabled
 *
 * @returns boolean - true if custom magic links should be used
 */
export function isCustomMagicLinksEnabled(): boolean {
  try {
    const env = validateEnvironmentOrThrow();
    return env.USE_CUSTOM_MAGIC_LINKS === 'true';
  } catch {
    // Default to false if validation fails
    return false;
  }
}

/**
 * Get safe client-side configuration (no sensitive keys)
 *
 * @returns Configuration object with only client-safe settings
 */
export function getClientSafeConfig() {
  const env = validateEnvironmentOrThrow();

  return {
    app: {
      siteUrl: env.NEXT_PUBLIC_SITE_URL,
      turnstileSiteKey: env.NEXT_PUBLIC_TURNSTILE_SITE_KEY,
    },
    features: {
      useCustomMagicLinks: env.USE_CUSTOM_MAGIC_LINKS === 'true',
    },
  };
}

/**
 * SECURITY NOTE: Server-side configuration access
 *
 * For server-side code that needs sensitive configuration:
 * - Import environment variables directly: process.env.NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY
 * - Use validateEnvironmentOrThrow() for validation
 * - Never create functions that return sensitive keys in objects
 */

/**
 * Validate environment on module load (for early error detection)
 * Disabled for now to prevent startup issues
 */
// if (typeof window === 'undefined') {
//   // Only validate on server-side
//   try {
//     validateEnvironmentOrThrow();
//     console.info('✅ Environment validation passed');
//   } catch (error) {
//     console.error('❌ Environment validation failed on startup');
//     // Don't throw here to allow the app to start and show proper error pages
//   }
// }
