import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, MoreVertical, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Step, StepType, STEP_TYPES } from "@/types/flows";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { FlowCardProps } from "@/types/components/flows/FlowCardProps";

export function FlowCard({ flow, onDelete, onSave }: FlowCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [name, setName] = useState(flow.name);
  const [description, setDescription] = useState(flow.description || "");
  const [steps, setSteps] = useState<Step[]>(flow.steps || []);
  const [showNameError, setShowNameError] = useState(false);

  useEffect(() => {
    setName(flow.name);
    setDescription(flow.description || "");
    setSteps(flow.steps || []);
  }, [flow]);

  const handleAddStep = () => {
    const newStep: Step = {
      id: steps.length + 1,
      uid: crypto.randomUUID(),
      type: "upload_passport_bio",
      title: "New Step",
      position: steps.length + 1,
      flowId: flow.id,
    };
    setSteps([...steps, newStep]);
  };

  const handleDeleteStep = (stepId: number) => {
    setSteps(steps.filter((step) => step.id !== stepId));
  };

  const handleStepTypeChange = (stepId: number, type: StepType) => {
    setSteps(
      steps.map((step) => (step.id === stepId ? { ...step, type } : step))
    );
  };

  const handleSave = () => {
    if (!name.trim()) {
      setShowNameError(true);
      return;
    }
    onSave({
      ...flow,
      name,
      description,
      steps,
    });
    setIsExpanded(false);
  };

  const handleCancel = () => {
    if (!flow.name) {
      onDelete(flow.id);
    } else {
      setName(flow.name);
      setDescription(flow.description || "");
      setSteps(flow.steps || []);
      setShowNameError(false);
      setIsExpanded(false);
    }
  };

  return (
    <Card className="bg-white">
      <div className="p-4 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 flex-1">
            <span className="text-sm text-muted-foreground">{flow.id}.</span>
            <TooltipProvider>
              <Tooltip open={showNameError}>
                <TooltipTrigger asChild>
                  <Input
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value);
                      setShowNameError(false);
                    }}
                    placeholder="Enter flow name"
                    className="h-7 text-sm flex-1"
                    onClick={() => setIsExpanded(true)}
                  />
                </TooltipTrigger>
                {showNameError && (
                  <TooltipContent>
                    <p>Flow name is required</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? "Collapse" : "Expand"}
            </Button>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  className="text-destructive"
                  onClick={() => onDelete(flow.id)}
                >
                  Delete Flow
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {isExpanded && (
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Description</div>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Add a description..."
                className="min-h-[80px] resize-y"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">Steps</div>
                <Button variant="outline" size="sm" onClick={handleAddStep}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Step
                </Button>
              </div>

              <div className="space-y-2">
                {steps.map((step, index) => (
                  <Card key={step.uid} className="p-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">
                        {index + 1}.
                      </span>
                      <Select
                        value={step.type}
                        onValueChange={(value: StepType) =>
                          handleStepTypeChange(step.id, value)
                        }
                      >
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {STEP_TYPES.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.split("_").join(" ").toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteStep(step.id)}
                        className="text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                Cancel
              </Button>
              <Button variant="default" size="sm" onClick={handleSave}>
                Save
              </Button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
