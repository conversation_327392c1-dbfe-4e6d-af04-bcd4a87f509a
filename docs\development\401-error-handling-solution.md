# 401 Error Handling Solution

## Problem

When a user logs out in one tab, other tabs continue to make API requests that return 401 errors because the session is gone. This creates an endless loop of failed requests and error logs:

```
[7r2uj] Authorized organizations request started: {
  referrer: 'http://localhost:3000/dashboard',
  timestamp: '2025-05-29T10:00:02.508Z'
}
[7r2uj] Unauthorized request: { error: 'Auth session missing!', duration: 1 }
 GET /api/organizations/authorized 401 in 26ms
```

## Solution

Implemented a comprehensive 401 error handling system that:

1. **Detects 401 authentication errors** across all API calls
2. **Clears local state** to prevent further requests
3. **Redirects to login** with a session expired message
4. **Prevents retry loops** by stopping SWR retries on auth errors
5. **Works across all tabs** through global interceptors

## Implementation

### 1. Global Authentication Error Handler

**File:** `src/lib/auth-error-handler.ts`

- Centralized function to handle 401 errors
- Clears auth state and localStorage
- Redirects to login with session expired message
- Prevents multiple simultaneous redirects

### 2. Enhanced API Functions

**Files Updated:**
- `src/lib/refresh-auth-context.ts`
- `src/hooks/use-organizations-list.ts`
- `src/hooks/use-server-context-refresher.ts`

All API functions now use the global error handler to catch 401 responses.

### 3. Global SWR Configuration

**Files:**
- `src/lib/swr-config.ts` - Global SWR config with auth error handling
- `src/components/providers/swr-provider.tsx` - SWR provider component
- `src/app/layout.tsx` - Added SWR provider to root layout

Configures SWR to:
- Stop retrying on 401 errors
- Handle authentication errors globally
- Use consistent error handling across all SWR requests

### 4. Global Fetch Interceptor

**Files:**
- `src/lib/global-fetch-interceptor.ts` - Intercepts all fetch calls
- `src/components/providers/global-dashboard-provider.tsx` - Initializes interceptor

Catches 401 errors from any fetch call in the application, even those not using SWR.

## How It Works

1. **User logs out in Tab A**
2. **Tab B makes an API request** (e.g., organization switcher refresh)
3. **Server returns 401** because session is expired
4. **Global error handler detects 401**
5. **Auth state is cleared** in Zustand store and localStorage
6. **User is redirected to login** with "Session expired" message
7. **No more retry loops** because SWR stops retrying on auth errors

## Testing

### Manual Testing

1. **Open two tabs** with the dashboard
2. **Log out in one tab** using the logout button
3. **Switch to the other tab** and wait a few seconds
4. **Verify:** The second tab should automatically redirect to login with "Session expired" message
5. **Check console:** No more 401 error loops should appear

### Test Scenarios

1. **Organization switcher refresh** - Should redirect on 401
2. **Context refresh calls** - Should redirect on 401  
3. **Any fetch call** - Should redirect on 401
4. **Multiple tabs** - All tabs should redirect when session expires
5. **SWR retries** - Should stop retrying on 401 errors

### Expected Behavior

- ✅ Immediate redirect to login on session expiry
- ✅ Clear error message: "Session expired"
- ✅ No endless 401 error loops in console
- ✅ Works across all tabs
- ✅ Preserves other error handling for non-auth errors

## Files Modified

1. `src/lib/auth-error-handler.ts` - **NEW** - Global auth error handler
2. `src/lib/swr-config.ts` - **NEW** - Global SWR configuration
3. `src/components/providers/swr-provider.tsx` - **NEW** - SWR provider
4. `src/lib/global-fetch-interceptor.ts` - **NEW** - Global fetch interceptor
5. `src/lib/refresh-auth-context.ts` - Enhanced with auth error handling
6. `src/hooks/use-organizations-list.ts` - Enhanced with auth error handling
7. `src/hooks/use-server-context-refresher.ts` - Enhanced with auth error handling
8. `src/components/providers/global-dashboard-provider.tsx` - Initialize fetch interceptor
9. `src/app/layout.tsx` - Added SWR provider

## Benefits

- **Better UX:** Users get clear feedback when session expires
- **Cleaner logs:** No more endless 401 error loops
- **Consistent behavior:** All API calls handle auth errors the same way
- **Cross-tab sync:** Session expiry is handled across all open tabs
- **Maintainable:** Centralized error handling makes future changes easier

## Future Enhancements

- Add session refresh attempts before redirecting
- Implement toast notifications for session expiry
- Add analytics tracking for session expiry events
- Consider implementing automatic session extension
