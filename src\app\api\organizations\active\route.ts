import { NextResponse } from 'next/server'
import { getCurrentUserActiveOrganization } from '@/lib/organization-utils-server'

export async function GET() {
  try {
    const { organization, error } = await getCurrentUserActiveOrganization()
    
    if (error) {
      console.error('Error getting active organization:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    
    return NextResponse.json({ organization })
  } catch (error) {
    console.error('Unexpected error in active organization endpoint:', error)
    return NextResponse.json(
      { error: 'Failed to get active organization' },
      { status: 500 }
    )
  }
} 