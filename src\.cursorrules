# Lessons

## Next.js Dynamic APIs

- Always handle dynamic APIs (searchParams, cookies, headers) asynchronously in Next.js 15+
- Use React.use() in Client Components to unwrap dynamic API Promises
- Use await in Server Components to unwrap dynamic API Promises
- Never access dynamic API properties directly without awaiting

Example for Server Components:
```typescript
interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default async function Page({ searchParams }: PageProps) {
  const params = await searchParams;
  // Now you can safely use params.id
}
```

Example for Client Components:
```typescript
'use client'

import { use } from 'react'

interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default function Page({ searchParams }: PageProps) {
  const params = use(searchParams);
  // Now you can safely use params.id
}
```

## Cursor learned

- follows Next.js's server-first pattern throughout the application
- Next.js cookies need to be handled asynchronously with async/await
- Use Supabase SSR package instead of auth-helpers (deprecated)
- Always handle cookies asynchronously in Next.js 15+
- Always use the updated cookieStore method in Next.js 15+
- Use npx shadcn@latest add [component] to add Shadcn UI components (NOT npx shadcn-ui@latest)
- Navigation authorization should be handled server-side for immediate role knowledge
- Avoid client-side role fetching to prevent incorrect initial states
- Never use @supabase/auth-helpers-nextjs as it's deprecated. Use @supabase/ssr instead for server-side Supabase authentication
- Always use supabase.auth.getUser() instead of relying on session.user for authentication, as session data could be insecure
- Always use Next.js Image component (<Image />) from 'next/image' instead of HTML <img> tag for better performance and optimization
- TypeScript interfaces for handbook components are located in src/types/handbook.ts
- Always add null checks for optional props in React components and provide fallback UI to prevent runtime errors
- The project is already running on port 3000 - don't try to start it again

# Project Structure

## Types

### Handbook Types (src/types/handbook.ts)

Contains all TypeScript interfaces for the handbook feature:

- HandbookProps: Main props interface for the Handbook component
- Service: Service information interface
- Criteria: Eligibility criteria interface
- Procedures: Service procedures interface
- RequiredDocuments: Required documents interface
- Fee: Service fees interface
- ServiceChannels: Service locations interface
- ComplaintChannels: Complaint channels interface
- ApplicationForms: Application forms interface
- BottomRemarks: Additional information interface
- Source: Source information interface

# Scratchpad

## [X] Refine Inactive User Account Logic

### Fixed Issues:
1. [X] Updated `organization-switcher.tsx` 
   - Removed the modal dialog for inactive user accounts
   - Now properly redirects to account-disabled page when selecting an org where user is inactive
   - Shows inactive status in the dropdown

2. [X] Enhanced Dashboard Provider 
   - Added `isUserInactive` flag to track user status in current organization
   - Updated DashboardContextType to include the new property

3. [X] Improved Sidebar Navigation
   - Added logic to hide navigation when user is inactive (even for superadmins)
   - Added message informing users their account is disabled
   - Maintains organizational switcher visibility for inactive users

4. [X] Updated Dashboard Event Manager
   - Modified real-time member status event handling
   - Added logic to redirect to account-disabled page when a user is deactivated
   - Properly refreshes server context before redirect

### Flow Now Works Correctly:
- Users with inactive accounts can see the organization switcher but no other navigation
- Inactive users are properly redirected to the account-disabled page
- Real-time status changes (activation/deactivation) are properly handled
- Organizations where a user is inactive are still visible in the switcher with appropriate styling

All changes respect the defined precedence order:
1. Authentication check
2. Organization disabled check (with superadmin exception)
3. User account disabled in organization check

# General

- Use always use npx shadcn@latest init to install shadcn/ui. Avoid using shadcn-ui as it's deprecated.
- Use always use npx shadcn@latest add [component] to add a component to the project.
- Always use pnpm as the package manager.