import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
// import { invalidateUserCache } from '@/lib/auth-context' // This function no longer exists
import { RoleId } from '@/lib/rbac/roles'; // Import RoleId for isSuperAdmin derivation
import { revalidatePath, revalidateTag } from 'next/cache' // Import revalidatePath and revalidateTag for cache clearing

// Define the expected shape for the context part of the successful response
interface ApiRefreshedAuthContext {
  userId: string | null;
  orgId: string | null;
  roleId: number | null;
  isUserActiveInOrg: boolean | null;
  isOrgActive: boolean | null;
  isSuperAdmin: boolean;
  userEmail: string | null;
  userFullName: string | null;
  avatarUrl: string | null;
  activeOrgName: string | null;
}

export async function POST(request: NextRequest) {
  // Create a new Headers object. This will be mutated by Supabase client if cookies are set.
  const responseHeaders = new Headers(request.headers) 
  // We will add any cookies set by Supa<PERSON> to these headers.

  // Parse request body if present to check for flags
  let requestBody: { 
    clearCache?: boolean; 
    flushMiddlewareCache?: boolean; 
    forceRevalidation?: boolean;
    preventEventEmit?: boolean;
  } = { 
    clearCache: true, // Default to clearing cache
    flushMiddlewareCache: false,
    forceRevalidation: false,
    preventEventEmit: false
  };
  
  try {
    // Try to parse body if not empty
    const contentType = request.headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      const bodyText = await request.text();
      if (bodyText && bodyText.trim()) {
        try {
          requestBody = JSON.parse(bodyText);
        } catch (parseError) {
          console.warn('[refresh-context] Failed to parse request body:', parseError);
          // Continue with default settings
        }
      }
    }
  } catch (bodyReadError) {
    console.warn('[refresh-context] Error reading request body:', bodyReadError);
    // Continue with default settings
  }

  try {
    // Create server client, configured for API Route context
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Manually construct the Set-Cookie header string
              let cookieString = `${name}=${value}`;
              if (options.path) cookieString += `; Path=${options.path}`;
              if (options.expires) cookieString += `; Expires=${options.expires.toUTCString()}`;
              if (options.maxAge !== undefined) cookieString += `; Max-Age=${options.maxAge}`;
              if (options.domain) cookieString += `; Domain=${options.domain}`;
              if (options.secure) cookieString += `; Secure`;
              if (options.httpOnly) cookieString += `; HttpOnly`;
              if (typeof options.sameSite === 'string') {
                // Capitalize first letter for SameSite value
                const sameSiteValue = options.sameSite.charAt(0).toUpperCase() + options.sameSite.slice(1);
                cookieString += `; SameSite=${sameSiteValue}`;
              } else if (options.sameSite === true) {
                // Handle the boolean true case if necessary, though typically not needed for Set-Cookie header value
                // Supabase might set it to true internally, but the header value is a string.
                // We'll rely on Supabase's client to provide the correct string value if options.sameSite is true.
                // For now, we'll just add the SameSite attribute without a value if it's true,
                // although this is not standard. A better approach might be to ensure Supabase client
                // provides a string value when options.sameSite is true.
                // Let's revert to the previous approach of using a temp NextResponse for this specific case
                // if this direct string manipulation proves problematic or incomplete for SameSite: true.
                // For now, let's assume if it's true, it implies 'Lax' or similar default, but we won't add a value.
                // A more robust solution might involve checking the Supabase client's expected behavior for SameSite: true.
                // Given the errors, let's stick to handling only string values for SameSite for now.
                // If options.sameSite is true, we won't add the SameSite attribute here.
              }

              responseHeaders.append('Set-Cookie', cookieString);
            });
          },
        },
      }
    )

    // Get the current user
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        console.error('[refresh-context] No authenticated user found:', userError?.message)
        const errorResponseJson = { message: 'Authentication required', details: userError?.message };
        console.log('[refresh-context] Attempting to return 401 JSON:', JSON.stringify(errorResponseJson));
        return NextResponse.json(
          errorResponseJson,
          { status: 401, headers: responseHeaders } 
        )
      }
      
      // Explicitly invalidate any cached auth context
      // We'll do a more targeted invalidation later when we have the org ID
      
      // Fetch current context details (org, role, user status in org, org status, org name)
      // Profile details like email will come from user object, full_name from organization_members
      try {
        const { data: currentContextAndProfile, error: contextProfileError } = await supabase
          .from('organization_members')
          .select(`
            org_id,
            org_member_role,
            org_member_is_active,
            user_full_name,
            organizations!inner (
              is_active,
              org_name
            )
          `)
          .eq('user_id', user.id)
          .eq('is_current_context', true)
          .single();

        if (contextProfileError || !currentContextAndProfile) {
          console.error('[refresh-context] Failed to get current context/profile:', contextProfileError?.message)
          const errorResponseJson = { message: 'Failed to refresh context details', error: contextProfileError?.message };
          console.log('[refresh-context] Attempting to return 500 JSON (contextProfileError):', JSON.stringify(errorResponseJson));
          return NextResponse.json(
            errorResponseJson,
            { status: 500, headers: responseHeaders }
          )
        }
        
        // Type guard for organizations to satisfy TypeScript
        const currentOrgDetails = Array.isArray(currentContextAndProfile.organizations) 
          ? currentContextAndProfile.organizations[0] 
          : currentContextAndProfile.organizations;

        if (!currentOrgDetails) {
          console.error('[refresh-context] Organization details missing in current context query result.');
          const errorResponseJson = { message: 'Incomplete organization details retrieved' };
          console.log('[refresh-context] Attempting to return 500 JSON (incomplete organization details):', JSON.stringify(errorResponseJson));
          return NextResponse.json(
            errorResponseJson,
            { status: 500, headers: responseHeaders }
          );
        }
        
        const roleId = currentContextAndProfile.org_member_role as number | null;

        const refreshedContext: ApiRefreshedAuthContext = {
          userId: user.id,
          orgId: currentContextAndProfile.org_id,
          roleId: roleId,
          isUserActiveInOrg: currentContextAndProfile.org_member_is_active,
          isOrgActive: currentOrgDetails.is_active,
          isSuperAdmin: roleId === RoleId.SUPERADMIN,
          userEmail: user.email ?? null, // Get email from user object
          userFullName: currentContextAndProfile.user_full_name ?? null, // Get from organization_members
          avatarUrl: user.user_metadata?.avatar_url ?? null, // Get from user_metadata or default to null
          activeOrgName: currentOrgDetails.org_name,
        };

        console.log('[refresh-context] Refreshed context object:', refreshedContext);

        // Clear the cache if requested
        let cacheCleared = false;
        let middlewareCacheFlushed = false;
        
        try {
          // Handle normal cache clearing
          if (requestBody.clearCache) {
            // Revalidate main layout paths to ensure fresh data
            revalidatePath('/', 'layout');
            revalidatePath('/dashboard', 'layout');
            revalidateTag('organizations-list'); // Revalidate the tag for the organizations list
            cacheCleared = true;
            console.log('[refresh-context] Successfully cleared cache for main paths and organizations-list tag');
          }
          
          // Handle middleware cache invalidation (more aggressive)
          if (requestBody.flushMiddlewareCache) {
            // Force full path revalidation including middleware
            revalidatePath('/', 'page');
            revalidatePath('/dashboard', 'page');
            
            // Also invalidate status-specific pages to ensure they get fresh data
            revalidatePath('/dashboard/organization-disabled', 'page');
            revalidatePath('/dashboard/account-disabled', 'page');
            
            // Mark that we've flushed the middleware cache
            middlewareCacheFlushed = true;
            console.log('[refresh-context] Successfully flushed middleware cache');
            
            // Also call invalidateUserCache to clear any memory cache entries for this user
            /* This function call is removed as invalidateUserCache no longer exists in auth-context.ts
            try {
              if (user?.id) {
                // Critical fix: Always use organization-specific cache invalidation
                // This ensures middleware cache is properly invalidated when switching orgs
                const userOrgId = refreshedContext?.orgId || undefined; // Convert null to undefined for type safety
                invalidateUserCache(user.id, userOrgId);
                console.log(`[refresh-context] Explicitly invalidated memory cache for user ${user.id} with org ${userOrgId || 'none'}`);
              }
            } catch (cacheError) {
              console.error('[refresh-context] Error invalidating user memory cache:', cacheError);
            }
            */
          }
          
          // Handle force revalidation (most aggressive) - use with caution
          if (requestBody.forceRevalidation) {
            // More aggressive revalidation: tag-based to ensure all middleware cache is refreshed
            try {
              // Add timestamp to force unique revalidation on every request
              const timestamp = Date.now();
              revalidatePath(`/force-refresh-${timestamp}`, 'layout');
              revalidatePath('/api/auth/refresh-context', 'page');
              console.log(`[refresh-context] Forced tag-based revalidation at ${timestamp}`);
            } catch (revalidateError) {
              console.error('[refresh-context] Error during force revalidation:', revalidateError);
            }
          }
        } catch (cacheError) {
          console.error('[refresh-context] Error during cache operations:', cacheError);
          // Continue with context refresh response even if cache clearing fails
        }

        console.log('[refresh-context] Successfully refreshed auth context (data prepared):', refreshedContext);

        const successResponseJson = {
          message: 'Context refreshed successfully',
          context: refreshedContext,
          cacheCleared,
          middlewareCacheFlushed,
          timestamp: Date.now() // Include timestamp for debugging
        };
        console.log('[refresh-context] Attempting to return 200 JSON:', JSON.stringify(successResponseJson));
        
        // Add explicit Content-Type header to ensure the browser correctly processes the response
        responseHeaders.set('Content-Type', 'application/json');
        
        // Force the correct encoding for JSON responses to avoid content corruption
        responseHeaders.set('Transfer-Encoding', 'chunked');
        
        // Create an explicit JSON string without relying on NextResponse.json to avoid potential corruption
        const jsonString = JSON.stringify(successResponseJson);
        
        // Log the exact response we're sending
        console.log(`[refresh-context] Sending exact response (${jsonString.length} chars):`, jsonString.substring(0, 100) + '...');
        
        // Return both regular and raw responses for debugging
        return new Response(jsonString, { 
          status: 200, 
          headers: responseHeaders 
        });
      } catch (dataError) {
        // Check specifically for Supabase connection timeout errors
        const errorMessage = dataError instanceof Error ? dataError.message : String(dataError);
        const errorCause = dataError instanceof Error && 'cause' in dataError ? String((dataError as Error & { cause?: unknown }).cause) : 'unknown';
        
        const isConnectionTimeout = 
          errorMessage.includes('ConnectTimeoutError') || 
          errorCause.includes('UND_ERR_CONNECT_TIMEOUT') ||
          errorMessage.includes('fetch failed') && errorMessage.includes('timeout');
          
        if (isConnectionTimeout) {
          console.error('[refresh-context] Supabase connection timeout error detected:', errorMessage, errorCause);
          
          // Return a specific error for connection timeouts
          const timeoutResponseJson = { 
            message: 'Database connection timeout', 
            errorType: 'SUPABASE_TIMEOUT',
            details: 'The connection to the database timed out. Please try again later.' 
          };
          
          return NextResponse.json(
            timeoutResponseJson,
            { status: 503, headers: responseHeaders } // 503 Service Unavailable
          );
        }
        
        // For other errors, continue with default error handling
        console.error('[refresh-context] Error fetching data from Supabase:', errorMessage);
        const errorResponseJson = { message: 'Failed to fetch data', error: errorMessage };
        return NextResponse.json(errorResponseJson, { status: 500, headers: responseHeaders });
      }
    } catch (authError) {
      // Check specifically for Supabase connection timeout errors during auth
      const errorMessage = authError instanceof Error ? authError.message : String(authError);
      const errorCause = authError instanceof Error && 'cause' in authError ? String((authError as Error & { cause?: unknown }).cause) : 'unknown';
      
      const isConnectionTimeout = 
        errorMessage.includes('ConnectTimeoutError') || 
        errorCause.includes('UND_ERR_CONNECT_TIMEOUT') ||
        errorMessage.includes('fetch failed') && errorMessage.includes('timeout');
        
      if (isConnectionTimeout) {
        console.error('[refresh-context] Supabase auth connection timeout error detected:', errorMessage, errorCause);
        
        // Return a specific error for connection timeouts
        const timeoutResponseJson = { 
          message: 'Authentication service timeout', 
          errorType: 'SUPABASE_AUTH_TIMEOUT',
          details: 'The connection to the authentication service timed out. Please try again later.' 
        };
        
        return NextResponse.json(
          timeoutResponseJson,
          { status: 503, headers: responseHeaders } // 503 Service Unavailable
        );
      }
      
      // For other auth errors, continue with normal error handling
      console.error('[refresh-context] Auth error:', errorMessage);
      const errorResponseJson = { message: 'Authentication error', details: errorMessage };
      return NextResponse.json(errorResponseJson, { status: 401, headers: responseHeaders });
    }
  } catch (error) {
    // General error handler for unexpected errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorCause = error instanceof Error && 'cause' in error ? String((error as Error & { cause?: unknown }).cause) : 'unknown';
    
    console.error('[refresh-context] Unexpected server error:', errorMessage, 'Cause:', errorCause);
    
    // Check if this is a connection timeout error
    const isConnectionTimeout = 
      errorMessage.includes('ConnectTimeoutError') || 
      errorCause.includes('UND_ERR_CONNECT_TIMEOUT') ||
      errorMessage.includes('fetch failed') && errorMessage.includes('timeout');
      
    if (isConnectionTimeout) {
      const timeoutResponseJson = { 
        message: 'Service connection timeout', 
        errorType: 'SUPABASE_CONNECTION_TIMEOUT',
        details: 'The connection to the service timed out. Please try again later.' 
      };
      
      return NextResponse.json(
        timeoutResponseJson,
        { status: 503, headers: responseHeaders } // 503 Service Unavailable
      );
    }
    
    const errorResponseJson = { message: 'Server error', error: errorMessage };
    console.log('[refresh-context] Attempting to return 500 JSON (catch block):', JSON.stringify(errorResponseJson));
    return NextResponse.json(
      errorResponseJson,
      { status: 500, headers: responseHeaders }
    )
  }
}

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic' 