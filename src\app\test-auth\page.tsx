"use client";

import { useState } from "react";
import { createClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function TestAuthPage() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [user, setUser] = useState<any>(null);

  const testMagicLink = async () => {
    if (!email) {
      setMessage("Please enter an email");
      return;
    }

    setLoading(true);
    setMessage("");

    try {
      const supabase = createClient();
      
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback?redirectTo=/test-auth`,
        },
      });

      if (error) {
        setMessage(`Error: ${error.message}`);
      } else {
        setMessage("Magic link sent! Check your email.");
      }
    } catch (error) {
      setMessage(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const checkUser = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
  };

  const signOut = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    setUser(null);
    setMessage("Signed out");
  };

  return (
    <div className="container mx-auto py-8 max-w-md">
      <h1 className="text-2xl font-bold mb-6">Magic Link Auth Test</h1>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        
        <Button 
          onClick={testMagicLink} 
          disabled={loading}
          className="w-full"
        >
          {loading ? "Sending..." : "Send Magic Link"}
        </Button>
        
        <Button 
          onClick={checkUser} 
          variant="outline"
          className="w-full"
        >
          Check Current User
        </Button>
        
        {user && (
          <Button 
            onClick={signOut} 
            variant="destructive"
            className="w-full"
          >
            Sign Out
          </Button>
        )}
        
        {message && (
          <div className={`p-3 rounded ${message.includes('Error') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {message}
          </div>
        )}
        
        {user && (
          <div className="p-3 bg-blue-100 text-blue-700 rounded">
            <h3 className="font-semibold">Current User:</h3>
            <p>Email: {user.email}</p>
            <p>ID: {user.id}</p>
            <p>Provider: {user.app_metadata?.provider || 'email'}</p>
          </div>
        )}
      </div>
    </div>
  );
}
