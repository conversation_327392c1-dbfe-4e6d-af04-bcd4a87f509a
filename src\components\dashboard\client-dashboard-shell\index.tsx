'use client';

import React from 'react';
import { useServerContextRefresher } from '@/hooks/use-server-context-refresher';
import { useOrganizationsList } from '@/hooks/use-organizations-list';
import { usePostNavigationOrgSwitch } from '@/hooks/use-post-navigation-org-switch';
import { ApiRefreshedAuthContext } from '@/hooks/use-server-context-refresher';
import { Organization } from '@/types/organization';
import { SystemToast } from '@/components/system-toast';
import { AuthContextProvider } from '@/components/providers/auth-context-provider';

interface ClientDashboardShellProps {
  initialContext: ApiRefreshedAuthContext;
  initialOrganizations: Organization[];
  children: React.ReactNode;
}

export function ClientDashboardShell({
  initialContext,
  initialOrganizations,
  children
}: ClientDashboardShellProps) {
  // Initialize the server context refresher - it will use the Zustand store internally
  useServerContextRefresher(initialContext);

  // Initialize organizations list
  useOrganizationsList({ initialData: initialOrganizations });

  // Handle post-navigation organization switches
  usePostNavigationOrgSwitch();

  // Note: Cross-tab organization switching is now handled by DashboardScenarioManager

  // Log once on hydration to confirm proper initialization
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[ClientShell] Hydrated from server props');
    }
  }, []);
  
  return (
    <AuthContextProvider initialContext={initialContext}>
      <SystemToast />
      {children}
    </AuthContextProvider>
  );
}