# ✅ Used Exports

## File: `src/app/actions/handbook.ts`
`getHandbook` (Line 5)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 53)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/actions/process-invite.ts`
`processInvite` (Line 9)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 16)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/actions/switch-organization.ts`
`switchOrganization` (Line 6)
- Used in: `src/hooks/use-organization-storage.ts` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/auth/context/route.ts`
`GET` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/auth/refresh-context/route.ts`
`dynamic` (Line 371)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

`POST` (Line 21)
- Used in: `src/lib/refresh-auth-context.ts` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/clear-cache/route.ts`
`dynamic` (Line 28)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

`POST` (Line 4)
- Used in: `src/lib/refresh-auth-context.ts` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organization-members/remove/route.ts`
`DELETE` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organization-members/route.ts`
`GET` (Line 43)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organization-members/update-profile/route.ts`
`PATCH` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organization-members/update-role/route.ts`
`PATCH` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organization-members/update-status/route.ts`
`PATCH` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organizations/[id]/route.ts`
`DELETE` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organizations/active/route.ts`
`GET` (Line 4)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organizations/authorized/route.ts`
`GET` (Line 16)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organizations/complete/route.ts`
`GET` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organizations/create/route.ts`
`OPTIONS` (Line 122)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

`POST` (Line 33)
- Used in: `src/lib/refresh-auth-context.ts` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/organizations/switch/route.ts`
`POST` (Line 18)
- Used in: `src/lib/refresh-auth-context.ts` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/api/user-profile/[userId]/route.ts`
`PATCH` (Line 7)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/auth/callback/route.ts`
`GET` (Line 4)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/auth/login/page.tsx`
`default: LoginPage` (Line 8)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/auth/signup/page.tsx`
`default: SignUpPage` (Line 8)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/DashboardLayoutClient.tsx`
`default: DashboardLayoutClient` (Line 20)
- Used in: `src/app/dashboard/layout.tsx` (Line 3)

## File: `src/app/dashboard/account-disabled/page.tsx`
`default: AccountDisabledPage` (Line 7)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/flows/page.tsx`
`default: FlowsPage` (Line 41)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/flowsteps/page.tsx`
`default: FlowStepsPage` (Line 329)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/invites/page.tsx`
`default: AllOrganizationInvitesPage` (Line 16)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/labels/page.tsx`
`default: LabelsPage` (Line 337)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/layout.tsx`
`default: AdminLayout` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/members/page.tsx`
`default: OrgAdminMembersPage` (Line 43)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx`
`default: OrganizationInvitesPage` (Line 51)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/organization/page.tsx`
`default: OrganizationPage` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/page.tsx`
`default: AdminPage` (Line 12)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/admin/users/table-skeleton.tsx`
`TableSkeleton` (Line 8)
- Used in: `src/components/shared/member-table.tsx` (Line 74)

## File: `src/app/dashboard/admin/users/user-profile-dialog.tsx`
`UserProfileDialog` (Line 42)
- Used in: `src/components/shared/member-table.tsx` (Line 73)

## File: `src/app/dashboard/clients/page.tsx`
`default` (Line 41)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/developer/create-organization/actions.ts`
`createOrganizationAction` (Line 102)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 13)

## File: `src/app/dashboard/developer/create-organization/create-organization-form.tsx`
`CreateOrganizationForm` (Line 32)
- Used in: `src/app/dashboard/developer/create-organization/page.tsx` (Line 9)

## File: `src/app/dashboard/developer/create-organization/page.tsx`
`default: CreateOrganizationPage` (Line 13)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/developer/organizations/organization-table.tsx`
`OrganizationTable` (Line 192)
- Used in: `src/app/dashboard/admin/organization/page.tsx` (Line 4)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 1)

## File: `src/app/dashboard/developer/organizations/page.tsx`
`default` (Line 53)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/developer/test-event/page.tsx`
`metadata` (Line 6)
`default: TestEventPage` (Line 11)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/developer/users/page.tsx`
`default` (Line 57)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/error/page.tsx`
`default: ErrorPage` (Line 3)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/handbooks/page.tsx`
`default: HandbookPage` (Line 56)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/layout.tsx`
`dynamic` (Line 9)
`default: DashboardLayout` (Line 11)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/organization-disabled/page.tsx`
`default: OrganizationDisabledPage` (Line 7)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/page.tsx`
`default: DashboardPage` (Line 22)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/profile/billing/page.tsx`
`default: BillingPage` (Line 89)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/profile/page.tsx`
`default: ProfilePage` (Line 6)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/rbac-test/page.tsx`
`default: RbacTestPage` (Line 9)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/dashboard/settings/page.tsx`
`default: SettingsPage` (Line 12)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/layout.tsx`
`metadata` (Line 10)
`default: RootLayout` (Line 15)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/app/page.tsx`
`default: HomePage` (Line 8)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/components/Breadcrumbs.tsx`
`Breadcrumbs` (Line 5)
- Used in: `src/app/dashboard/page.tsx` (Line 1)

## File: `src/components/Charts.tsx`
`Charts` (Line 39)
- Used in: `src/app/dashboard/page.tsx` (Line 2)

## File: `src/components/ClientHandbookSelector.tsx`
`default: ClientHandbookSelector` (Line 14)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 52)

## File: `src/components/MenuItem.tsx`
`default: MenuItem` (Line 96)
- Used in: `src/components/Sidebar.tsx` (Line 4)

## File: `src/components/MobileSidebar.tsx`
`MobileSidebar` (Line 13)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 6)

## File: `src/components/ModeToggle.tsx`
`ModeToggle` (Line 15)
- Used in: `src/components/TopBar.tsx` (Line 6)

## File: `src/components/Sidebar.tsx`
`Sidebar` (Line 18)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 4)
- Used in: `src/components/MobileSidebar.tsx` (Line 4)

## File: `src/components/TopBar.tsx`
`TopBar` (Line 18)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 5)

## File: `src/components/auth/auth-container.tsx`
`AuthContainer` (Line 8)
- Used in: `src/app/auth/login/page.tsx` (Line 6)
- Used in: `src/app/auth/signup/page.tsx` (Line 6)

## File: `src/components/auth/user-auth-form.tsx`
`UserAuthForm` (Line 18)
- Used in: `src/app/auth/login/page.tsx` (Line 5)
- Used in: `src/app/auth/signup/page.tsx` (Line 5)

## File: `src/components/country-selector.tsx`
`default: CountrySelector` (Line 33)
- Used in: `src/app/page.tsx` (Line 1)

## File: `src/components/dashboard/client-dashboard-shell/index.tsx`
`ClientDashboardShell` (Line 17)
- Used in: `src/app/dashboard/layout.tsx` (Line 6)

## File: `src/components/dashboard/dashboard-event-manager-core.tsx`
`DashboardEventManagerCore` (Line 98)
- Used in: `src/components/providers/global-dashboard-provider.tsx` (Line 5)

## File: `src/components/dashboard/dashboard-scenario-manager.tsx`
`DashboardScenarioManager` (Line 37)
- Used in: `src/components/providers/global-dashboard-provider.tsx` (Line 6)

## File: `src/components/developer/test-event-system.tsx`
`TestEventSystem` (Line 30)
- Used in: `src/app/dashboard/developer/test-event/page.tsx` (Line 3)

## File: `src/components/faq-section.tsx`
`default: FAQSection` (Line 55)
- Used in: `src/app/page.tsx` (Line 2)

## File: `src/components/flows/FlowCard.tsx`
`FlowCard` (Line 29)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 9)

## File: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx`
`FlowStepCategoryEditDialog` (Line 14)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 29)

## File: `src/components/frontpage-header.tsx`
`FrontpageHeader` (Line 5)
- Used in: `src/app/page.tsx` (Line 3)

## File: `src/components/icons.tsx`
`Icons` (Line 10)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 10)

## File: `src/components/labels/LabelCategoryEditDialog.tsx`
`LabelCategoryEditDialog` (Line 14)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 29)

## File: `src/components/organization/all-invites-list.tsx`
`AllInvitesList` (Line 27)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 10)

## File: `src/components/organization/create-invite-button.tsx`
`CreateInviteButton` (Line 35)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 13)

## File: `src/components/organization/create-invite-dialog.tsx`
`CreateInviteDialog` (Line 68)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 11)

## File: `src/components/organization/invite-code-display.tsx`
`InviteCodeDisplay` (Line 17)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 25)

## File: `src/components/organization/invites-list.tsx`
`InvitesList` (Line 27)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 12)

## File: `src/components/organization/join-organization-modal.tsx`
`JoinOrganizationModal` (Line 19)
- Used in: `src/providers/organization-check-provider.tsx` (Line 6)

## File: `src/components/organization/organization-switcher.tsx`
`OrganizationSwitcher` (Line 57)
- Used in: `src/components/Sidebar.tsx` (Line 8)

## File: `src/components/profile/ProfileForm.tsx`
`ProfileForm` (Line 84)
- Used in: `src/app/dashboard/profile/page.tsx` (Line 4)

## File: `src/components/providers/auth-context-provider.tsx`
`AuthContextProvider` (Line 7)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 9)

## File: `src/components/providers/dashboard-provider.tsx`
`DashboardProvider` (Line 16)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 7)

`useDashboard` (Line 12)
- Used in: `src/app/dashboard/account-disabled/page.tsx` (Line 4)
- Used in: `src/app/dashboard/account-disabled/page.tsx` (Line 8)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 4)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 48)
- Used in: `src/app/dashboard/organization-disabled/page.tsx` (Line 4)
- Used in: `src/app/dashboard/organization-disabled/page.tsx` (Line 8)
- Used in: `src/components/Sidebar.tsx` (Line 7)
- Used in: `src/components/Sidebar.tsx` (Line 25)
- Used in: `src/components/user-avatar-menu.tsx` (Line 13)
- Used in: `src/components/user-avatar-menu.tsx` (Line 21)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 14)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 46)

## File: `src/components/providers/global-dashboard-provider.tsx`
`GlobalDashboardProvider` (Line 34)
- Used in: `src/app/layout.tsx` (Line 6)

## File: `src/components/rbac/restricted.tsx`
`Restricted` (Line 34)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 48)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 5)
- Used in: `src/components/organization/organization-list.tsx` (Line 18)

## File: `src/components/shared/Icon.tsx`
`Icon` (Line 5)
- Used in: `src/components/MenuItem.tsx` (Line 7)

## File: `src/components/shared/member-table.tsx`
`MemberTableBusinessRules` (Line 16)
`MemberTable` (Line 190)
- Used in: `src/app/dashboard/admin/members/page.tsx` (Line 2)
- Used in: `src/app/dashboard/clients/page.tsx` (Line 1)
- Used in: `src/app/dashboard/developer/users/page.tsx` (Line 1)

## File: `src/components/system-toast.tsx`
`SystemToast` (Line 12)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 8)

## File: `src/components/ui/accordion.tsx`
`Accordion` (Line 57)
`AccordionContent` (Line 57)
`AccordionItem` (Line 57)
`AccordionTrigger` (Line 57)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 2)
- Used in: `src/components/faq-section.tsx` (Line 3)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 8)

## File: `src/components/ui/alert-dialog.tsx`
`AlertDialog`, `AlertDialogAction`, `AlertDialogCancel`, `AlertDialogContent`, `AlertDialogDescription`, `AlertDialogFooter`, `AlertDialogHeader`, `AlertDialogTitle` (Line 129)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 36)

## File: `src/components/ui/alert.tsx`
`Alert` (Line 59)
`AlertDescription` (Line 59)
`AlertTitle` (Line 59)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 6)
- Used in: `src/app/dashboard/developer/users/page.tsx` (Line 4)

## File: `src/components/ui/avatar.tsx`
`Avatar` (Line 50)
`AvatarFallback` (Line 50)
`AvatarImage` (Line 50)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 11)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 7)
- Used in: `src/components/shared/member-table.tsx` (Line 44)

## File: `src/components/ui/badge.tsx`
`Badge` (Line 32)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 12)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 22)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 8)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 21)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 7)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 22)
- Used in: `src/components/organization/invites-list.tsx` (Line 22)
- Used in: `src/components/shared/member-table.tsx` (Line 45)

`badgeVariants` (Line 32)
- Used in: `src/types/components/ui/BadgeProps.ts` (Line 3)

## File: `src/components/ui/breadcrumb.tsx`
`Breadcrumb`, `BreadcrumbItem`, `BreadcrumbLink`, `BreadcrumbList`, `BreadcrumbPage`, `BreadcrumbSeparator` (Line 107)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 11)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 52)
- Used in: `src/app/dashboard/admin/page.tsx` (Line 3)
- Used in: `src/app/dashboard/developer/create-organization/page.tsx` (Line 1)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 6)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 2)
- Used in: `src/app/dashboard/settings/page.tsx` (Line 3)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 14)

## File: `src/components/ui/button.tsx`
`Button` (Line 51)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 4)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 19)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 19)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 13)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 8)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 3)
- Used in: `src/app/dashboard/handbooks/handbook-search.tsx` (Line 7)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 10)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 3)
- Used in: `src/app/page.tsx` (Line 4)
- Used in: `src/components/auth/google-auth-button.tsx` (Line 4)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 11)
- Used in: `src/components/dashboard/dashboard-header.tsx` (Line 3)
- Used in: `src/components/developer/test-event-system.tsx` (Line 4)
- Used in: `src/components/flows/FlowCard.tsx` (Line 3)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 7)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 7)
- Used in: `src/components/frontpage-header.tsx` (Line 2)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 7)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 2)
- Used in: `src/components/login-form.tsx` (Line 2)
- Used in: `src/components/ModeToggle.tsx` (Line 7)
- Used in: `src/components/no-access.tsx` (Line 3)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 7)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 6)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 6)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 5)
- Used in: `src/components/organization/invites-list.tsx` (Line 7)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 6)
- Used in: `src/components/organization/organization-list.tsx` (Line 7)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 6)
- Used in: `src/components/shared/member-table.tsx` (Line 46)

`buttonVariants` (Line 51)
- Used in: `src/components/ui/alert-dialog.tsx` (Line 7)
- Used in: `src/components/ui/pagination.tsx` (Line 5)
- Used in: `src/types/components/ui/ButtonProps.ts` (Line 3)

## File: `src/components/ui/card.tsx`
`Card` (Line 76)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 18)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 18)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 5)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 1)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 19)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 4)
- Used in: `src/components/auth/auth-card.tsx` (Line 4)
- Used in: `src/components/Charts.tsx` (Line 22)
- Used in: `src/components/country-selector.tsx` (Line 6)
- Used in: `src/components/developer/test-event-system.tsx` (Line 6)
- Used in: `src/components/flows/FlowCard.tsx` (Line 2)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 6)

`CardContent` (Line 76)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 5)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 1)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 19)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 4)
- Used in: `src/components/auth/auth-card.tsx` (Line 4)
- Used in: `src/components/Charts.tsx` (Line 22)
- Used in: `src/components/developer/test-event-system.tsx` (Line 6)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 6)

`CardDescription` (Line 76)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 4)
- Used in: `src/components/auth/auth-card.tsx` (Line 4)
- Used in: `src/components/Charts.tsx` (Line 22)
- Used in: `src/components/developer/test-event-system.tsx` (Line 6)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 6)

`CardFooter` (Line 76)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 3)
- Used in: `src/components/auth/auth-card.tsx` (Line 4)
- Used in: `src/components/Charts.tsx` (Line 22)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 6)

`CardHeader` (Line 76)
`CardTitle` (Line 76)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 3)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 5)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 1)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 4)
- Used in: `src/components/auth/auth-card.tsx` (Line 4)
- Used in: `src/components/Charts.tsx` (Line 22)
- Used in: `src/components/developer/test-event-system.tsx` (Line 6)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 6)

## File: `src/components/ui/chart.tsx`
`ChartContainer` (Line 358)
`ChartTooltip` (Line 358)
`ChartTooltipContent` (Line 358)
- Used in: `src/components/Charts.tsx` (Line 30)

## File: `src/components/ui/command.tsx`
`Command`, `CommandEmpty`, `CommandGroup`, `CommandInput`, `CommandItem`, `CommandList` (Line 143)
- Used in: `src/app/dashboard/handbooks/handbook-search.tsx` (Line 8)

## File: `src/components/ui/dialog.tsx`
`Dialog` (Line 111)
`DialogContent` (Line 111)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 17)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 1)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 1)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 1)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 1)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 7)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 7)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 7)
- Used in: `src/components/ui/command.tsx` (Line 9)

`DialogDescription` (Line 111)
`DialogFooter` (Line 111)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 17)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 7)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 7)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 7)

`DialogHeader` (Line 111)
`DialogTitle` (Line 111)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 17)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 1)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 1)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 1)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 1)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 7)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 7)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 7)

`DialogTrigger` (Line 111)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 7)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 7)

## File: `src/components/ui/dropdown-menu.tsx`
`DropdownMenu` (Line 185)
`DropdownMenuContent` (Line 185)
`DropdownMenuItem` (Line 185)
`DropdownMenuTrigger` (Line 185)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 22)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 22)
- Used in: `src/components/flows/FlowCard.tsx` (Line 7)
- Used in: `src/components/ModeToggle.tsx` (Line 8)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 21)
- Used in: `src/components/shared/member-table.tsx` (Line 48)
- Used in: `src/components/user-avatar-menu.tsx` (Line 3)

`DropdownMenuLabel` (Line 185)
`DropdownMenuSeparator` (Line 185)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 21)
- Used in: `src/components/shared/member-table.tsx` (Line 48)
- Used in: `src/components/user-avatar-menu.tsx` (Line 3)

`DropdownMenuRadioGroup` (Line 185)
`DropdownMenuRadioItem` (Line 185)
- Used in: `src/components/shared/member-table.tsx` (Line 48)

## File: `src/components/ui/form.tsx`
`Form`, `FormControl`, `FormDescription`, `FormField`, `FormItem`, `FormLabel`, `FormMessage` (Line 169)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 16)

## File: `src/components/ui/input.tsx`
`Input` (Line 22)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 6)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 21)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 21)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 9)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 6)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 23)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 12)
- Used in: `src/components/flows/FlowCard.tsx` (Line 4)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 8)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 8)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 8)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 3)
- Used in: `src/components/login-form.tsx` (Line 3)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 15)
- Used in: `src/components/organization/organization-list.tsx` (Line 8)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 4)
- Used in: `src/components/shared/member-table.tsx` (Line 47)

## File: `src/components/ui/label.tsx`
`Label` (Line 26)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 10)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 7)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 13)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 9)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 9)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 9)
- Used in: `src/components/login-form.tsx` (Line 4)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 23)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 5)
- Used in: `src/components/ui/form.tsx` (Line 16)

## File: `src/components/ui/popover.tsx`
`Popover` (Line 33)
`PopoverContent` (Line 33)
`PopoverTrigger` (Line 33)
- Used in: `src/app/dashboard/handbooks/handbook-search.tsx` (Line 16)

## File: `src/components/ui/select.tsx`
`Select`, `SelectContent`, `SelectItem`, `SelectTrigger`, `SelectValue` (Line 148)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 40)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 40)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 29)
- Used in: `src/components/ClientHandbookSelector.tsx` (Line 4)
- Used in: `src/components/flows/FlowCard.tsx` (Line 13)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 11)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 6)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 16)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 25)
- Used in: `src/components/shared/member-table.tsx` (Line 66)

## File: `src/components/ui/separator.tsx`
`Separator` (Line 31)
- Used in: `src/app/dashboard/rbac-test/page.tsx` (Line 6)
- Used in: `src/components/Charts.tsx` (Line 35)

## File: `src/components/ui/sheet.tsx`
`Sheet` (Line 128)
`SheetContent` (Line 128)
`SheetHeader` (Line 128)
`SheetTitle` (Line 128)
- Used in: `src/components/MobileSidebar.tsx` (Line 5)

`sheetVariants` (Line 128)
- Used in: `src/types/components/ui/SheetContentProps.ts` (Line 4)

## File: `src/components/ui/skeleton.tsx`
`Skeleton` (Line 15)
- Used in: `src/app/dashboard/admin/users/table-skeleton.tsx` (Line 2)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 26)

## File: `src/components/ui/switch.tsx`
`Switch` (Line 29)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 11)

## File: `src/components/ui/table.tsx`
`Table` (Line 111)
`TableBody` (Line 111)
`TableHead` (Line 111)
`TableHeader` (Line 111)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 14)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 9)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 11)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 8)
- Used in: `src/components/organization/invites-list.tsx` (Line 8)
- Used in: `src/components/organization/organization-list.tsx` (Line 9)
- Used in: `src/components/shared/member-table.tsx` (Line 58)

`TableCell` (Line 111)
`TableRow` (Line 111)
- Used in: `src/app/dashboard/admin/users/table-skeleton.tsx` (Line 1)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 14)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 9)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 11)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 8)
- Used in: `src/components/organization/invites-list.tsx` (Line 8)
- Used in: `src/components/organization/organization-list.tsx` (Line 9)
- Used in: `src/components/shared/member-table.tsx` (Line 58)

## File: `src/components/ui/textarea.tsx`
`Textarea` (Line 22)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 47)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 47)
- Used in: `src/components/flows/FlowCard.tsx` (Line 5)

## File: `src/components/ui/toast.tsx`
`ToastActionElement` (Line 119)
`ToastProps` (Line 119)
- Used in: `src/hooks/use-toast.ts` (Line 7)
- Used in: `src/types/hooks/State.ts` (Line 2)

## File: `src/components/ui/toaster.tsx`
`Toaster` (Line 5)
- Used in: `src/app/layout.tsx` (Line 4)

## File: `src/components/ui/tooltip.tsx`
`Tooltip` (Line 32)
`TooltipContent` (Line 32)
`TooltipProvider` (Line 32)
`TooltipTrigger` (Line 32)
- Used in: `src/components/flows/FlowCard.tsx` (Line 21)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 16)
- Used in: `src/components/organization/invites-list.tsx` (Line 16)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 29)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 24)

## File: `src/components/user-avatar-menu.tsx`
`UserAvatarMenu` (Line 19)
- Used in: `src/components/TopBar.tsx` (Line 4)

## File: `src/data/handbooks.ts`
`handbooks` (Line 1)
- Used in: `src/app/actions/handbook.ts` (Line 3)
- Used in: `src/components/ClientHandbookSelector.tsx` (Line 11)

## File: `src/data/navigation.ts`
`navigationData` (Line 14)
- Used in: `src/components/Sidebar.tsx` (Line 5)

## File: `src/hooks/use-auth-context-events.ts`
`useAuthContextEvents` (Line 75)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 8)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 135)

## File: `src/hooks/use-organization-members-event-bus.ts`
`useOrganizationMembersEventBus` (Line 39)
- Used in: `src/components/shared/member-table.tsx` (Line 4)
- Used in: `src/components/shared/member-table.tsx` (Line 286)

`UseOrganizationMembersProps` (Line 29)
- Used in: `src/components/shared/member-table.tsx` (Line 4)

## File: `src/hooks/use-organization-storage.ts`
`useOrganizationStorage` (Line 9)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 12)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 117)

## File: `src/hooks/use-organizations-list.ts`
`useOrganizationsList` (Line 93)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 5)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 26)
- Used in: `src/components/dashboard/dashboard-event-manager-core.tsx` (Line 7)
- Used in: `src/components/dashboard/dashboard-event-manager-core.tsx` (Line 101)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 16)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 79)
- Used in: `src/components/shared/member-table.tsx` (Line 76)
- Used in: `src/components/shared/member-table.tsx` (Line 201)
- Used in: `src/components/Sidebar.tsx` (Line 12)
- Used in: `src/components/Sidebar.tsx` (Line 27)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 5)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 33)

## File: `src/hooks/use-rbac-permission.ts`
`useRbacPermission` (Line 26)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 49)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 195)
- Used in: `src/components/MenuItem.tsx` (Line 8)
- Used in: `src/components/MenuItem.tsx` (Line 13)
- Used in: `src/components/rbac/restricted.tsx` (Line 4)
- Used in: `src/components/rbac/restricted.tsx` (Line 39)
- Used in: `src/components/shared/member-table.tsx` (Line 6)
- Used in: `src/components/shared/member-table.tsx` (Line 202)
- Used in: `src/components/Sidebar.tsx` (Line 9)
- Used in: `src/components/Sidebar.tsx` (Line 30)

## File: `src/hooks/use-realtime-subscription.ts`
`RealtimePostgresPayload` (Line 10)
- Used in: `src/types/hooks/UseRealtimeSubscriptionProps.ts` (Line 2)

## File: `src/hooks/use-server-context-refresher.ts`
`ApiRefreshedAuthContext` (Line 7)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 6)
- Used in: `src/components/providers/auth-context-provider.tsx` (Line 5)

`useServerContextRefresher` (Line 23)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 4)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 23)
- Used in: `src/components/dashboard/dashboard-event-manager-core.tsx` (Line 6)
- Used in: `src/components/dashboard/dashboard-event-manager-core.tsx` (Line 100)
- Used in: `src/components/dashboard/dashboard-scenario-manager.tsx` (Line 6)
- Used in: `src/components/dashboard/dashboard-scenario-manager.tsx` (Line 41)

## File: `src/hooks/use-toast.ts`
`toast` (Line 202)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 5)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 4)

`useToast` (Line 202)
- Used in: `src/components/auth/google-auth-button.tsx` (Line 6)

## File: `src/lib/auth-context.ts`
`getUserOrganizations` (Line 221)
- Used in: `src/app/dashboard/layout.tsx` (Line 5)

`resolveUserAuthContext` (Line 88)
- Used in: `src/app/api/auth/context/route.ts` (Line 2)
- Used in: `src/lib/rbac/middleware.ts` (Line 5)
- Used in: `src/middleware.ts` (Line 4)

## File: `src/lib/auth-utils.ts`
`hasRequiredRole` (Line 91)
- Used in: `src/components/auth/with-role-check.tsx` (Line 5)

## File: `src/lib/eventBus/channels/allMembers.ts`
`registerAllMembersInterpreter` (Line 13)
- Used in: `src/lib/eventBus/initializeInterpreters.ts` (Line 5)

## File: `src/lib/eventBus/channels/context.ts`
`registerContextInterpreter` (Line 14)
- Used in: `src/lib/eventBus/initializeInterpreters.ts` (Line 4)

## File: `src/lib/eventBus/channels/manager.ts`
`subscribeToDashboardChannels` (Line 182)
`unsubscribeAllDashboardChannels` (Line 287)
`areChannelsBroken` (Line 314)
`hasActiveChannels` (Line 321)
`getSubscriptionState` (Line 328)
- Used in: `src/components/dashboard/dashboard-event-manager-core.tsx` (Line 8)

## File: `src/lib/eventBus/channels/name.ts`
`registerNameInterpreter` (Line 13)
- Used in: `src/lib/eventBus/initializeInterpreters.ts` (Line 3)

## File: `src/lib/eventBus/channels/organizationStatusInterpreter.ts`
`registerOrganizationStatusInterpreter` (Line 12)
- Used in: `src/lib/eventBus/initializeInterpreters.ts` (Line 6)

## File: `src/lib/eventBus/channels/role.ts`
`registerRoleInterpreter` (Line 56)
- Used in: `src/lib/eventBus/initializeInterpreters.ts` (Line 1)

## File: `src/lib/eventBus/channels/status.ts`
`registerStatusInterpreter` (Line 57)
- Used in: `src/lib/eventBus/initializeInterpreters.ts` (Line 2)

## File: `src/lib/eventBus/constants.ts`
`AUTH_CONTEXT_CHANGED` (Line 6)
- Used in: `src/hooks/use-auth-context-events.ts` (Line 2)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 6)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 6)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 7)
- Used in: `src/lib/eventBus/emitter.ts` (Line 15)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 3)

## File: `src/lib/eventBus/emitter.ts`
`clientLog` (Line 91)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/name.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/organizationStatusInterpreter.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 5)

`emitter` (Line 69)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/name.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/organizationStatusInterpreter.ts` (Line 4)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 2)
- Used in: `src/lib/eventBus/index.ts` (Line 15)
- Used in: `src/lib/useBusEvent.ts` (Line 2)

`Events` (Line 29)
- Used in: `src/lib/useBusEvent.ts` (Line 2)

## File: `src/lib/eventBus/hooks/useAuthEvents.ts`
`useAuthEventEmitters` (Line 11)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 9)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 139)

## File: `src/lib/eventBus/hooks/useContextEvents.ts`
`useOrganizationContextEvents` (Line 9)
- Used in: `src/components/developer/test-event-system.tsx` (Line 84)
- Used in: `src/hooks/use-organization-context.ts` (Line 143)
- Used in: `src/providers/organization-check-provider.tsx` (Line 100)

## File: `src/lib/eventBus/hooks/useNameEvents.ts`
`useOrgNameEvents` (Line 9)
- Used in: `src/components/developer/test-event-system.tsx` (Line 87)

## File: `src/lib/eventBus/hooks/useOrganizationEvents.ts`
`useAllOrganizationEvents` (Line 30)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 288)

## File: `src/lib/eventBus/hooks/useRoleEvents.ts`
`useUserRoleEvents` (Line 9)
- Used in: `src/components/developer/test-event-system.tsx` (Line 85)

## File: `src/lib/eventBus/hooks/useStatusEvents.ts`
`useMemberStatusEvents` (Line 9)
- Used in: `src/components/developer/test-event-system.tsx` (Line 86)

## File: `src/lib/eventBus/index.ts`
`emitter` (Line 19)
- Used in: `src/components/providers/global-dashboard-provider.tsx` (Line 9)
- Used in: `src/components/system-toast.tsx` (Line 4)
- Used in: `src/hooks/use-auth-context-events.ts` (Line 3)
- Used in: `src/lib/auth-context.ts` (Line 3)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 4)
- Used in: `src/lib/permissions-service-client.ts` (Line 1)

`Events` (Line 18)
- Used in: `src/tools/mitt-debugger.ts` (Line 9)

`organizationEventBus` (Line 26)
- Used in: `src/hooks/use-organization-storage.ts` (Line 5)

`useAllOrganizationEvents` (Line 34)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 50)

`useUserRoleEvents` (Line 30)
`useMemberStatusEvents` (Line 31)
`useOrgNameEvents` (Line 32)
- Used in: `src/components/developer/test-event-system.tsx` (Line 7)

`useOrganizationContextEvents` (Line 29)
- Used in: `src/components/developer/test-event-system.tsx` (Line 7)
- Used in: `src/hooks/use-organization-context.ts` (Line 6)
- Used in: `src/providers/organization-check-provider.tsx` (Line 7)

## File: `src/lib/eventBus/initializeInterpreters.ts`
`initializeEventSystem` (Line 11)
- Used in: `src/components/providers/global-dashboard-provider.tsx` (Line 10)

## File: `src/lib/eventTypes.ts`
`AnyMemberChangeEvent` (Line 50)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAllMemberChanges.ts` (Line 3)

`AuthEvent` (Line 112)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 5)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 5)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 6)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 5)

`MemberStatusChangedEvent` (Line 29)
- Used in: `src/components/developer/test-event-system.tsx` (Line 13)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 6)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 5)
- Used in: `src/lib/eventBus/hooks/useStatusEvents.ts` (Line 3)

`OrganizationActiveStatusChangedEvent` (Line 101)
- Used in: `src/lib/eventBus/channels/organizationStatusInterpreter.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 5)

`OrganizationContextEvent` (Line 8)
- Used in: `src/components/developer/test-event-system.tsx` (Line 13)
- Used in: `src/hooks/use-organization-context.ts` (Line 7)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 5)
- Used in: `src/lib/eventBus/hooks/useContextEvents.ts` (Line 3)

`OrganizationDataChangedEvent` (Line 91)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 51)
- Used in: `src/lib/eventBus/channels/name.ts` (Line 5)
- Used in: `src/lib/eventBus/channels/organizationStatusInterpreter.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 3)

`OrganizationMemberDeletedEvent` (Line 81)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)

`OrganizationMemberInsertedEvent` (Line 61)
`OrganizationMemberUpdatedEvent` (Line 71)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 5)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 5)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 5)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 6)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)

`OrgNameChangedEvent` (Line 41)
- Used in: `src/components/developer/test-event-system.tsx` (Line 13)
- Used in: `src/lib/eventBus/channels/name.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 5)
- Used in: `src/lib/eventBus/hooks/useNameEvents.ts` (Line 3)

`UserRoleChangedEvent` (Line 17)
- Used in: `src/components/developer/test-event-system.tsx` (Line 13)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 5)
- Used in: `src/lib/eventBus/emitter.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 5)
- Used in: `src/lib/eventBus/hooks/useRoleEvents.ts` (Line 3)

## File: `src/lib/organization-utils-server.ts`
`getCurrentUserActiveOrganization` (Line 291)
- Used in: `src/app/api/organization-members/route.ts` (Line 6)
- Used in: `src/app/api/organizations/active/route.ts` (Line 2)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 11)

`getUserOrganizations` (Line 50)
- Used in: `src/app/api/organizations/complete/route.ts` (Line 3)
- Used in: `src/app/dashboard/admin/organization/page.tsx` (Line 3)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 4)

`PaginatedOrganizationsResponse` (Line 43)
- Used in: `src/app/api/organizations/complete/route.ts` (Line 3)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 4)

## File: `src/lib/permission-utils.ts`
`canChangeUserRole` (Line 57)
- Used in: `src/app/api/organization-members/update-role/route.ts` (Line 3)
- Used in: `src/components/shared/member-table.tsx` (Line 8)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 9)

`canDeleteMember` (Line 105)
- Used in: `src/app/api/organization-members/remove/route.ts` (Line 3)
- Used in: `src/components/shared/member-table.tsx` (Line 8)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 9)

`canEditUserProfile` (Line 153)
- Used in: `src/app/api/organization-members/update-profile/route.ts` (Line 3)
- Used in: `src/app/api/user-profile/[userId]/route.ts` (Line 3)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 5)

`canModifyUser` (Line 185)
`getAssignableRoles` (Line 223)
- Used in: `src/components/shared/member-table.tsx` (Line 8)

`canToggleMemberStatus` (Line 17)
- Used in: `src/app/api/organization-members/update-status/route.ts` (Line 3)
- Used in: `src/components/shared/member-table.tsx` (Line 8)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 9)

## File: `src/lib/permissions-service-client.ts`
`rbacCache` (Line 10)
- Used in: `src/hooks/use-auth-context-events.ts` (Line 5)

## File: `src/lib/rbac/permissions-server.ts`
`checkRbacPermission` (Line 23)
- Used in: `src/app/api/organizations/[id]/route.ts` (Line 3)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 5)
- Used in: `src/app/api/organizations/create/route.ts` (Line 3)
- Used in: `src/app/api/user-profile/[userId]/route.ts` (Line 4)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 14)
- Used in: `src/app/dashboard/developer/test-event/page.tsx` (Line 4)
- Used in: `src/lib/rbac/server-action-multi.ts` (Line 1)
- Used in: `src/lib/rbac/server-action-org.ts` (Line 1)
- Used in: `src/lib/rbac/server-action.ts` (Line 1)

`withRbacPermission` (Line 153)
- Used in: `src/app/dashboard/admin/members/page.tsx` (Line 1)
- Used in: `src/app/dashboard/admin/organization/page.tsx` (Line 1)
- Used in: `src/app/dashboard/clients/page.tsx` (Line 2)
- Used in: `src/app/dashboard/developer/create-organization/page.tsx` (Line 11)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 3)
- Used in: `src/app/dashboard/developer/users/page.tsx` (Line 2)

## File: `src/lib/rbac/rbac-utils.ts`
`evaluateRbac` (Line 108)
- Used in: `src/app/api/organization-members/route.ts` (Line 3)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 4)
- Used in: `src/app/api/organizations/switch/route.ts` (Line 3)
- Used in: `src/app/dashboard/admin/layout.tsx` (Line 3)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 14)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 15)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 8)
- Used in: `src/lib/auth-context.ts` (Line 4)
- Used in: `src/lib/permission-utils.ts` (Line 1)
- Used in: `src/lib/permissions-service-client.ts` (Line 2)
- Used in: `src/lib/rbac/middleware.ts` (Line 4)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 3)

`RoleKey` (Line 5)
- Used in: `src/app/api/organization-members/route.ts` (Line 3)
- Used in: `src/app/dashboard/admin/members/page.tsx` (Line 3)
- Used in: `src/components/shared/member-table.tsx` (Line 5)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 13)
- Used in: `src/types/components/MenuItemProps.ts` (Line 1)

`roleKeyToId` (Line 14)
- Used in: `src/lib/permission-utils.ts` (Line 1)

## File: `src/lib/rbac/role-utils.ts`
`roleHelpers` (Line 30)
`createPermissionResult` (Line 59)
`roleComparison` (Line 122)
`roleUtils` (Line 192)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 12)

## File: `src/lib/rbac/roles.ts`
`RoleId` (Line 3)
- Used in: `src/app/api/auth/refresh-context/route.ts` (Line 4)
- Used in: `src/app/api/organization-members/route.ts` (Line 4)
- Used in: `src/app/dashboard/admin/layout.tsx` (Line 4)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 10)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 1)
- Used in: `src/middleware.ts` (Line 5)
- Used in: `src/stores/useAuthContextStore.ts` (Line 3)

`RoleName` (Line 12)
- Used in: `src/lib/auth-context.ts` (Line 5)

## File: `src/lib/rbac/server-action.ts`
`withPermissionAction` (Line 10)
- Used in: `src/app/dashboard/developer/create-organization/actions.ts` (Line 3)

## File: `src/lib/refresh-auth-context.ts`
`refreshAuthContext` (Line 3)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 17)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 3)
- Used in: `src/lib/auth-context.ts` (Line 7)

## File: `src/lib/supabase/client.ts`
`createClient` (Line 3)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 6)
- Used in: `src/components/auth/google-auth-button.tsx` (Line 5)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 5)
- Used in: `src/components/dashboard/dashboard-event-manager-core.tsx` (Line 4)
- Used in: `src/components/dashboard/dashboard-header.tsx` (Line 4)
- Used in: `src/components/developer/test-event-system.tsx` (Line 5)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 6)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 5)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 5)
- Used in: `src/components/organization/invites-list.tsx` (Line 6)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 14)
- Used in: `src/components/providers/global-dashboard-provider.tsx` (Line 7)
- Used in: `src/components/user-avatar-menu.tsx` (Line 11)
- Used in: `src/hooks/use-organization-context.ts` (Line 4)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 4)
- Used in: `src/hooks/use-realtime-subscription.ts` (Line 4)
- Used in: `src/providers/organization-check-provider.tsx` (Line 5)

## File: `src/lib/supabase/server.ts`
`createClient` (Line 6)
- Used in: `src/app/actions/process-invite.ts` (Line 3)
- Used in: `src/app/actions/switch-organization.ts` (Line 4)
- Used in: `src/app/api/auth/context/route.ts` (Line 3)
- Used in: `src/app/api/organization-members/remove/route.ts` (Line 1)
- Used in: `src/app/api/organization-members/route.ts` (Line 1)
- Used in: `src/app/api/organization-members/update-profile/route.ts` (Line 1)
- Used in: `src/app/api/organization-members/update-role/route.ts` (Line 1)
- Used in: `src/app/api/organization-members/update-status/route.ts` (Line 1)
- Used in: `src/app/api/organizations/[id]/route.ts` (Line 1)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 1)
- Used in: `src/app/api/organizations/complete/route.ts` (Line 2)
- Used in: `src/app/api/organizations/create/route.ts` (Line 2)
- Used in: `src/app/api/organizations/switch/route.ts` (Line 1)
- Used in: `src/app/api/user-profile/[userId]/route.ts` (Line 1)
- Used in: `src/app/auth/callback/route.ts` (Line 1)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 2)
- Used in: `src/app/dashboard/admin/layout.tsx` (Line 2)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 2)
- Used in: `src/app/dashboard/admin/organization/page.tsx` (Line 2)
- Used in: `src/app/dashboard/developer/create-organization/actions.ts` (Line 4)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 2)
- Used in: `src/app/dashboard/developer/test-event/page.tsx` (Line 2)
- Used in: `src/app/dashboard/developer/users/page.tsx` (Line 3)
- Used in: `src/app/dashboard/layout.tsx` (Line 4)
- Used in: `src/app/dashboard/profile/page.tsx` (Line 1)
- Used in: `src/lib/auth-utils.ts` (Line 1)
- Used in: `src/lib/get-user-active-organization.ts` (Line 1)
- Used in: `src/lib/organization-utils-server.ts` (Line 1)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 1)

## File: `src/lib/toast-messages.ts`
`toastMessages` (Line 220)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 8)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 50)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 60)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 14)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 12)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 46)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 11)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 23)
- Used in: `src/components/shared/member-table.tsx` (Line 36)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 12)

## File: `src/lib/useBusEvent.ts`
`useBusEvent` (Line 6)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 7)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 58)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 67)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 76)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 87)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 15)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 294)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 298)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 302)
- Used in: `src/lib/eventBus/hooks/useAllMemberChanges.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useAllMemberChanges.ts` (Line 13)
- Used in: `src/lib/eventBus/hooks/useContextEvents.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useContextEvents.ts` (Line 13)
- Used in: `src/lib/eventBus/hooks/useNameEvents.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useNameEvents.ts` (Line 13)
- Used in: `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 15)
- Used in: `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 33)
- Used in: `src/lib/eventBus/hooks/useRoleEvents.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useRoleEvents.ts` (Line 13)
- Used in: `src/lib/eventBus/hooks/useStatusEvents.ts` (Line 4)
- Used in: `src/lib/eventBus/hooks/useStatusEvents.ts` (Line 13)

## File: `src/lib/utils.ts`
`cn` (Line 4)
- Used in: `src/app/dashboard/handbooks/handbook-search.tsx` (Line 6)
- Used in: `src/app/dashboard/profile/billing/page.tsx` (Line 20)
- Used in: `src/components/auth/auth-card.tsx` (Line 12)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 9)
- Used in: `src/components/country-selector.tsx` (Line 5)
- Used in: `src/components/login-form.tsx` (Line 1)
- Used in: `src/components/organization/organization-nav.tsx` (Line 3)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 10)
- Used in: `src/components/ui/accordion.tsx` (Line 7)
- Used in: `src/components/ui/alert-dialog.tsx` (Line 6)
- Used in: `src/components/ui/alert.tsx` (Line 4)
- Used in: `src/components/ui/avatar.tsx` (Line 6)
- Used in: `src/components/ui/badge.tsx` (Line 3)
- Used in: `src/components/ui/breadcrumb.tsx` (Line 5)
- Used in: `src/components/ui/button.tsx` (Line 4)
- Used in: `src/components/ui/card.tsx` (Line 3)
- Used in: `src/components/ui/chart.tsx` (Line 6)
- Used in: `src/components/ui/checkbox.tsx` (Line 7)
- Used in: `src/components/ui/command.tsx` (Line 8)
- Used in: `src/components/ui/context-menu.tsx` (Line 7)
- Used in: `src/components/ui/dialog.tsx` (Line 7)
- Used in: `src/components/ui/drawer.tsx` (Line 6)
- Used in: `src/components/ui/dropdown-menu.tsx` (Line 7)
- Used in: `src/components/ui/form.tsx` (Line 15)
- Used in: `src/components/ui/input.tsx` (Line 3)
- Used in: `src/components/ui/label.tsx` (Line 7)
- Used in: `src/components/ui/navigation-menu.tsx` (Line 6)
- Used in: `src/components/ui/pagination.tsx` (Line 4)
- Used in: `src/components/ui/popover.tsx` (Line 6)
- Used in: `src/components/ui/scroll-area.tsx` (Line 6)
- Used in: `src/components/ui/select.tsx` (Line 7)
- Used in: `src/components/ui/separator.tsx` (Line 6)
- Used in: `src/components/ui/sheet.tsx` (Line 8)
- Used in: `src/components/ui/skeleton.tsx` (Line 1)
- Used in: `src/components/ui/switch.tsx` (Line 6)
- Used in: `src/components/ui/table.tsx` (Line 3)
- Used in: `src/components/ui/tabs.tsx` (Line 6)
- Used in: `src/components/ui/textarea.tsx` (Line 3)
- Used in: `src/components/ui/toast.tsx` (Line 8)
- Used in: `src/components/ui/toggle.tsx` (Line 7)
- Used in: `src/components/ui/tooltip.tsx` (Line 6)

## File: `src/providers/organization-check-provider.tsx`
`OrganizationCheckProvider` (Line 26)
- Used in: `src/app/layout.tsx` (Line 5)

## File: `src/stores/useAuthContextStore.ts`
`useAuthContextStore` (Line 144)
- Used in: `src/components/dashboard/dashboard-scenario-manager.tsx` (Line 5)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 9)
- Used in: `src/components/providers/auth-context-provider.tsx` (Line 4)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 6)
- Used in: `src/components/shared/member-table.tsx` (Line 75)
- Used in: `src/components/TopBar.tsx` (Line 9)
- Used in: `src/hooks/use-organization-storage.ts` (Line 7)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 4)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 2)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 4)
- Used in: `src/lib/permissions-service-client.ts` (Line 3)
- Used in: `src/lib/refresh-auth-context.ts` (Line 1)

## File: `src/tools/mitt-debugger.ts`
`attachMittDebugger` (Line 70)
- Used in: `src/components/providers/global-dashboard-provider.tsx` (Line 8)
- Used in: `src/lib/eventBus/emitter.ts` (Line 17)

## File: `src/types/app/PageProps.ts`
`PageProps` (Line 1)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 54)

## File: `src/types/app/actions/ProcessInviteResponse.ts`
`ProcessInviteResponse` (Line 2)
- Used in: `src/app/actions/process-invite.ts` (Line 5)
- Used in: `C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/next.js-framework` (Line 0)

## File: `src/types/app/dashboard/admin/flowsteps/FlowStepCategory.ts`
`FlowStepCategory` (Line 1)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 53)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 11)
- Used in: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts` (Line 3)
- Used in: `src/types/components/flowsteps/FlowStepCategoryEditDialogProps.ts` (Line 1)

## File: `src/types/app/dashboard/admin/flowsteps/FlowStepData.ts`
`FLOW_STEP_TYPES` (Line 1)
`FlowStepType` (Line 12)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 54)

`FlowStepData` (Line 14)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 54)
- Used in: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts` (Line 2)

## File: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts`
`SortableFlowStepProps` (Line 5)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 55)

## File: `src/types/app/dashboard/admin/invites/BaseInvite.ts`
`BaseInvite` (Line 1)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 12)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 1)

## File: `src/types/app/dashboard/admin/invites/Invite.ts`
`Invite` (Line 6)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 13)

## File: `src/types/app/dashboard/admin/invites/Organization.ts`
`Organization` (Line 1)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 2)

## File: `src/types/app/dashboard/admin/labels/DatabaseLabelCategory.ts`
`DatabaseLabelCategory` (Line 1)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 61)

## File: `src/types/app/dashboard/admin/labels/LabelCategory.ts`
`LabelCategory` (Line 1)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 62)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 11)
- Used in: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts` (Line 2)
- Used in: `src/types/components/labels/LabelCategoryEditDialogProps.ts` (Line 1)

## File: `src/types/app/dashboard/admin/labels/LabelData.ts`
`LabelType` (Line 12)
`Label` (Line 26)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 64)

`LABEL_TYPES` (Line 1)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 63)

`LabelData` (Line 14)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 64)
- Used in: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts` (Line 1)

## File: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts`
`SortableLabelProps` (Line 4)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 65)

## File: `src/types/app/dashboard/admin/users/TableSkeletonProps.ts`
`TableSkeletonProps` (Line 2)
- Used in: `src/app/dashboard/admin/users/table-skeleton.tsx` (Line 3)

## File: `src/types/app/dashboard/handbooks/HandbookSearchProps.ts`
`HandbookSearchProps` (Line 3)
- Used in: `src/app/dashboard/handbooks/handbook-search.tsx` (Line 21)

## File: `src/types/components/BreadcrumbsProps.ts`
`BreadcrumbsProps` (Line 1)
- Used in: `src/components/Breadcrumbs.tsx` (Line 3)

## File: `src/types/components/HandbookSelectorProps.ts`
`HandbookSelectorProps` (Line 1)
- Used in: `src/components/ClientHandbookSelector.tsx` (Line 12)

## File: `src/types/components/MenuItemProps.ts`
`MenuItemProps` (Line 3)
- Used in: `src/components/MenuItem.tsx` (Line 9)

## File: `src/types/components/NoAccessProps.ts`
`NoAccessProps` (Line 1)
- Used in: `src/components/no-access.tsx` (Line 5)

## File: `src/types/components/UserAvatarMenuProps.ts`
`UserAvatarMenuProps` (Line 4)
- Used in: `src/components/user-avatar-menu.tsx` (Line 15)

## File: `src/types/components/auth/AuthCardProps.ts`
`AuthCardProps` (Line 3)
- Used in: `src/components/auth/auth-card.tsx` (Line 13)

## File: `src/types/components/auth/AuthContainerProps.ts`
`AuthContainerProps` (Line 3)
- Used in: `src/components/auth/auth-container.tsx` (Line 6)

## File: `src/types/components/auth/ComponentProps.ts`
`ComponentProps` (Line 1)
- Used in: `src/components/auth/with-role-check.tsx` (Line 6)

## File: `src/types/components/auth/UserAuthFormProps.ts`
`UserAuthFormProps` (Line 4)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 14)

## File: `src/types/components/flows/FlowCardProps.ts`
`FlowCardProps` (Line 3)
- Used in: `src/components/flows/FlowCard.tsx` (Line 27)

## File: `src/types/components/flowsteps/FlowStep.ts`
`FLOW_STEP_TYPES` (Line 1)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 18)

`FlowStep` (Line 14)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 19)
- Used in: `src/types/components/flowsteps/FlowStepEditDialogProps.ts` (Line 1)

`FlowStepType` (Line 12)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 19)

## File: `src/types/components/flowsteps/FlowStepCategoryEditDialogProps.ts`
`FlowStepCategoryEditDialogProps` (Line 3)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 12)

## File: `src/types/components/flowsteps/FlowStepEditDialogProps.ts`
`FlowStepEditDialogProps` (Line 3)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 20)

## File: `src/types/components/labels/Label.ts`
`Label` (Line 14)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 7)
- Used in: `src/types/components/labels/LabelEditDialogProps.ts` (Line 1)

`LABEL_TYPES` (Line 1)
`LabelType` (Line 12)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 7)

## File: `src/types/components/labels/LabelCategoryEditDialogProps.ts`
`LabelCategoryEditDialogProps` (Line 3)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 12)

## File: `src/types/components/labels/LabelEditDialogProps.ts`
`LabelEditDialogProps` (Line 3)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 8)

## File: `src/types/components/organization/AllInvitesListProps.ts`
`AllInvitesListProps` (Line 3)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 24)

## File: `src/types/components/organization/CreateInviteButtonProps.ts`
`CreateInviteButtonProps` (Line 1)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 26)

## File: `src/types/components/organization/Invite.ts`
`Invite` (Line 5)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 15)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 25)
- Used in: `src/components/organization/invites-list.tsx` (Line 24)
- Used in: `src/types/components/organization/AllInvitesListProps.ts` (Line 1)
- Used in: `src/types/components/organization/InvitesListProps.ts` (Line 1)

## File: `src/types/components/organization/InviteCodeDisplayProps.ts`
`InviteCodeDisplayProps` (Line 1)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 15)

## File: `src/types/components/organization/InviteData.ts`
`InviteData` (Line 1)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 27)

## File: `src/types/components/organization/InvitesListProps.ts`
`InvitesListProps` (Line 3)
- Used in: `src/components/organization/invites-list.tsx` (Line 25)

## File: `src/types/components/organization/JoinOrganizationModalProps.ts`
`JoinOrganizationModalProps` (Line 1)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 17)

## File: `src/types/components/organization/Organization.ts`
`Organization` (Line 2)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 17)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 61)
- Used in: `src/types/components/organization/Invite.ts` (Line 3)

## File: `src/types/components/organization/OrganizationListProps.ts`
`OrganizationListProps` (Line 3)
- Used in: `src/components/organization/organization-list.tsx` (Line 17)

## File: `src/types/components/organization/OrganizationNavProps.ts`
`OrganizationNavProps` (Line 1)
- Used in: `src/components/organization/organization-nav.tsx` (Line 4)

## File: `src/types/components/organization/OrganizationSwitcherProps.ts`
`OrganizationSwitcherProps` (Line 3)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 13)

## File: `src/types/components/organization/OrganizationTableProps.ts`
`OrganizationTableProps` (Line 3)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 47)

## File: `src/types/components/organization/Role.ts`
`Role` (Line 2)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 62)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 3)

## File: `src/types/components/profile/PersonalInfo.ts`
`PersonalInfo` (Line 9)
- Used in: `src/types/components/profile/ProfileFormProps.ts` (Line 3)

## File: `src/types/components/profile/Profile.ts`
`Profile` (Line 9)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 4)
- Used in: `src/types/components/organization/Invite.ts` (Line 2)
- Used in: `src/types/components/profile/ProfileFormProps.ts` (Line 2)

## File: `src/types/components/profile/ProfileFormProps.ts`
`ProfileFormProps` (Line 5)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 31)

## File: `src/types/components/providers/DashboardProviderProps.ts`
`DashboardProviderProps` (Line 5)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 4)

## File: `src/types/components/shared/IconProps.ts`
`IconProps` (Line 1)
- Used in: `src/components/shared/Icon.tsx` (Line 3)

## File: `src/types/components/shared/TooltipProps.ts`
`TooltipProps` (Line 4)
- Used in: `src/components/shared/Tooltip.tsx` (Line 1)

## File: `src/types/components/ui/SheetContentProps.ts`
`SheetContentProps` (Line 6)
- Used in: `src/components/ui/sheet.tsx` (Line 52)

## File: `src/types/flows.ts`
`Flow` (Line 27)
- Used in: `src/types/components/flows/FlowCardProps.ts` (Line 1)

`StepType` (Line 15)
`Step` (Line 17)
- Used in: `src/components/flows/FlowCard.tsx` (Line 20)

`STEP_TYPES` (Line 1)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 10)
- Used in: `src/components/flows/FlowCard.tsx` (Line 20)

## File: `src/types/handbook.ts`
`SubItem` (Line 1)
`CriteriaItem` (Line 5)
`Criteria` (Line 16)
`ServicePlace` (Line 22)
`ServiceChannels` (Line 30)
`ProcedureStep` (Line 34)
`Procedure` (Line 39)
`Procedures` (Line 48)
`RequiredDocument` (Line 53)
`RequiredDocuments` (Line 62)
`FeeItem` (Line 66)
`Fee` (Line 72)
`ComplaintChannel` (Line 76)
`ComplaintChannels` (Line 86)
`ApplicationForm` (Line 90)
`ApplicationForms` (Line 96)
`SubRemark` (Line 100)
`BottomRemarks` (Line 104)
`Source` (Line 110)
`Service` (Line 115)
`HandbookProps` (Line 133)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 29)

`Handbook` (Line 120)
- Used in: `src/types/app/dashboard/handbooks/HandbookSearchProps.ts` (Line 1)

## File: `src/types/hooks/AvailableRole.ts`
`AvailableRole` (Line 1)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 9)

## File: `src/types/hooks/MemberUpdatingState.ts`
`MemberUpdatingState` (Line 1)
`UpdatingStates` (Line 7)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 11)

## File: `src/types/hooks/State.ts`
`State` (Line 16)
- Used in: `src/hooks/use-toast.ts` (Line 11)

## File: `src/types/hooks/UseRealtimeSubscriptionProps.ts`
`UseRealtimeSubscriptionProps` (Line 4)
- Used in: `src/hooks/use-realtime-subscription.ts` (Line 7)

## File: `src/types/lib/rbac/index.ts`
`ActionConfig` (Line 278)
- Used in: `src/lib/rbac/server-action.ts` (Line 2)

`ExtractRbacConditions` (Line 200)
- Used in: `src/lib/rbac/server-action-multi.ts` (Line 2)
- Used in: `src/lib/rbac/server-action-org.ts` (Line 2)
- Used in: `src/lib/rbac/server-action.ts` (Line 2)

`MultiPermissionConfig` (Line 306)
- Used in: `src/lib/rbac/server-action-multi.ts` (Line 2)

`OrgActionConfig` (Line 292)
- Used in: `src/lib/rbac/server-action-org.ts` (Line 2)

`PermissionCheckOptions` (Line 244)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 5)

`PermissionResult` (Line 65)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 11)
- Used in: `src/lib/rbac/role-utils.ts` (Line 6)

`RbacConditions` (Line 156)
- Used in: `src/components/rbac/restricted.tsx` (Line 5)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 11)
- Used in: `src/lib/permissions-service-client.ts` (Line 4)
- Used in: `src/lib/rbac/middleware.ts` (Line 3)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 5)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 2)

`RoleId` (Line 35)
`RoleHelpers` (Line 85)
- Used in: `src/lib/rbac/role-utils.ts` (Line 6)

`RoleKey` (Line 29)
- Used in: `src/app/dashboard/clients/page.tsx` (Line 3)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 11)
- Used in: `src/lib/rbac/role-utils.ts` (Line 6)

## File: `src/types/navigation.ts`
`NavigationItem` (Line 3)
- Used in: `src/components/Sidebar.tsx` (Line 10)

`NavigationSection` (Line 11)
- Used in: `src/data/navigation.ts` (Line 1)

## File: `src/types/organization.ts`
`Organization` (Line 1)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 6)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 9)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 28)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 5)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 7)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 18)
- Used in: `src/components/shared/member-table.tsx` (Line 77)
- Used in: `src/components/Sidebar.tsx` (Line 11)
- Used in: `src/components/TopBar.tsx` (Line 8)
- Used in: `src/hooks/use-organization-context.ts` (Line 5)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 10)
- Used in: `src/hooks/use-organization-storage.ts` (Line 4)
- Used in: `src/hooks/use-organizations-list.ts` (Line 3)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 6)
- Used in: `src/lib/get-user-active-organization.ts` (Line 2)
- Used in: `src/lib/organization-utils-server.ts` (Line 2)
- Used in: `src/types/components/organization/OrganizationListProps.ts` (Line 1)
- Used in: `src/types/components/organization/OrganizationSwitcherProps.ts` (Line 1)
- Used in: `src/types/components/organization/OrganizationTableProps.ts` (Line 1)
- Used in: `src/types/components/providers/DashboardContextType.ts` (Line 2)
- Used in: `src/types/components/providers/DashboardProviderProps.ts` (Line 3)

## File: `src/types/organization/Organization.ts`
`Organization` (Line 5)
- Used in: `src/types/organization/index.ts` (Line 1)

## File: `src/types/organization/OrganizationMember.ts`
`OrganizationMember` (Line 5)
- Used in: `src/types/organization/index.ts` (Line 2)

`OrganizationMemberBasic` (Line 22)
- Used in: `src/app/api/organization-members/remove/route.ts` (Line 4)
- Used in: `src/app/api/organization-members/update-profile/route.ts` (Line 4)
- Used in: `src/app/api/organization-members/update-role/route.ts` (Line 4)
- Used in: `src/app/api/organization-members/update-status/route.ts` (Line 4)
- Used in: `src/app/api/user-profile/[userId]/route.ts` (Line 5)
- Used in: `src/components/shared/member-table.tsx` (Line 35)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 7)
- Used in: `src/lib/permission-utils.ts` (Line 2)
- Used in: `src/types/organization/index.ts` (Line 2)

## File: `src/types/organization/OrganizationMemberWithOrg.ts`
`OrganizationMemberWithOrg` (Line 1)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 3)

## File: `src/types/organization/OrganizationMembershipResponse.ts`
`OrganizationMembershipResponse` (Line 4)
- Used in: `src/types/organization/index.ts` (Line 4)

## File: `src/types/user/UserPersonalInfo.ts`
`UserPersonalInfo` (Line 5)
- Used in: `src/types/user/index.ts` (Line 2)

## File: `src/types/user/UserProfile.ts`
`UserProfile` (Line 4)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 16)
- Used in: `src/types/user/index.ts` (Line 1)

## File: `src/types/user/index.ts`
`UserPersonalInfo` (Line 13)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 8)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 30)
- Used in: `src/types/app/dashboard/admin/users/OrganizationMemberResponse.ts` (Line 1)
- Used in: `src/types/components/profile/PersonalInfo.ts` (Line 4)
- Used in: `src/types/organization/index.ts` (Line 3)

`UserProfile` (Line 13)
- Used in: `src/types/components/profile/Profile.ts` (Line 4)

## File: `src/utils/supabase/client.ts`
`createClient` (Line 3)
- Used in: `src/hooks/use-auth.ts` (Line 5)

