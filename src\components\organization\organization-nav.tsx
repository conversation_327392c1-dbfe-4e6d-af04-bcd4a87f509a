import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { OrganizationNavProps } from "@/types/components/organization/OrganizationNavProps";

export function OrganizationNav({ orgId, className, isAdmin }: OrganizationNavProps) {
  const pathname = usePathname();

  return (
    <nav className={cn("flex items-center space-x-4 lg:space-x-6", className)}>
      <Link
        href={`/dashboard/organization/${orgId}`}
        className={cn(
          "text-sm font-medium transition-colors hover:text-primary",
          pathname === `/dashboard/organization/${orgId}`
            ? "text-primary"
            : "text-muted-foreground"
        )}
      >
        Overview
      </Link>
      <Link
        href={`/dashboard/organization/${orgId}/members`}
        className={cn(
          "text-sm font-medium transition-colors hover:text-primary",
          pathname === `/dashboard/organization/${orgId}/members`
            ? "text-primary"
            : "text-muted-foreground"
        )}
      >
        Members
      </Link>
      {isAdmin && (
        <Link
          href={`/dashboard/admin/organization/${orgId}/invites`}
          className={cn(
            "text-sm font-medium transition-colors hover:text-primary",
            pathname === `/dashboard/admin/organization/${orgId}/invites`
              ? "text-primary"
              : "text-muted-foreground"
          )}
        >
          Invites
        </Link>
      )}
      {isAdmin && (
        <Link
          href={`/dashboard/admin/organization/${orgId}/settings`}
          className={cn(
            "text-sm font-medium transition-colors hover:text-primary",
            pathname === `/dashboard/admin/organization/${orgId}/settings`
              ? "text-primary"
              : "text-muted-foreground"
          )}
        >
          Settings
        </Link>
      )}
    </nav>
  );
}
