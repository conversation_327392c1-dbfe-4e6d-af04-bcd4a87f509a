"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createClient } from "@/lib/supabase/client";

/**
 * Full‑page loading / auth‑callback screen that shows
 * the night‑sky airplane animation while we negotiate auth.
 *
 * 1. Authentication logic is IDENTICAL to the previous version.
 * 2. While loading (`isLoading`), we render <NightSkyAnimation />.
 * 3. On error, we fall back to the original error UI to keep clarity.
 */
export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState("Verifying your authentication…");
  const [isRedirecting, setIsRedirecting] = useState(false);

  /* -----------------------------  Auth logic  ------------------------------*/

  // Helper function to handle successful redirects with animation
  const handleSuccessfulRedirect = async (redirectTo: string, message: string = "Success! Redirecting…") => {
    setLoadingMessage(message);
    setIsRedirecting(true);

    // Show success message for at least 1.5 seconds to let users see the animation
    await new Promise(resolve => setTimeout(resolve, 1500));

    router.replace(redirectTo);
  };

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createClient();
        const redirectTo = searchParams.get("redirectTo") ?? "/dashboard";

        // Give Supabase a moment to auto‑handle hash/PKCE flows and ensure animation shows
        await new Promise((r) => setTimeout(r, 1200));

        const {
          data: { session: existingSession },
          error: sessionErr,
        } = await supabase.auth.getSession();
        if (!sessionErr && existingSession) {
          await handleSuccessfulRedirect(redirectTo, "Welcome back! Redirecting…");
          return;
        }

        const code = searchParams.get("code");
        const hasHash = typeof window !== "undefined" && window.location.hash;

        /* ---------- 1. Fragment flow (magic‑link / implicit) ---------- */
        if (hasHash && !code) {
          const hash = new URLSearchParams(window.location.hash.substring(1));

          // Check for error parameters in hash first
          const hashError = hash.get("error");
          const hashErrorDescription = hash.get("error_description");
          if (hashError) {
            setLoadingMessage("Authentication failed…");
            await new Promise(resolve => setTimeout(resolve, 800)); // Show message briefly
            const errorMessage = hashErrorDescription || hashError || "Authentication failed";
            setError(errorMessage);
            setTimeout(() => {
              router.replace('/auth/login?error=' + encodeURIComponent(errorMessage));
            }, 3000);
            return;
          }

          const access_token = hash.get("access_token");
          const refresh_token = hash.get("refresh_token");
          if (access_token && refresh_token) {
            setLoadingMessage("Creating your session…");
            const { error: setErr } = await supabase.auth.setSession({
              access_token,
              refresh_token,
            });
            if (!setErr) {
              window.history.replaceState(
                {},
                document.title,
                window.location.pathname + window.location.search
              );
              await handleSuccessfulRedirect(redirectTo, "Login successful! Welcome back…");
              return;
            }
            setError("Failed to create session from tokens");
            setTimeout(() => {
              router.replace('/auth/login?error=' + encodeURIComponent('Failed to create session from tokens'));
            }, 3000);
            return;
          }
          setError("Invalid authentication token format");
          setTimeout(() => {
            router.replace('/auth/login?error=' + encodeURIComponent('Invalid authentication token format'));
          }, 3000);
          return;
        }

        /* ---------- 2. OAuth PKCE flow (code param) ---------- */
        if (code && !hasHash) {
          setLoadingMessage("Completing OAuth authentication…");
          let attempts = 0;
          while (attempts < 5) {
            await new Promise((r) => setTimeout(r, 500));
            const { data, error: sErr } = await supabase.auth.getSession();
            if (!sErr && data?.session) {
              await handleSuccessfulRedirect(redirectTo, "OAuth login successful! Redirecting…");
              return;
            }
            attempts++;
          }
          setError("OAuth authentication timed out. Please try again.");
          setTimeout(() => {
            router.replace('/auth/login?error=' + encodeURIComponent('OAuth authentication timed out. Please try again.'));
          }, 3000);
          return;
        }

        /* ---------- 3. No valid flow ---------- */
        const errorParam = searchParams.get("error");
        if (errorParam) {
          setLoadingMessage("Checking authentication status…");
          await new Promise(resolve => setTimeout(resolve, 800)); // Show message briefly
          const errorDescription = searchParams.get("error_description") ?? "Authentication failed";
          setError(errorDescription);

          // Redirect to login after showing error for 3 seconds
          setTimeout(() => {
            router.replace('/auth/login?error=' + encodeURIComponent(errorDescription));
          }, 3000);
          return;
        }

        setError("No valid authentication data found. Please try logging in again.");

        // Redirect to login after showing error for 3 seconds
        setTimeout(() => {
          router.replace('/auth/login?error=' + encodeURIComponent('No valid authentication data found'));
        }, 3000);
      } catch (e) {
        console.error("[Auth Callback]", e);
        setError("An unexpected error occurred during authentication");
        setTimeout(() => {
          router.replace('/auth/login?error=' + encodeURIComponent('An unexpected error occurred during authentication'));
        }, 3000);
      } finally {
        setIsLoading(false);
      }
    };
    handleAuthCallback();
  }, [router, searchParams]);

  /* -------------------------------  UI  -----------------------------------*/
  if (error) {
    return <NightSkyAnimation msg={`❌ ${error}`} showRedirectMessage={true} />;
  }

  if (isLoading || isRedirecting) {
    return <NightSkyAnimation msg={loadingMessage} />;
  }

  // Fallback (should be rare) – tiny spinner
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900" />
    </div>
  );
}

/* --------------------------------------------------------------------------
   Night‑Sky Animation component (pure client, no SSR DOM usage on server)
   ------------------------------------------------------------------------*/
function NightSkyAnimation({ msg, showRedirectMessage = false }: { msg?: string; showRedirectMessage?: boolean }) {

  /* Inject stars once on mount */
  useEffect(() => {
    const scene = document.querySelector<HTMLElement>(".scene");
    if (!scene) return;

    // Small delay to ensure background is painted first
    const timer = setTimeout(() => {
      const w = window.innerWidth;
      const h = window.innerHeight;
      for (let i = 0; i < 120; i++) {
        const star = document.createElement("div");
        star.className = "star";
        star.style.left = `${Math.random() * w}px`;
        star.style.top = `${Math.random() * h * 0.5}px`;
        star.style.animationDelay = `${Math.random() * 4}s`;
        scene.appendChild(star);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      scene.querySelectorAll(".star").forEach((s) => s.remove());
    };
  }, []);

  return (
    <div
      className="hero-section relative"
      style={{
        background: 'linear-gradient(135deg, #0c1445 0%, #1e3c72 20%, #2a5298 40%, #1e3c72 60%, #0f1419 80%, #000000 100%)',
        minHeight: '100vh',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Clouds */}
      <div id="cloud-wrap">
        <div className="cloud x1" />
        <div className="cloud x2" />
        <div className="cloud x3" />
        <div className="cloud x4" />
        <div className="cloud x5" />
      </div>

      {/* Stars & airplane */}
      <div className="scene">
        <div
          className="plane"
          style={{
            position: 'absolute',
            top: '40vh',
            left: '-320px',
            width: '320px',
            height: '136px',
            zIndex: 2,
            filter: 'drop-shadow(0 0 12px rgba(255,255,255,.4))',
            opacity: 0,
            animation: 'fadeInPlane 0.5s ease-in-out 0.5s forwards, fly 25s linear 1s infinite'
          }}
        >
          <img
            src="/images/plane.png"
            alt="Airplane"
            style={{ width: '100%', height: '100%', objectFit: 'contain' }}
          />
        </div>
      </div>

      {/* Optional loading text */}
      {msg && (
        <div className="absolute inset-0 flex flex-col items-center justify-center z-20 text-center pointer-events-none">
          <h2 className="text-3xl font-bold text-white drop-shadow-lg" style={{textShadow: '0 0 20px rgba(255,255,255,0.5), 0 2px 4px rgba(0,0,0,0.8)'}}>{msg}</h2>
          <p className="text-sm text-gray-200 mt-2 drop-shadow-md" style={{textShadow: '0 1px 2px rgba(0,0,0,0.8)'}}>
            {showRedirectMessage ? 'Redirecting to login page…' : 'Processing authentication…'}
          </p>
        </div>
      )}

      {/* Animation styles */}
      <style jsx global>{`
        body { margin: 0; padding: 0; }
        .hero-section{
          height:100vh;
          display:flex;
          align-items:center;
          justify-content:center;
          background:linear-gradient(135deg, #0c1445 0%, #1e3c72 20%, #2a5298 40%, #1e3c72 60%, #0f1419 80%, #000000 100%) !important;
          position:relative;
          overflow:hidden;
        }
        .scene{position:relative;width:100%;height:100vh;overflow:hidden}
        /* stars */
        .star{position:absolute;width:2px;height:2px;background:#fff;border-radius:50%;box-shadow:0 0 6px rgba(255,255,255,.9);pointer-events:none;animation:twinkle 4s ease-in-out infinite;z-index:1}
        @keyframes twinkle{0%,100%{opacity:0;transform:scale(1)}50%{opacity:1;transform:scale(1.6)}}
        /* clouds */
        #cloud-wrap{position:fixed;top:55vh;left:0;right:0;bottom:0;overflow:visible;pointer-events:none;z-index:0}
        @keyframes cloudDrift{0%{margin-left:-400px}100%{margin-left:100%}}
        .cloud{position:relative;width:350px;height:120px;background:linear-gradient(to bottom,#fff 5%,#f1f1f1 100%);border-radius:100px;box-shadow:0 8px 5px rgba(0,0,0,.15);opacity:.8}
        .cloud:before,.cloud:after{content:'';position:absolute;background:#fff;z-index:-1}
        .cloud:before{width:180px;height:180px;top:-90px;right:50px;border-radius:200px}
        .cloud:after{width:100px;height:100px;top:-50px;left:50px;border-radius:100px}
        .x1{animation:cloudDrift 150s linear infinite -30s;transform:scale(.65)}
        .x2{animation:cloudDrift  90s linear infinite -50s;transform:scale(.30)}
        .x3{animation:cloudDrift 120s linear infinite -70s;transform:scale(.50)}
        .x4{animation:cloudDrift  85s linear infinite -20s;transform:scale(.40)}
        .x5{animation:cloudDrift 110s linear infinite -60s;transform:scale(.55)}
        /* plane */
        .plane{position:absolute;top:40vh;left:-320px;width:320px;height:136px;z-index:2;filter:drop-shadow(0 0 12px rgba(255,255,255,.4));opacity:0;animation:fadeInPlane 0.5s ease-in-out 0.5s forwards, fly 25s linear 1s infinite;}
        .plane img{width:100%;height:100%;object-fit:contain}
        @keyframes fadeInPlane{0%{opacity:0}100%{opacity:1}}
        @keyframes fly{0%{left:-320px;transform:translateY(0) rotate(0deg)}50%{transform:translateY(-12px) rotate(-3deg)}100%{left:105%;transform:translateY(0) rotate(0deg)}}
        @media(max-width:768px){.plane{width:200px;height:85px;top:45vh;animation-duration:20s}}
      `}</style>
    </div>
  );
}
