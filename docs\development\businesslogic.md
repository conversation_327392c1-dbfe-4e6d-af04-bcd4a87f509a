# Business Logic Adherence Report

This report assesses the application's current logic against predefined scenarios, focusing on how changes to user roles, user status within an organization, and organization status are handled.

## Scenario Analysis Key:

*   **Scenario #**: Unique identifier for the test case.
*   **Initial State**:
    *   **Role**: User's role (Superadmin/Non-superadmin).
    *   **Org Status**: Status of the currently active organization (Active/Inactive).
    *   **User Status**: User's status within the currently active organization (Active/Inactive).
*   **Transition Axis**: The type of entity whose status/attribute is changing (User, Role, Organization).
*   **Change**: The specific modification being made.
*   **Expected Results**: The desired application behavior upon receiving and processing the event.
*   **Current Behavior Analysis**: Detailed analysis of how the code *currently* handles this scenario.
    *   **Event Trigger & Handling**: Which event is fired, how `DashboardEventManager` processes it.
    *   **Context Resolution**: How `auth-context.ts` (via middleware or direct calls) and `DashboardProvider` update.
    *   **Middleware Action**: How `middleware.ts` behaves on subsequent requests or redirects.
    *   **UI Updates**: How `Sidebar.tsx` (navigation, notifications) and `OrganizationSwitcher.tsx` (dropdown, active display) react.
    *   **Redirects**: Expected vs. actual redirection behavior.
*   **Pass/Fail**: Whether the current behavior matches the expected results.
*   **Actionable Tasks**: Specific code changes needed if the scenario fails or needs refinement.

---

## Detailed Scenario Breakdown:

### Transition Axis: Organization

#### Scenario 1

*   **Initial State**:
    *   **Role**: superadmin
    *   **Org Status**: active
    *   **User Status**: active
*   **Transition Axis**: Organization
*   **Change**: org status: active → inactive
*   **Expected Results**:
    *   Display 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: Superadmin retains access to disabled organization if user account is active
*   **Current Behavior Analysis**:
    1.  **Event Trigger**: Active organization's `is_active` field changes from `true` to `false` in DB.
    2.  **Realtime Event Handling**: `OrganizationSwitcher.tsx` has a Supabase realtime subscription for `organizations` table updates where `is_active=eq.false`. This subscription will fire for the active organization.
    3.  **`OrganizationSwitcher.tsx` Reaction**:
        *   It identifies that the `selectedOrg` (active org) is the one that became inactive.
        *   It calls `router.push("/dashboard/organization-disabled")`.
        *   It also calls `setTimeout(() => { router.refresh(); }, 100)`.
    4.  **`middleware.ts` (during navigation to `/dashboard/organization-disabled` and subsequent `router.refresh()`)**:
        *   `resolveUserAuthContext(user.id)` is called. For the current org, it now gets `isOrgActive: false`. The user is superadmin and active, so `isSuperAdmin: true`, `isUserActive: true`.
        *   Headers like `x-auth-is-org-active: false`, `x-auth-is-superadmin: true`, `x-auth-is-user-active: true` are set on the response.
        *   The navigation to `/dashboard/organization-disabled` occurs. This path is typically in `BYPASS_STATUS_REDIRECT_PATHS`, so the middleware itself won't cause a further redirect *from* this page based on status.
    5.  **`DashboardLayout` (rendering `/dashboard/organization-disabled`)**:
        *   Reads headers: `x-auth-is-org-active: false`, `x-auth-is-superadmin: true`, `x-auth-is-user-active: true`.
        *   `DashboardProvider` is initialized: `isActiveOrgDisabled` becomes `true`. `isUserInactive` is `false`. `isSuperAdmin` (from user context/role) is `true`.
    6.  **UI Updates**:
        *   **Sidebar.tsx**: `isActiveOrgDisabled` is `true`, `isUserInactive` is `false`, `isSuperAdmin` is `true`.
            *   'Organization disabled' (standard version, as special SA version is to be removed) notification is displayed.
            *   'Account disabled' notification is not displayed.
            *   `shouldShowNavigation` remains `true` (superadmin can see nav in inactive org if their account is active). Navigation items re-evaluate with `rbacEvaluate` against the current context.
        *   **`OrganizationSwitcher.tsx`**: Fetches updated org list (e.g., via `/api/organizations/authorized` or reflects the change from its own state update). Dropdown item and main display for the active org show "Organization disabled".
    7.  **Redirect**: The system currently redirects superadmins to `/dashboard/organization-disabled` due to `OrganizationSwitcher`'s action.
*   **Pass/Fail**: Fail. Expected: Superadmin stays on current arbitrary page. Current: Redirected to `/dashboard/organization-disabled`.
*   **Actionable Tasks**:
    *   [x] Modify `OrganizationSwitcher.tsx`: If the active org becomes inactive AND the user is a superadmin (check via `DashboardProvider` context or refreshed auth context), it should *not* call `router.push("/dashboard/organization-disabled")`. Instead, it should only call `router.refresh()` (or trigger `refreshServerContextAndSoftRefresh()` if that's the standard pattern) to ensure the context is updated across the app. The superadmin should remain on their current page.
    *   [ ] Ensure `Sidebar.tsx` displays the standard 'Organization disabled' notification (since the special SA version is being removed).
    *   [ ] Ensure `rbacEvaluate` correctly filters navigation for a superadmin in an inactive org context on their current page.

---

#### Scenario 2

*   **Initial State**:
    *   **Role**: Any
    *   **Org Status**: active
    *   **User Status**: inactive
*   **Transition Axis**: Organization
*   **Change**: org status: active → inactive
*   **Expected Results**:
    *   Display 'Organization disabled' sidebar notification.
    *   The 'Account disabled' sidebar notification is expected.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: User account already inactive and user already on account disabled page
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User is likely on `/dashboard/account-disabled` page.
    2.  **Event Trigger**: Active organization's `is_active` field changes from `true` to `false`.
    3.  **`OrganizationSwitcher.tsx` Realtime**: Its subscription fires.
        *   It detects the active org (`selectedOrg`) became inactive.
        *   It calls `router.push("/dashboard/organization-disabled")`.
        *   It calls `router.refresh()`.
    4.  **`middleware.ts` (navigating to `/dashboard/organization-disabled` and during `router.refresh()`)**:
        *   `resolveUserAuthContext` gets `isOrgActive: false`, `isUserActive: false` (user was already inactive). `isSuperAdmin` depends on the role.
        *   Headers `x-auth-is-org-active: false`, `x-auth-is-user-active: false` set.
        *   The user is already on `/dashboard/account-disabled` (a bypass path). The `router.push` from `OrganizationSwitcher` will attempt to navigate to `/dashboard/organization-disabled`.
        *   When landing on `/dashboard/organization-disabled` (also a bypass path), middleware won't cause further status-based redirects.
    5.  **`DashboardLayout` (rendering `/dashboard/organization-disabled`)**:
        *   Headers: `x-auth-is-org-active: false`, `x-auth-is-user-active: false`.
        *   `DashboardProvider`: `isActiveOrgDisabled` becomes `true`. `isUserInactive` remains `true`.
    6.  **UI Updates (on `/dashboard/organization-disabled`)**:
        *   **Sidebar.tsx**: `isActiveOrgDisabled` is `true`, `isUserInactive` is `true`.
            *   'Organization disabled' notification displayed.
            *   'Account disabled' notification remains/displayed.
            *   `shouldShowNavigation` is `false` (due to `isUserInactive` taking precedence). Navigation hidden, `rbacEvaluate` is not relevant for visibility here.
        *   **`OrganizationSwitcher.tsx`**: Updates to show org as "Organization disabled".
    7.  **Redirect**: User is moved from `/dashboard/account-disabled` to `/dashboard/organization-disabled`. Expected "No redirect" could mean they should stay on `/dashboard/account-disabled`.
*   **Pass/Fail**: Partial Fail. The redirect to `/dashboard/organization-disabled` occurs, while "No redirect expected" (implying stay on `/dashboard/account-disabled`) is the expectation. The account being inactive is the primary reason for restricted access.
*   **Actionable Tasks**:
    *   [x] In `OrganizationSwitcher.tsx`, if the active org becomes inactive BUT the user is already inactive (`isUserInactive` from `DashboardProvider` context is true), it should potentially not `router.push` to `/dashboard/organization-disabled`. Instead, just `router.refresh()` might be enough to update the context for the `OrganizationSwitcher` display itself. The user should remain on `/dashboard/account-disabled`.
    *   [ ] Prioritize user status over org status for page determination if user is inactive.

---

#### Scenario 3

*   **Initial State**:
    *   **Role**: Any
    *   **Org Status**: inactive
    *   **User Status**: inactive
*   **Transition Axis**: Organization
*   **Change**: org status: inactive → active
*   **Expected Results**:
    *   Remove 'Organization disabled' sidebar notification.
    *   The 'Account disabled' sidebar notification is expected.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: User account already inactive and user already on account disabled page
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User is likely on `/dashboard/account-disabled` or `/dashboard/organization-disabled`.
    2.  **Event Trigger**: Active organization's `is_active` field changes from `false` to `true`.
    3.  **Realtime Event Handling**:
        *   The `OrganizationSwitcher`'s current realtime subscription is filtered for `is_active=eq.false`. It will **not** pick up an organization becoming active. This is a known gap (from old Scenario 7 analysis).
        *   `DashboardEventManager` (DEM) uses the `organization:statusChanged` event to detect when the *active* organization context changes to trigger an immediate refresh.
    4.  **Consequence of No Direct Event Handling for Org Activation**:
        *   No immediate `refreshServerContextAndSoftRefresh()` or `router.refresh()`.
        *   User remains on their current page (e.g., `/dashboard/account-disabled` or `/dashboard/organization-disabled`).
        *   Context (`isActiveOrgDisabled`) does not update in realtime. 'Organization disabled' notification persists. 'Account disabled' notification also persists. Navigation remains hidden due to `isUserInactive`.
    5.  **On Manual Refresh/Navigation**:
        *   `middleware.ts`: `resolveUserAuthContext` gets `isOrgActive: true`, `isUserActive: false`.
        *   Headers `x-auth-is-org-active: true`, `x-auth-is-user-active: false` set.
        *   If user was on `/dashboard/organization-disabled` (bypass), they stay. If they were on `/dashboard/account-disabled` (bypass), they stay. If they try to navigate to e.g. `/dashboard`, middleware will redirect to `/dashboard/account-disabled` because `!authContext.isUserActive`.
        *   `DashboardLayout`: `isActiveOrgDisabled` becomes `false`. `isUserInactive` remains `true`.
        *   **Sidebar.tsx**: 'Organization disabled' notification removed. 'Account disabled' notification remains. `shouldShowNavigation` remains `false`.
        *   **`OrganizationSwitcher.tsx`**: Shows org as active with user's role (or "Inactive Account" due to user status).
*   **Pass/Fail**: Fail (for realtime update). Pass (on manual refresh, outcome aligns mostly, especially "No redirect" from account-disabled page and notifications).
*   **Actionable Tasks**:
    *   [x] **Critical**: Implement realtime detection for active organization activation (using `organization:statusChanged` event in `DEM` or a new Supabase subscription in `OrganizationSwitcher` / `DEM`).
    *   [x] On detection, `DEM` should call `refreshServerContextAndSoftRefresh()`.
    *   [x] Ensure that even after context refresh, if `isUserInactive` is true, the user remains on `/dashboard/account-disabled` (or is redirected there if they somehow ended up on a non-bypass page). "No redirect expected" likely means "stay on account-disabled page".
    *   [x] Update navigation expectation: if user is inactive, nav is hidden, so `rbacEvaluate` for items isn't the primary concern.

---

#### Scenario 4

*   **Initial State**:
    *   **Role**: superadmin
    *   **Org Status**: inactive
    *   **User Status**: active
*   **Transition Axis**: Organization
*   **Change**: org status: inactive → active
*   **Expected Results**:
    *   Remove 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: (empty)
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User (superadmin) is likely on `/dashboard/organization-disabled`.
    2.  **Event Trigger**: Active organization `is_active`: `false` → `true`.
    3.  **Realtime Event Handling**: Gap exists (as in Scenario 3). No immediate reaction.
    4.  **Consequence of No Direct Event Handling**: User stays on `/dashboard/organization-disabled`. 'Organization disabled' notification persists. Navigation remains visible (superadmin, active user).
    5.  **On Manual Refresh/Navigation**:
        *   Assume realtime fix: `DEM` calls `refreshServerContextAndSoftRefresh()`.
        *   **`middleware.ts` (during refresh)**:
            *   `resolveUserAuthContext` gets `isOrgActive: true`, `isUserActive: true`, `isSuperAdmin: true`.
            *   Headers `x-auth-is-org-active: true`, `x-auth-is-user-active: true`, `x-auth-is-superadmin: true` set.
            *   If user was on `/dashboard/organization-disabled` (bypass), they stay. This aligns with the clarified expectation for superadmins to stay on their current arbitrary page.
        *   **`DashboardLayout` (re-rendering current page, e.g. `/dashboard/organization-disabled` or other arbitrary page)**:
            *   `DashboardProvider`: `isActiveOrgDisabled` becomes `false`. `isUserInactive` is `false`.
            *   **Sidebar.tsx**: 'Organization disabled' notification removed. `shouldShowNavigation` remains `true`. Navigation items re-evaluate.
            *   **`OrganizationSwitcher.tsx`**: Shows org as active with superadmin role.
*   **Pass/Fail**: Fail (for immediate realtime update). Pass (on manual refresh, outcome aligns with new expectation to stay on current page).
*   **Actionable Tasks**:
    *   [x] **Critical**: Implement realtime detection for active organization activation (Task 1 in Scratchpad).
    *   [x] `DEM` to call `refreshServerContextAndSoftRefresh()` upon this detection (Task 2.1).
    *   [x] No specific redirect needed from DEM for this scenario as superadmin should stay on their current page. The context refresh will update UI elements like notifications and org switcher display.

---

#### Scenario 5

*   **Initial State**:
    *   **Role**: non-superadmin
    *   **Org Status**: active
    *   **User Status**: active
*   **Transition Axis**: Organization
*   **Change**: org status: active → inactive
*   **Expected Results**:
    *   Display 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard/organization-disabled`.
    *   Comment: (empty)
*   **Current Behavior Analysis**:
    1.  **Event Trigger**: Active organization `is_active`: `true` → `false`.
    2.  **`OrganizationSwitcher.tsx` Realtime**: Subscription fires.
        *   Detects active org became inactive.
        *   Calls `router.push("/dashboard/organization-disabled")`.
        *   Calls `router.refresh()`.
    3.  **`middleware.ts` (navigating to `/dashboard/organization-disabled` and refresh)**:
        *   `resolveUserAuthContext` gets `isOrgActive: false`, `isUserActive: true` (non-superadmin).
        *   Headers `x-auth-is-org-active: false`, `x-auth-is-user-active: true`, `x-auth-is-superadmin: false` set.
        *   Redirect to `/dashboard/organization-disabled` happens from `OrganizationSwitcher`. Middleware on this target page (bypass path) won't cause further redirects. It would also enforce this redirect if `OrganizationSwitcher` hadn't already done it and user tried to access a non-bypass page.
    4.  **`DashboardLayout` (rendering `/dashboard/organization-disabled`)**:
        *   Headers reflect context.
        *   `DashboardProvider`: `isActiveOrgDisabled` becomes `true`. `isUserInactive` is `false`. `isSuperAdmin` is `false`.
    5.  **UI Updates**:
        *   **Sidebar.tsx**: `isActiveOrgDisabled` is `true`, `isUserInactive` is `false`, `isSuperAdmin` is `false`.
            *   'Organization disabled' (non-superadmin version) notification displayed.
            *   `shouldShowNavigation` becomes `false`. Navigation hidden.
        *   **`OrganizationSwitcher.tsx`**: Shows org as "Organization disabled".
*   **Pass/Fail**: Pass. Behavior aligns with expected results.
*   **Actionable Tasks**: None.

---

#### Scenario 6

*   **Initial State**:
    *   **Role**: non-superadmin
    *   **Org Status**: inactive
    *   **User Status**: active
*   **Transition Axis**: Organization
*   **Change**: org status: inactive → active
*   **Expected Results**:
    *   Remove 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard`.
    *   Comment: (empty)
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User (non-superadmin) is on `/dashboard/organization-disabled`.
    2.  **Event Trigger**: Active organization `is_active`: `false` → `true`.
    3.  **Realtime Event Handling**: Gap exists (as in Scenario 3 & 4). No immediate reaction.
    4.  **Consequence of No Direct Event Handling**: User stays on `/dashboard/organization-disabled`. 'Organization disabled' notification persists. Navigation remains hidden.
    5.  **On Manual Refresh/Navigation (or after realtime fix)**:
        *   Assume realtime fix: `DEM` calls `refreshServerContextAndSoftRefresh()`.
        *   Then, `DEM` needs to check if `currentPath === '/dashboard/organization-disabled'` and user is non-superadmin, then `router.push('/dashboard')`.
        *   **`middleware.ts` (during refresh or navigation to `/dashboard`)**:
            *   `resolveUserAuthContext` gets `isOrgActive: true`, `isUserActive: true` (non-superadmin).
            *   Headers reflect active state.
            *   If user tries to access `/dashboard`, middleware allows it.
        *   **`DashboardLayout` (rendering `/dashboard`)**:
            *   `DashboardProvider`: `isActiveOrgDisabled` becomes `false`. `isUserInactive` is `false`.
            *   **Sidebar.tsx**: 'Organization disabled' notification removed. `shouldShowNavigation` becomes `true`. Navigation items re-evaluate.
            *   **`OrganizationSwitcher.tsx`**: Shows org as active.
*   **Pass/Fail**: Fail (for realtime update and automatic redirect from `/dashboard/organization-disabled`). Works correctly on manual navigation to `/dashboard` after context is somehow updated.
*   **Actionable Tasks**:
    *   [x] **Critical**: Implement realtime detection for active organization activation.
    *   [x] `DEM` to call `refreshServerContextAndSoftRefresh()`.
    *   [x] After context refresh, `DEM` should explicitly check if the user is non-superadmin, their account is active, the org just became active, and if they are currently on `/dashboard/organization-disabled`. If all true, then `router.push('/dashboard')`.

---

### Transition Axis: Role

#### Scenario 7

*   **Initial State**:
    *   **Role**: Any (implicitly superadmin, given the change)
    *   **Org Status**: active
    *   **User Status**: active
*   **Transition Axis**: Role
*   **Change**: role: superadmin → non-superadmin
*   **Expected Results**:
    *   No change to 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: (empty)
*   **Current Behavior Analysis**:
    1.  **Event Trigger**: `user:role:changed` event (`{ userId, orgId, newRoleId (non-superadmin), oldRoleId (superadmin) }`) for active org.
    2.  **`DashboardEventManager` (`DEM`)**:
        *   `user:role:changed` handler runs.
        *   If `event.userId === currentUserId && event.orgId === activeOrganization?.id`.
        *   Calls `refreshServerContextAndSoftRefresh()`.
        *   `setNeedsRefresh(true)`. (No automatic redirect from DEM based on role change alone in an active org).
    3.  **`refreshServerContextAndSoftRefresh()`**:
        *   `/api/auth/refresh-context` called. `resolveUserAuthContext` now returns `isSuperAdmin: false`, `orgRoleId` is new non-superadmin ID. `isOrgActive: true`, `isUserActive: true`.
        *   Headers `x-auth-is-superadmin: false`, `x-auth-org-role-id: <new_id>` updated.
        *   `router.refresh()` re-fetches Server Components for the current page.
    4.  **`middleware.ts` (during `router.refresh()`)**:
        *   New auth context (non-superadmin, org active, user active) is picked up.
        *   No status-based redirect as org and user are active.
    5.  **`DashboardLayout` (re-rendering current page)**:
        *   `DashboardProvider`: `userRole` updates. `isSuperAdmin` becomes `false`. `isActiveOrgDisabled`, `isUserInactive` are `false`.
    6.  **UI Updates**:
        *   **Sidebar.tsx**: `isSuperAdmin` is `false`.
            *   No 'Organization disabled' or 'Account disabled' notifications displayed.
            *   `shouldShowNavigation` is `true`. `filteredNavigation` recomputes based on new (non-superadmin) role using `rbacEvaluate`. Some items might disappear.
        *   **`OrganizationSwitcher.tsx`**: Its own `user:role:changed` handler (or general refresh) updates the displayed role.
*   **Pass/Fail**: Pass.
*   **Actionable Tasks**: None.

---

#### Scenario 8

*   **Initial State**:
    *   **Role**: Any (implicitly superadmin)
    *   **Org Status**: active
    *   **User Status**: inactive
*   **Transition Axis**: Role
*   **Change**: role: superadmin → non-superadmin
*   **Expected Results**:
    *   No change to 'Organization disabled' sidebar notification.
    *   The 'Account disabled' sidebar notification is expected.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: User account already inactive and user already on account disabled page
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User is likely on `/dashboard/account-disabled`.
    2.  **Event Trigger**: `user:role:changed` (superadmin → non-superadmin) in active org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   Handles event, calls `refreshServerContextAndSoftRefresh()`.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` now returns `isSuperAdmin: false`, new `orgRoleId`, `isOrgActive: true`, but critically `isUserActive: false`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (during `router.refresh()` on `/dashboard/account-disabled`)**:
        *   New context (non-superadmin, org active, user INACTIVE).
        *   Current path `/dashboard/account-disabled` is a bypass path. No redirect by middleware from here.
    6.  **`DashboardLayout` (re-rendering `/dashboard/account-disabled`)**:
        *   `DashboardProvider`: `userRole` updates. `isSuperAdmin` is `false`. `isUserInactive` is `true`. `isActiveOrgDisabled` is `false`.
    7.  **UI Updates**:
        *   **Sidebar.tsx**: `isUserInactive` is `true`.
            *   'Account disabled' notification remains. No 'Org disabled' notification.
            *   `shouldShowNavigation` remains `false` (due to `isUserInactive`). Navigation hidden. `rbacEvaluate` for items is not relevant for visibility.
        *   **`OrganizationSwitcher.tsx`**: Role display updates. Still shows "Inactive Account" due to user status.
*   **Pass/Fail**: Pass. User inactivity correctly takes precedence.
*   **Actionable Tasks**: None.

---

#### Scenario 9

*   **Initial State**:
    *   **Role**: superadmin
    *   **Org Status**: inactive
    *   **User Status**: active
*   **Transition Axis**: Role
*   **Change**: role: superadmin → non-superadmin
*   **Expected Results**:
    *   Display 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard/organization-disabled`.
    *   Comment: Only superadmin has access to inactive organizations
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User (superadmin) is on `/dashboard/organization-disabled` (can view nav).
    2.  **Event Trigger**: `user:role:changed` (superadmin → non-superadmin) in the current *inactive* org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   Handles event, calls `refreshServerContextAndSoftRefresh()`.
        *   DEM current logic for `user:role:changed` for an active org does not have a conditional redirect if `pathname.includes('/organization-disabled')` and role changes from SA to non-SA.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isSuperAdmin: false`, `orgRoleId` (non-SA), `isOrgActive: false`, `isUserActive: true`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (during `router.refresh()` on `/dashboard/organization-disabled`)**:
        *   New context (non-superadmin, org INACTIVE, user active).
        *   Path `/dashboard/organization-disabled` is bypass. Middleware doesn't redirect from here.
        *   However, if the user *were* to navigate to a non-bypass page, middleware condition `!authContext.isOrgActive && !authContext.isSuperAdmin` would be true, leading to a redirect to `/dashboard/organization-disabled`.
    6.  **`DashboardLayout` (re-rendering `/dashboard/organization-disabled`)**:
        *   `DashboardProvider`: `userRole` non-SA. `isSuperAdmin` false. `isActiveOrgDisabled` true. `isUserInactive` false.
    7.  **UI Updates (on `/dashboard/organization-disabled`)**:
        *   **Sidebar.tsx**: `isSuperAdmin` false, `isActiveOrgDisabled` true.
            *   'Organization disabled' (non-superadmin version) notification displayed.
            *   `shouldShowNavigation` becomes `false`. Navigation hidden.
        *   **`OrganizationSwitcher.tsx`**: Role display updates.
    8.  **Redirect**: The user stays on `/dashboard/organization-disabled`. This matches the expected redirect destination. The question is whether an explicit redirect *action* is expected if they are already there. The system effectively enforces this page.
*   **Pass/Fail**: Pass. The user is already on, or would be forced to, `/dashboard/organization-disabled`. The UI updates correctly.
*   **Actionable Tasks**: None needed; the combination of UI update (hiding nav) and middleware enforcement for non-bypass paths achieves the goal.

---

#### Scenario 10

*   **Initial State**:
    *   **Role**: superadmin (or any, as change is Any -> Any, but context implies role change might be relevant if user status wasn't overriding)
    *   **Org Status**: inactive
    *   **User Status**: inactive
*   **Transition Axis**: Role
*   **Change**: role: Any → Any (e.g. superadmin → non-superadmin)
*   **Expected Results**:
    *   The 'Organization disabled' sidebar notification is expected.
    *   The 'Account disabled' sidebar notification is expected.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   No redirect expected.
    *   Comment: User account already inactive and user already on account disabled page
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User on `/dashboard/account-disabled` or `/dashboard/organization-disabled` (but likely `/account-disabled` due to user status).
    2.  **Event Trigger**: `user:role:changed`.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   Handles event, calls `refreshServerContextAndSoftRefresh()`.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets updated role, but `isOrgActive: false`, `isUserActive: false`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (during refresh on current bypass page)**:
        *   No redirect from bypass page.
    6.  **`DashboardLayout`**:
        *   `DashboardProvider`: `userRole` updates. `isSuperAdmin` might change. `isActiveOrgDisabled` true. `isUserInactive` true.
    7.  **UI Updates**:
        *   **Sidebar.tsx**: `isUserInactive` is `true`.
            *   'Account disabled' notification remains/displayed.
            *   'Organization disabled' notification also remains/displayed.
            *   `shouldShowNavigation` remains `false`. Navigation hidden.
        *   **`OrganizationSwitcher.tsx`**: Role updates, "Inactive Account" still likely shown due to user status.
*   **Pass/Fail**: Pass. User inactivity is dominant. "No redirect" is correct.
*   **Actionable Tasks**: None.

---

#### Scenario 11

*   **Initial State**:
    *   **Role**: non-superadmin
    *   **Org Status**: inactive
    *   **User Status**: active
*   **Transition Axis**: Role
*   **Change**: role: non-superadmin → superadmin
*   **Expected Results**:
    *   Remove 'Organization disabled' sidebar notification.
    *   No change to 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard`.
    *   Comment: Promoted to superadmin, so gets access to disabled organization
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User (non-SA) on `/dashboard/organization-disabled`. Navigation hidden.
    2.  **Event Trigger**: `user:role:changed` (non-SA → SA) in inactive org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   Handles event. Calls `refreshServerContextAndSoftRefresh()`.
        *   Old DEM logic from `businesslogic.md` for `user:role:changed`: if `newRoleId === 1` (superadmin) AND `window.location.pathname.includes('/organization-disabled')`, it calls refresh and *then returns*, implying no automatic redirect *from DEM*.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isSuperAdmin: true`, `orgRoleId` (SA), `isOrgActive: false`, `isUserActive: true`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (refresh on `/dashboard/organization-disabled`)**:
        *   New context (SA, org INACTIVE, user active).
        *   Path `/dashboard/organization-disabled` is bypass. No redirect from middleware here.
    6.  **`DashboardLayout` (re-rendering `/dashboard/organization-disabled`)**:
        *   `DashboardProvider`: `userRole` SA. `isSuperAdmin` true. `isActiveOrgDisabled` true. `isUserInactive` false.
    7.  **UI Updates (on `/dashboard/organization-disabled`)**:
        *   **Sidebar.tsx**: `isSuperAdmin` true, `isActiveOrgDisabled` true.
            *   'Organization disabled' (superadmin version, if different, or generic one if not) notification displayed. The expectation is "Remove", which implies the generic "Org disabled" for non-SA should go away, and SA might see a different variant or none if they can fully operate.
            *   `shouldShowNavigation` becomes `true`. Navigation appears. `rbacEvaluate` filters items.
        *   **`OrganizationSwitcher.tsx`**: Role display updates.
    8.  **Redirect**: User stays on `/dashboard/organization-disabled`. Expected is `/dashboard`.
*   **Pass/Fail**: Fail (on redirect and potentially 'Organization disabled' notification handling). Superadmin gains ability to see nav on org-disabled page, but isn't redirected to main `/dashboard`.
*   **Actionable Tasks**:
    *   [x] In `DashboardEventManager`, for `user:role:changed` to superadmin: if `event.newRoleId === 1` and current org is inactive (`!authContext.isOrgActive` after refresh concept) and user is active:
        *   [x] After `refreshServerContextAndSoftRefresh()`, explicitly call `router.push('/dashboard')`.
    *   [x] Clarify 'Organization disabled' notification for superadmin in an inactive org. Is it removed entirely, or is there a superadmin-specific version? The sidebar logic for notifications needs to reflect this. If they are meant to operate normally, the notification should likely be removed.

---

### Transition Axis: User

#### Scenario 12

*   **Initial State**:
    *   **Role**: Any
    *   **Org Status**: active
    *   **User Status**: active
*   **Transition Axis**: User
*   **Change**: user status: active → inactive
*   **Expected Results**:
    *   No change to 'Organization disabled' sidebar notification.
    *   Display 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard/account-disabled`.
    *   Comment: Superadmin only retains access to disabled organization if user account is active
*   **Current Behavior Analysis**:
    1.  **Event Trigger**: `member:status:changed` event (`{ userId, orgId, isActive: false }`) for active org.
    2.  **`DashboardEventManager` (`DEM`)**:
        *   `member:status:changed` handler runs.
        *   Condition `event.userId === userId && event.orgId === activeOrganization?.id && event.isActive === false` is met.
        *   Calls `refreshServerContextAndSoftRefresh().then(() => router.push('/dashboard/account-disabled'))`.
    3.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isUserActive: false`. `isOrgActive: true`.
        *   Headers `x-auth-is-user-active: false` updated. `router.refresh()`.
    4.  **`middleware.ts` (during `router.push` to `/dashboard/account-disabled` or `router.refresh`)**:
        *   New context (user INACTIVE).
        *   DEM pushes to `/dashboard/account-disabled`. Middleware on this bypass path won't redirect further. If DEM hadn't pushed, middleware would enforce this redirect from non-bypass pages.
    5.  **`DashboardLayout` (rendering `/dashboard/account-disabled`)**:
        *   `DashboardProvider`: `isUserInactive` true. `isActiveOrgDisabled` false.
    6.  **UI Updates**:
        *   **Sidebar.tsx**: `isUserInactive` true.
            *   'Account disabled' notification displayed.
            *   `shouldShowNavigation` becomes `false`. Navigation hidden.
        *   **`OrganizationSwitcher.tsx`**: Updates to show "Inactive Account".
*   **Pass/Fail**: Pass.
*   **Actionable Tasks**: None.

---

#### Scenario 13

*   **Initial State**:
    *   **Role**: Any
    *   **Org Status**: active
    *   **User Status**: inactive
*   **Transition Axis**: User
*   **Change**: user status: inactive → active
*   **Expected Results**:
    *   No change to 'Organization disabled' sidebar notification.
    *   Remove 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard`.
    *   Comment: (empty)
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User is on `/dashboard/account-disabled`.
    2.  **Event Trigger**: `member:status:changed` (`{ userId, orgId, isActive: true }`) for active org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   `member:status:changed` handler. `event.isActive === true`.
        *   Calls `refreshServerContextAndSoftRefresh()`.
        *   Old DEM logic from `businesslogic.md` (Scenario 5/17) indicated a missing redirect from DEM here if on `account-disabled` page.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isUserActive: true`. `isOrgActive: true`.
        *   Headers `x-auth-is-user-active: true` updated. `router.refresh()`.
    5.  **`middleware.ts` (refresh on `/dashboard/account-disabled`)**:
        *   New context (user ACTIVE, org ACTIVE).
        *   Path `/dashboard/account-disabled` is bypass. No redirect by middleware from here.
    6.  **`DashboardLayout` (re-rendering `/dashboard/account-disabled`)**:
        *   `DashboardProvider`: `isUserInactive` false. `isActiveOrgDisabled` false.
    7.  **UI Updates (on `/dashboard/account-disabled`)**:
        *   **Sidebar.tsx**: `isUserInactive` false.
            *   'Account disabled' notification removed.
            *   `shouldShowNavigation` becomes `true`. Navigation appears. `rbacEvaluate` filters items.
        *   **`OrganizationSwitcher.tsx`**: Updates to show role.
    8.  **Redirect**: User stays on `/dashboard/account-disabled`. Expected is `/dashboard`.
*   **Pass/Fail**: Fail (on redirect).
*   **Actionable Tasks**:
    *   [x] Modify `DashboardEventManager` for `member:status:changed` when `event.isActive: true`:
        *   [x] After `refreshServerContextAndSoftRefresh()`, if `window.location.pathname.includes('/dashboard/account-disabled')` (and org is active), explicitly call `router.push('/dashboard')`.

---

#### Scenario 14

*   **Initial State**:
    *   **Role**: Any
    *   **Org Status**: inactive
    *   **User Status**: active
*   **Transition Axis**: User
*   **Change**: user status: active → inactive
*   **Expected Results**:
    *   The 'Organization disabled' sidebar notification is expected.
    *   Display 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard/account-disabled`.
    *   Comment: Superadmin only retains access to disabled organization if user account is active
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User on `/dashboard/organization-disabled` (if superadmin with nav, or just the page if non-SA and nav hidden).
    2.  **Event Trigger**: `member:status:changed` (`{ isActive: false }`) in current *inactive* org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   `member:status:changed`, `isActive: false` handler.
        *   Calls `refreshServerContextAndSoftRefresh().then(() => router.push('/dashboard/account-disabled'))`.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isUserActive: false`, `isOrgActive: false`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (push to `/dashboard/account-disabled`)**:
        *   Redirect occurs. Target `/dashboard/account-disabled` is bypass.
    6.  **`DashboardLayout` (rendering `/dashboard/account-disabled`)**:
        *   `DashboardProvider`: `isUserInactive` true. `isActiveOrgDisabled` true.
    7.  **UI Updates**:
        *   **Sidebar.tsx**: `isUserInactive` true, `isActiveOrgDisabled` true.
            *   'Account disabled' notification displayed.
            *   'Organization disabled' notification also displayed.
            *   `shouldShowNavigation` becomes `false`. Navigation hidden.
        *   **`OrganizationSwitcher.tsx`**: Updates to show "Inactive Account".
*   **Pass/Fail**: Pass.
*   **Actionable Tasks**: None. (Verify sidebar notification combination clarity as per old Scenario 8/20).

---

#### Scenario 15

*   **Initial State**:
    *   **Role**: superadmin
    *   **Org Status**: inactive
    *   **User Status**: inactive
*   **Transition Axis**: User
*   **Change**: user status: inactive → active
*   **Expected Results**:
    *   The 'Organization disabled' sidebar notification is expected.
    *   Remove 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard`.
    *   Comment: Superadmin account changed to active, so gains access to disabled org
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User (SA) on `/dashboard/account-disabled`.
    2.  **Event Trigger**: `member:status:changed` (`{ isActive: true }`) in current *inactive* org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   `member:status:changed`, `isActive: true` handler.
        *   Calls `refreshServerContextAndSoftRefresh()`.
        *   Old DEM logic (Scenario 11) for this case (user becomes active in inactive org) did not have specific redirect logic, meaning user stays on current page.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isUserActive: true`, `isOrgActive: false`, `isSuperAdmin: true`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (refresh on `/dashboard/account-disabled`)**:
        *   Path is bypass. No redirect by middleware.
    6.  **`DashboardLayout` (re-rendering `/dashboard/account-disabled`)**:
        *   `DashboardProvider`: `isUserInactive` false. `isActiveOrgDisabled` true. `isSuperAdmin` true.
    7.  **UI Updates (on `/dashboard/account-disabled`)**:
        *   **Sidebar.tsx**: `isUserInactive` false, `isActiveOrgDisabled` true, `isSuperAdmin` true.
            *   'Account disabled' notification removed.
            *   'Organization disabled' (superadmin version) notification remains/displayed.
            *   `shouldShowNavigation` becomes `true` (SA can see nav in inactive org if their account is active). Navigation appears.
        *   **`OrganizationSwitcher.tsx`**: Updates to show SA role (no longer "Inactive Account").
    8.  **Redirect**: User stays on `/dashboard/account-disabled`. Expected is `/dashboard`.
*   **Pass/Fail**: Fail (on redirect). User becomes operational as SA in an inactive org (nav appears) but isn't moved from account-disabled page.
*   **Actionable Tasks**:
    *   [x] Modify `DashboardEventManager` for `member:status:changed` when `event.isActive: true`:
        *   [x] After `refreshServerContextAndSoftRefresh()`, if user is `superAdmin`, org is `inactive`, user becomes `active`, and current path is `/dashboard/account-disabled`: then `router.push('/dashboard')`. (The comment "gains access to disabled org" implies they should be able to use the app, typically starting from `/dashboard` or `/dashboard/organization-disabled`. `/dashboard` is specified).
        *   [x] Alternatively, if a superadmin becoming active in an inactive org should land on `/dashboard/organization-disabled` (to manage it), then the redirect target should be that. The current expectation is `/dashboard`.

---

#### Scenario 16

*   **Initial State**:
    *   **Role**: non-superadmin
    *   **Org Status**: inactive
    *   **User Status**: inactive
*   **Transition Axis**: User
*   **Change**: user status: inactive → active
*   **Expected Results**:
    *   The 'Organization disabled' sidebar notification is expected.
    *   Remove 'Account disabled' sidebar notification.
    *   Update organization switcher (Yes - soft-refresh).
    *   Update navigation (Yes - soft-refresh rbacEvaluate).
    *   Redirect to `/dashboard/organization-disabled`.
    *   Comment: (empty)
*   **Current Behavior Analysis**:
    1.  **Initial Page**: User (non-SA) on `/dashboard/account-disabled`.
    2.  **Event Trigger**: `member:status:changed` (`{ isActive: true }`) in current *inactive* org.
    3.  **`DashboardEventManager` (`DEM`)**:
        *   `member:status:changed`, `isActive: true` handler.
        *   Calls `refreshServerContextAndSoftRefresh()`.
        *   Old DEM logic (Scenario 23) for user (non-SA) becoming active in inactive org while on `account-disabled` page was missing specific redirect to `organization-disabled`.
    4.  **`refreshServerContextAndSoftRefresh()`**:
        *   `resolveUserAuthContext` gets `isUserActive: true`, `isOrgActive: false`, `isSuperAdmin: false`.
        *   Headers updated. `router.refresh()`.
    5.  **`middleware.ts` (refresh on `/dashboard/account-disabled`)**:
        *   Path is bypass. No redirect by middleware.
    6.  **`DashboardLayout` (re-rendering `/dashboard/account-disabled`)**:
        *   `DashboardProvider`: `isUserInactive` false. `isActiveOrgDisabled` true. `isSuperAdmin` false.
    7.  **UI Updates (on `/dashboard/account-disabled`)**:
        *   **Sidebar.tsx**: `isUserInactive` false, `isActiveOrgDisabled` true, `isSuperAdmin` false.
            *   'Account disabled' notification removed.
            *   'Organization disabled' (non-SA version) notification remains/displayed.
            *   `shouldShowNavigation` remains `false` (non-SA in inactive org).
        *   **`OrganizationSwitcher.tsx`**: Updates to show role (no longer "Inactive Account").
    8.  **Redirect**: User stays on `/dashboard/account-disabled`. Expected is `/dashboard/organization-disabled`.
*   **Pass/Fail**: Fail (on redirect).
*   **Actionable Tasks**:
    *   [x] Modify `DashboardEventManager` for `member:status:changed` when `event.isActive: true`:
        *   [x] After `refreshServerContextAndSoftRefresh()`, if user is non-superadmin, org is inactive, user becomes active, and current path is `/dashboard/account-disabled`: then `router.push('/dashboard/organization-disabled')`.

---

## Scratchpad: Implementation Tasks

This scratchpad outlines the tasks needed to align the application behavior with the expected results defined in the scenarios. Tasks should be executed in the specified order where dependencies exist.

**Phase 1: Realtime Event Handling & Core Context Refresh**

1.  [x] **Task 1: Implement Realtime Active Organization Activation Detection**
    *   [x] **Goal**: Ensure the application immediately detects when the *currently active* organization transitions from `inactive` to `active`.
    *   [x] **File(s)**: Likely `DashboardEventManager.ts` (or a new dedicated service, or `OrganizationSwitcher.tsx` if logic is co-located).
    *   [x] **Details**:
        *   [x] Investigate using a Supabase realtime subscription for the `organizations` table, specifically for the `id` of the `activeOrganization` context, listening for `UPDATE` events where `is_active` becomes `true`.
        *   [x] Alternatively, if org activation is always triggered from within the app, ensure a custom event (using `organization:statusChanged`, payload: `{ orgId, isActive: true }`) is emitted on the event bus.
        *   [x] The listener for this event/subscription should be in `DashboardEventManager.ts` (preferred for centralization of such cross-cutting concerns).
    *   [x] **Affected Scenarios**: 3, 4, 6, 15 (indirectly via context for redirect), 16 (indirectly).

2.  [x] **Task 2: Enhance `DashboardEventManager.ts` (DEM) Event Handlers**
    *   [x] **Goal**: Refine DEM to correctly trigger context refreshes and subsequent conditional client-side navigations based on the new state and current page.
    *   [x] **File(s)**: `DashboardEventManager.ts`.
    *   [x] **Sub-Task 2.1: Org Activation Reaction**
        *   [x] When active org activation is detected (from Task 1), DEM must call `refreshServerContextAndSoftRefresh()`.
    *   [x] **Sub-Task 2.2: Conditional Redirects after `member:status:changed` (`isActive: true`)**
        *   [x] **Scenario 13 (User Inactive → Active in Active Org)**: After refresh, if `currentPath === '/dashboard/account-disabled'` and org is active, `router.push('/dashboard')`.
        *   [x] **Scenario 15 (SA User Inactive → Active in Inactive Org)**: After refresh, if user is `superAdmin`, org is `inactive`, user becomes `active`, and `currentPath === '/dashboard/account-disabled'`, then `router.push('/dashboard')`. (Confirm if `/dashboard/organization-disabled` might be preferred for SA to manage the inactive org immediately. Current spec is `/dashboard`).
        *   [x] **Scenario 16 (Non-SA User Inactive → Active in Inactive Org)**: After refresh, if user is non-superadmin, org is `inactive`, user becomes `active`, and `currentPath === '/dashboard/account-disabled'`, then `router.push('/dashboard/organization-disabled')`.
    *   [x] **Sub-Task 2.3: Conditional Redirects after `user:role:changed`**
        *   [x] **Scenario 11 (Non-SA → SA in Inactive Org)**: After refresh, if `newRoleId === RoleId.SUPERADMIN`, org is `inactive`, user is active, and `currentPath === '/dashboard/organization-disabled'`, then `router.push('/dashboard')`.
    *   [x] **Sub-Task 2.4: Conditional Redirects after Org Becomes Active (triggered by Task 2.1's refresh)**
        *   [x] **Scenario 4 (SA, Inactive Org → Active Org)**: No redirect from DEM needed. Superadmin stays on current page.
        *   [x] **Scenario 6 (Non-SA, Inactive Org → Active Org)**: After refresh (triggered by org activation), if user is non-superadmin, org just became active, user is active, and `currentPath === '/dashboard/organization-disabled'`, then `router.push('/dashboard')`.
    *   [x] **Details for DEM**: Access `window.location.pathname` for `currentPath`. Use the refreshed auth context (returned by `refreshServerContextAndSoftRefresh`) for immediate decisions within DEM logic.

**Phase 2: Component-Specific Logic Adjustments**

3.  [x] **Task 3: Modify `OrganizationSwitcher.tsx` Logic**
    *   [x] **Goal**: Prevent unnecessary redirects for superadmins or already inactive users.
    *   [x] **File(s)**: `OrganizationSwitcher.tsx`.
    *   [x] **Sub-Task 3.1: Superadmin Active Org → Inactive**
        *   [x] **Scenario 1**: When the active org becomes inactive, if the current user is a superadmin (check via `useDashboard()` context), do *not* call `router.push("/dashboard/organization-disabled")`. Only call `router.refresh()` (or trigger `DEM.refreshServerContextAndSoftRefresh()`).
    *   [x] **Sub-Task 3.2: Already Inactive User, Active Org → Inactive**
        *   [x] **Scenario 2**: When the active org becomes inactive, if the current user is *already* inactive (`isUserInactive` from `useDashboard()` is true), do *not* call `router.push("/dashboard/organization-disabled")`. Only call `router.refresh()` (or trigger `DEM.refreshServerContextAndSoftRefresh()`). User should remain on `/dashboard/account-disabled`.

4.  [x] **Task 4: Refine `Sidebar.tsx` Notification Logic**
    *   [x] **Goal**: Ensure sidebar notifications are accurate and not misleading, especially for superadmins, and correctly reflect the removal of the special SA org disabled card.
    *   [x] **File(s)**: `Sidebar.tsx`.
    *   [x] **Sub-Task 4.1: Superadmin in Inactive Org Notification**
        *   [x] **Scenario 11 (and impacts how SA views inactive orgs generally)**: If user is `superAdmin` and `isActiveOrgDisabled` is `true` (from `useDashboard()` context), the 'Organization disabled' notification card should *not* be displayed. Superadmins operate normally in inactive orgs (navigation is shown).
    *   [x] **Sub-Task 4.2: Combined Notification Clarity**
        *   [x] **Scenario 14 (and others where both user & org might be inactive)**: Review the display conditions for 'Account disabled' and 'Organization disabled' notifications to ensure they are clear and not visually redundant if both states apply simultaneously. The current expectation implies both can show, which is acceptable if distinct. (Decision: Current logic to show both distinct messages is acceptable; no code change needed for this specific point beyond 4.1).

**Phase 3: Verification & Testing**

5.  [ ] **Task 5: Comprehensive Scenario Testing**
    *   [ ] **Goal**: Verify all 16 scenarios behave as expected after the changes.
    *   [ ] **Details**: Manually trigger each event and observe UI changes, redirects, console logs, and context updates.

This structured task list should guide the implementation process effectively.

---
