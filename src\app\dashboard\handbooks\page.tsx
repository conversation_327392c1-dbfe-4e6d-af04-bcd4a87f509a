import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Clock,
  MapPin,
  Phone,
  Mail,
  Globe,
  FileText,
  AlertCircle,
  DollarSign,
  MessageSquare,
  Mailbox,
} from "lucide-react";
import type {
  HandbookProps,
  Service,
  Criteria,
  ServiceChannels,
  ServicePlace,
  Procedures,
  Procedure,
  ProcedureStep,
  RequiredDocuments,
  RequiredDocument,
  Fee,
  FeeItem,
  ComplaintChannels,
  ComplaintChannel,
  ApplicationForms,
  ApplicationForm,
  BottomRemarks,
  SubRemark,
  Source,
  CriteriaItem,
  SubItem,
} from "@/types/handbook";
import ClientHandbookSelector from "@/components/ClientHandbookSelector";
import { getHandbook } from "@/app/actions/handbook";
import { PageProps } from "@/types/app/PageProps";

export default async function HandbookPage({ searchParams }: PageProps) {
  // Properly await searchParams before accessing its properties
  const params = await searchParams;
  const handbook = await getHandbook(params?.id);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Immigration Service Handbooks
          </h2>
        </div>
      </div>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <ClientHandbookSelector {...(params?.id && { currentId: params.id })} />
        <Handbook handbook={handbook} />
      </div>
    </div>
  );
}

function Handbook({ handbook }: HandbookProps) {
  if (!handbook) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <p className="text-muted-foreground">No handbook data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ServiceHeader service={handbook.service} />

      <div className="grid grid-cols-1 gap-6">
        <div className="space-y-6">
          <CriteriaSection criteria={handbook.criteria} />
          <ApplicationFormsSection
            applicationForms={handbook.application_forms}
          />
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <ProceduresSection procedures={handbook.procedures} />
          <RequiredDocumentsSection
            requiredDocuments={handbook.required_documents}
          />
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <FeeSection fee={handbook.fee} />
            <ServiceChannelsSection
              serviceChannels={handbook.service_channels}
            />
          </div>
          <div className="space-y-6">
            <ComplaintChannelsSection
              complaintChannels={handbook.complaint_channels}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          <BottomRemarksSection bottomRemarks={handbook.bottom_remarks} />
        </div>
      </div>
      <SourceSection source={handbook.source} />
    </div>
  );
}

function ServiceHeader({ service }: { service: Service }) {
  return (
    <Card className="border-primary/20">
      <CardHeader className="bg-primary/5">
        <CardTitle className="text-2xl text-primary">
          {service.service_name}
        </CardTitle>
        {service.service_case_id && (
          <Badge variant="outline" className="w-fit">
            Service ID: {service.service_case_id}
          </Badge>
        )}
      </CardHeader>
    </Card>
  );
}

function CriteriaSection({ criteria }: { criteria: Criteria }) {
  if (!criteria.criteria_items.sub_criteria.length) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Eligibility Criteria
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {criteria.criteria_notes && (
          <p className="text-sm text-muted-foreground">
            {criteria.criteria_notes}
          </p>
        )}

        {criteria.criteria_items.header && (
          <h3 className="font-semibold">{criteria.criteria_items.header}</h3>
        )}

        <ul className="space-y-2 list-disc pl-5">
          {criteria.criteria_items.sub_criteria.map((item: CriteriaItem) => (
            <li key={item.number} className="text-sm">
              {item.text}
              {item.sub_items && item.sub_items.length > 0 && (
                <ul className="space-y-1 list-disc pl-5 mt-1">
                  {item.sub_items.map((subItem: SubItem, index: number) => (
                    <li key={index} className="text-sm">
                      {subItem.text}
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>

        {criteria.criteria_remark && (
          <div className="bg-amber-50 p-3 rounded-md border border-amber-200 text-sm">
            <p className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500 shrink-0 mt-0.5" />
              <span>{criteria.criteria_remark}</span>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ServiceChannelsSection({
  serviceChannels,
}: {
  serviceChannels: ServiceChannels;
}) {
  if (!serviceChannels.places_of_service.length) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Service Locations
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion
          type="multiple"
          defaultValue={serviceChannels.places_of_service.map(
            (_: ServicePlace, index: number) => `place-${index}`
          )}
          className="w-full"
        >
          {serviceChannels.places_of_service.map(
            (place: ServicePlace, index: number) => (
              <AccordionItem key={index} value={`place-${index}`}>
                <AccordionTrigger className="text-base font-medium text-left">
                  {place.place_of_service}
                </AccordionTrigger>
                <AccordionContent className="space-y-2 text-sm">
                  {place.place_of_service_text && (
                    <p>{place.place_of_service_text}</p>
                  )}

                  {(place.service_time_days || place.service_time_times) && (
                    <div className="flex items-start gap-2 mt-2">
                      <Clock className="h-4 w-4 text-muted-foreground shrink-0 mt-0.5" />
                      <div>
                        {place.service_time_days && (
                          <p>{place.service_time_days}</p>
                        )}
                        {place.service_time_times && (
                          <p>{place.service_time_times}</p>
                        )}
                      </div>
                    </div>
                  )}

                  {place.place_of_service_remark && (
                    <p className="text-muted-foreground italic mt-2">
                      {place.place_of_service_remark}
                    </p>
                  )}
                </AccordionContent>
              </AccordionItem>
            )
          )}
        </Accordion>
      </CardContent>
    </Card>
  );
}

function ProceduresSection({ procedures }: { procedures: Procedures }) {
  if (!procedures.rows.length) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Procedures
          {procedures.total_time && (
            <Badge variant="outline" className="ml-auto">
              Total time: {procedures.total_time}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion
          type="multiple"
          defaultValue={procedures.rows.map(
            (procedure: Procedure) => `step-${procedure.step}`
          )}
          className="w-full"
        >
          {procedures.rows.map((procedure: Procedure) => (
            <AccordionItem
              key={procedure.step}
              value={`step-${procedure.step}`}
            >
              <AccordionTrigger className="text-base font-medium">
                Step {procedure.step}: {procedure.title}
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <ol className="list-decimal pl-5 space-y-1 text-sm">
                  {procedure.procedure_steps.map((step: ProcedureStep) => (
                    <li key={step.number}>{step.text}</li>
                  ))}
                </ol>

                <div className="text-sm text-muted-foreground">
                  {procedure.time && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{procedure.time}</span>
                    </div>
                  )}
                  {procedure.responsible_section && (
                    <div className="mt-1">
                      Responsible: {procedure.responsible_section}
                    </div>
                  )}
                </div>

                {procedure.procedure_remarks && (
                  <p className="text-muted-foreground italic">
                    {procedure.procedure_remarks}
                  </p>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}

function RequiredDocumentsSection({
  requiredDocuments,
}: {
  requiredDocuments: RequiredDocuments;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Required Documents
        </CardTitle>
      </CardHeader>
      <CardContent>
        {requiredDocuments.rows.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">#</TableHead>
                <TableHead>Document</TableHead>
                <TableHead className="text-center">Original</TableHead>
                <TableHead className="text-center">Copies</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {requiredDocuments.rows.map((doc: RequiredDocument) => (
                <TableRow key={doc.number}>
                  <TableCell>{doc.number}</TableCell>
                  <TableCell>
                    <div>
                      <p>{doc.document_name}</p>
                      {doc.remarks && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {doc.remarks}
                        </p>
                      )}
                      {doc.authority && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Issued by: {doc.authority}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    {doc.original_documents}
                  </TableCell>
                  <TableCell className="text-center">{doc.copies}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <p className="text-muted-foreground">No documents required</p>
        )}
      </CardContent>
    </Card>
  );
}

function FeeSection({ fee }: { fee: Fee }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Fees
        </CardTitle>
      </CardHeader>
      <CardContent>
        {fee.rows.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">#</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Amount (THB)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fee.rows.map((feeItem: FeeItem) => (
                <TableRow key={feeItem.number}>
                  <TableCell>{feeItem.number}</TableCell>
                  <TableCell>{feeItem.fee_details}</TableCell>
                  <TableCell className="text-right font-medium">
                    {Number(feeItem.fee_amount).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <p className="text-muted-foreground">No fees required</p>
        )}
      </CardContent>
    </Card>
  );
}

function ComplaintChannelsSection({
  complaintChannels,
}: {
  complaintChannels: ComplaintChannels;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Complaint Channels
        </CardTitle>
      </CardHeader>
      <CardContent>
        {complaintChannels.rows.length > 0 ? (
          <Accordion
            type="multiple"
            defaultValue={complaintChannels.rows.map(
              (channel: ComplaintChannel) => `channel-${channel.number}`
            )}
            className="w-full"
          >
            {complaintChannels.rows.map((channel: ComplaintChannel) => (
              <AccordionItem
                key={channel.number}
                value={`channel-${channel.number}`}
              >
                <AccordionTrigger className="text-base font-medium text-left">
                  <div className="text-left w-full">{channel.channel_name}</div>
                </AccordionTrigger>
                <AccordionContent className="space-y-2 text-sm">
                  {channel.address && (
                    <p className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground shrink-0 mt-0.5" />
                      <span>{channel.address}</span>
                    </p>
                  )}

                  {channel.hotline && (
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{channel.hotline}</span>
                    </p>
                  )}

                  {channel.email && (
                    <p className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{channel.email}</span>
                    </p>
                  )}

                  {channel.website && (
                    <p className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={channel.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {channel.website}
                      </a>
                    </p>
                  )}

                  {channel.pobox && (
                    <p className="flex items-center gap-2">
                      <Mailbox className="h-4 w-4 text-muted-foreground" />
                      <span>{channel.pobox}</span>
                    </p>
                  )}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <p className="text-muted-foreground">
            No complaint channels available
          </p>
        )}
      </CardContent>
    </Card>
  );
}

function ApplicationFormsSection({
  applicationForms,
}: {
  applicationForms: ApplicationForms;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Application Forms
        </CardTitle>
      </CardHeader>
      <CardContent>
        {applicationForms.rows.length > 0 &&
        applicationForms.rows[0].form_name !== "N/A" ? (
          <ul className="space-y-2">
            {applicationForms.rows.map((form: ApplicationForm) => (
              <li
                key={`${form.number}-${form.form_name}`}
                className="flex items-start gap-2 text-sm"
              >
                <span className="font-medium">
                  {form.number ? `${form.number}.` : ""}
                </span>
                <span>{form.form_name}</span>
                {form.remarks && (
                  <span className="text-muted-foreground ml-1">
                    ({form.remarks})
                  </span>
                )}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-muted-foreground">No application forms required</p>
        )}
      </CardContent>
    </Card>
  );
}

function BottomRemarksSection({
  bottomRemarks,
}: {
  bottomRemarks: BottomRemarks;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">
          {bottomRemarks.header || "Additional Information"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {bottomRemarks.remarks && (
          <p className="mb-4 text-sm">{bottomRemarks.remarks}</p>
        )}

        {bottomRemarks.sub_remarks.length > 0 && (
          <ul className="space-y-2 list-disc pl-5">
            {bottomRemarks.sub_remarks.map((remark: SubRemark) => (
              <li key={`${remark.text.substring(0, 20)}`} className="text-sm">
                {remark.text}
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}

function SourceSection({ source }: { source: Source }) {
  if (!source || (!source["source-url"] && !source["source-publication-date"]))
    return null;

  return (
    <div className="text-xs text-muted-foreground text-right mt-4">
      Source: {source["source-url"]}
      {source["source-publication-date"] && (
        <span> | Published: {source["source-publication-date"]}</span>
      )}
    </div>
  );
}
