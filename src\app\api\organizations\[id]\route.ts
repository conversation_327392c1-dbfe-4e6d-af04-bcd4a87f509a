import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'

export async function DELETE(
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Check permission using centralized RBAC system
    const hasAccess = await checkRbacPermission(
      { rdRoles: ["superAdmin"], resourceOrgId: params.id },
      { silentFail: true }
    )

    if (!hasAccess) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    // Check if organization is active
    const { data: org, error: orgError } = await supabase
      .from('organizations')
      .select('is_active')
      .eq('id', params.id)
      .single()

    if (orgError || !org) {
      return new NextResponse('Organization not found', { status: 404 })
    }

    if (org.is_active) {
      return new NextResponse(
        'Cannot delete active organization. Deactivate it first.',
        { status: 400 }
      )
    }

    // Delete organization
    const { error: deleteError } = await supabase
      .from('organizations')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting organization:', deleteError)
      return new NextResponse('Failed to delete organization', { status: 500 })
    }

    // Proactively invalidate the server-side auth context cache for the organization.
    // invalidateServerAuthContextCache(undefined, params.id) // REMOVED

    return NextResponse.json({ message: 'Organization deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/organizations/[id]:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}