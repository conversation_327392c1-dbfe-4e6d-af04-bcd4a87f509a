import * as React from "react";
import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast";

// Included from src/hooks/use-toast.ts as it's a dependency
type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
};

// Extracted from src/hooks/use-toast.ts
export interface State {
  toasts: ToasterToast[];
}