Number,Initial Role,Initial Current Org Status,Initial Current User Status,Transition Axis,Change,Sidebar notification organization disabled,Sidebar notification account disabled,Update organization-switcher,Update navigation,Redirect?,Comments
1,superadmin,active,active,Organization,org status: active → inactive,Add,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,<PERSON><PERSON><PERSON> retains access to disabled organization if user account is active
2,Any,active,inactive,Organization,org status: active → inactive,Add,Yes,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,User account already inactive and user already on account disabled page
3,Any,inactive,inactive,Organization,org status: inactive → active,Remove,Yes,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,User account already inactive and user already on account disabled page
4,superadmin,inactive,active,Organization,org status: inactive → active,Remove,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,
5,non-superadmin,active,active,Organization,org status: active → inactive,Add,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Organization-disabled,
6,non-superadmin,inactive,active,Organization,org status: inactive → active,Remove,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Dashboard,
7,Any,active,active,Role,role: superadmin → non-superadmin,No,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,
8,Any,active,inactive,Role,role: superadmin → non-superadmin,No,Yes,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,User account already inactive and user already on account disabled page
9,superadmin,inactive,active,Role,role: superadmin → non-superadmin,Add,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Organization-disabled,Only superadmin has access to inactive organizations
10,superadmin,inactive,inactive,Role,role: Any → Any,Yes,Yes,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,No,User account already inactive and user already on account disabled page
11,non-superadmin,inactive,active,Role,role: non-superadmin → superadmin,Remove,No,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Dashboard,"Promoted to superadmin, so gets access to disabled organization"
12,Any,active,active,User,user status: active → inactive,No,Add,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Account-disabled,Superadmin only retains access to disabled organization if user account is active
13,Any,active,inactive,User,user status: inactive → active,No,Remove,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Dashboard,
14,Any,inactive,active,User,user status: active → inactive,Yes,Add,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Account-disabled,Superadmin only retains access to disabled organization if user account is active
15,superadmin,inactive,inactive,User,user status: inactive → active,Yes,Remove,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Dashboard,"Superadmin account changed to active, so gains access to disabled org"
16,non-superadmin,inactive,inactive,User,user status: inactive → active,Yes,Remove,Yes - soft-refresh,Yes - soft-refresh rbacEvaluate,Organization-disabled,