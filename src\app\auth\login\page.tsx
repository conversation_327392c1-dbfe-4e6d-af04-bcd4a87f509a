"use client";

import * as React from "react";
import { useSearchParams } from "next/navigation";
import { UserAuthForm } from "@/components/auth/user-auth-form";
import { AuthContainer } from "@/components/auth/auth-container";

export default function LoginPage() {
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/dashboard";
  const urlError = searchParams.get("error");

  // Track email sent state to conditionally show welcome message
  const [emailSent, setEmailSent] = React.useState<boolean>(false);

  // Also check for hash parameters (client-side only)
  const [hashError, setHashError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash;
      if (hash) {
        const hashParams = new URLSearchParams(hash.substring(1));
        const hashErrorParam = hashParams.get('error_description') || hashParams.get('error');
        setHashError(hashErrorParam);
      }
    }
  }, []);

  const finalError = urlError || hashError;

  return (
    <AuthContainer>
      <div className="w-full max-w-[500px] mx-auto">
        <div className="bg-white rounded-lg shadow-[0_2px_10px] shadow-black/5">
          <div className="px-10 py-10">
            {!emailSent && (
              <div className="text-center mb-6">
                <h1 className="text-2xl font-medium">Welcome back</h1>
                <p className="text-sm text-gray-600 mt-2">
                  Choose your preferred way to sign in
                </p>
              </div>
            )}
            <UserAuthForm
              redirectTo={redirectTo}
              urlError={finalError}
              onEmailSentChange={setEmailSent}
            />
          </div>
        </div>
      </div>
    </AuthContainer>
  );
}
