"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { NoAccessProps } from "@/types/components/NoAccessProps";

export function NoAccess({
  message = "You don't have permission to access this page.",
}: NoAccessProps) {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
          Access Denied
        </h1>
        <div className="text-gray-600 dark:text-gray-300">{message}</div>
        <Button
          onClick={() => router.back()}
          variant="outline"
          className="mt-4"
        >
          Go Back
        </Button>
      </div>
    </div>
  );
}
