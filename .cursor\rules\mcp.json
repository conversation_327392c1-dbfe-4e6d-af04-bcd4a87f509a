{"mcpServers": {"supabase agent forms": {"command": "node", "args": ["C:/Users/<USER>/AppData/Roaming/nvm/v20.17.0/node_modules/@modelcontextprotocol/server-postgres/dist/index.js", "postgresql://postgres.jdhltybpmwuijrxqhzjj:<EMAIL>:5432/postgres"], "enabled": false}, "browser-tools": {"command": "node", "args": ["C:/Users/<USER>/AppData/Roaming/nvm/v20.17.0/node_modules/@modelcontextprotocol/server-postgres/dist/index.js"], "enabled": false}}}