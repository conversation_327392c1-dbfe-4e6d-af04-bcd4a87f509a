import { User } from "@supabase/supabase-js"
import { Organization } from "@/types/organization"

export interface DashboardContextType {
  user: User | null
  userRole: number | null
  isSuperAdmin: boolean
  avatarUrl: string | null
  isSidebarCollapsed: boolean
  isMobileSidebarOpen: boolean
  organizations: Organization[]
  activeOrganization: Organization | null
  isActiveOrgDisabled: boolean
  isUserInactive: boolean
  setIsSidebarCollapsed: (value: boolean) => void
  setIsMobileSidebarOpen: (value: boolean) => void
  toggleSidebar: () => void
} 