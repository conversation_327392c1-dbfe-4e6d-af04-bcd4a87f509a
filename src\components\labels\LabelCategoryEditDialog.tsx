import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { LabelCategory } from "@/types/app/dashboard/admin/labels/LabelCategory";
import { LabelCategoryEditDialogProps } from "@/types/components/labels/LabelCategoryEditDialogProps";

export function LabelCategoryEditDialog({
  labelCategory,
  isOpen,
  onClose,
  onSave,
}: LabelCategoryEditDialogProps) {
  const [formData, setFormData] = useState<Partial<LabelCategory>>({
    name: "",
  });

  useEffect(() => {
    if (labelCategory) {
      setFormData(labelCategory);
    } else {
      setFormData({ name: "" });
    }
  }, [labelCategory]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {labelCategory ? "Edit Category" : "Add Category"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              required
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
