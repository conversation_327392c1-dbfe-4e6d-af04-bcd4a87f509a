import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON>rumb<PERSON>tem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { CreateOrganizationForm } from "./create-organization-form"; // This path might need adjustment if CreateOrganizationForm is not in the same new directory. Assuming it is for now or is a globally accessible component.
import { ChevronRight } from "lucide-react";
import { withRbacPermission } from "@/lib/rbac/permissions-server";

export default async function CreateOrganizationPage() {
  return withRbacPermission(
    { rRoles: ["superAdmin", "supportAdmin"] },
    { redirectTo: "/dashboard" }
  )(async () => {
    return (
      <div className="p-6 pt-0">
        <div className="mb-6 space-y-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                {/* Assuming /dashboard/developer is the main page for the developer section */}
                <BreadcrumbLink href="/dashboard/developer">Developer</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                <BreadcrumbPage>Create Organization</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <h2 className="text-2xl font-bold text-[#194852]">
            Create Organization
          </h2>
        </div>

        {/* Assuming CreateOrganizationForm is either in this new directory or correctly resolved. 
            If it was in 'src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx', 
            it also needs to be moved or its import path updated. 
            For this step, I'm assuming it will be moved alongside or is a general component. */}
        <CreateOrganizationForm />
      </div>
    );
  });
} 