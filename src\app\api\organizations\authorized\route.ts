import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { OrganizationMemberWithOrg } from '@/types/organization/OrganizationMemberWithOrg'
import { evaluateRbac } from '@/lib/rbac/rbac-utils'
import { Organization } from '@/types/organization'

// Define paginated response type
export interface PaginatedOrganizationsResponse {
  organizations: Organization[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(7)
  const startTime = Date.now()
  const referrer = request.headers.get('referer') || 'unknown'

  console.log(`[${requestId}] Authorized organizations request started:`, {
    referrer,
    timestamp: new Date().toISOString()
  })

  // Check if pagination is requested
  const searchParams = request.nextUrl.searchParams;
  const isPaginated = searchParams.has('page') || searchParams.has('pageSize');
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.log(`[${requestId}] Unauthorized request:`, {
        error: userError?.message,
        duration: Date.now() - startTime
      })
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First, get all the user's roles in all organizations
    const { data: userRoles, error: userRolesError } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role')
      .eq('user_id', user.id)
      .eq('org_member_is_active', true)

    if (userRolesError) {
      console.error(`[${requestId}] Error fetching user roles:`, {
        error: userRolesError,
        userId: user.id,
        duration: Date.now() - startTime
      })
      return NextResponse.json({ error: 'Failed to verify access' }, { status: 500 })
    }

    // Create a map of organization IDs to user roles
    const orgRolesMap = new Map(userRoles?.map(role => [role.org_id, role.org_member_role]) || [])

    // Get organizations where the user is a superadmin
    const superadminOrgIds = Array.from(orgRolesMap.entries())
      .filter(([, roleId]) => evaluateRbac(roleId, { rRoles: ["superAdmin"] }))
      .map(([orgId]) => orgId)

    const isSuperadminAnywhere = superadminOrgIds.length > 0

    console.log(`[${requestId}] User role analysis:`, {
      userId: user.id,
      isSuperadminAnywhere,
      superadminOrgCount: superadminOrgIds.length,
      superadminOrgIds
    })

    // First, count total records for pagination
    let countQuery = supabase
      .from('organization_members')
      .select('org_id', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .or('org_member_is_active.eq.true,org_member_is_active.eq.false') // Get both active and inactive memberships
      .in('org_member_role', [1, 2, 3, 4, 5, 6]) // Include all possible roles

    // For non-superadmins, only count active organizations
    if (!isSuperadminAnywhere) {
      countQuery = countQuery.eq('organizations.is_active', true)
    }

    // Get organizations where user has one of the authorized roles
    let query = supabase
      .from('organization_members')
      .select(`
        organizations (
          id,
          org_name,
          org_icon,
          is_active
        ),
        org_id,
        org_member_role,
        org_member_is_active
      `)
      .eq('user_id', user.id)
      .or('org_member_is_active.eq.true,org_member_is_active.eq.false') // Get both active and inactive memberships
      .in('org_member_role', [1, 2, 3, 4, 5, 6]) // Include all possible roles

    // For organizations where user is not a superadmin, filter by active only
    // For organizations where user is a superadmin, include both active and inactive
    if (superadminOrgIds.length > 0) {
      console.log(`[${requestId}] Including inactive organizations only where user is a superadmin:`, {
        userId: user.id,
        superadminOrgIds
      })

      // Don't try to create a complex OR condition - just don't filter by is_active
      // This will include both active and inactive organizations
      // If we need to be more precise, we can filter the results after fetching
    } else {
      // User is not a superadmin anywhere, only show active organizations
      query = query.eq('organizations.is_active', true)
      countQuery = countQuery.eq('organizations.is_active', true)
    }

    // Add pagination if requested
    if (isPaginated) {
      const from = (page - 1) * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)
    }

    // Add ordering for consistency
    query = query.order('org_member_is_active', { ascending: false })
          .order('org_name', { ascending: true, referencedTable: 'organizations' })

    // Execute queries
    const [organizationsResult, countResult] = await Promise.all([
      query,
      isPaginated ? countQuery : null
    ]);

    const { data: organizations, error: orgError } = organizationsResult;
    const { count: totalCount, error: countError } = countResult || { count: null, error: null };

    if (orgError) {
      console.log(`[${requestId}] Error fetching organizations:`, {
        error: orgError,
        userId: user.id,
        duration: Date.now() - startTime
      })
      return NextResponse.json({ error: 'Error fetching organizations' }, { status: 500 })
    }

    if (countError && isPaginated) {
      console.error(`[${requestId}] Error counting organizations:`, {
        error: countError,
        userId: user.id
      })
      // Continue with the organizations we've fetched
    }

    // Filter organizations after fetching:
    // For the organization-switcher, we need to include ALL organizations that the user is a member of,
    // even if they're inactive, because the UI will handle showing them as disabled
    const filteredOrganizations = organizations || [];

    // Get roles for mapping role IDs to names
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('role_id, role_name')

    if (rolesError) {
      console.error(`[${requestId}] Error fetching roles:`, {
        error: rolesError,
        duration: Date.now() - startTime
      })
      return NextResponse.json({ error: 'Failed to fetch roles' }, { status: 500 })
    }

    // Create a role mapping for easy lookup
    const roleMap = (roles || []).reduce((acc, role) => {
      acc[role.role_id] = role.role_name;
      return acc;
    }, {} as Record<number, string>);

    // Filter out organizations with null organization data
    const validOrganizations = (filteredOrganizations as unknown as OrganizationMemberWithOrg[])
      .filter(org => org.organizations !== null);

    // Transform the data to match the expected format
    const transformedOrganizations = validOrganizations.map(org => {
      const orgId = org.organizations.id
      const orgIsActive = org.organizations.is_active
      const userRoleId = org.org_member_role
      // Use evaluateRbac to check if user is a superadmin, for consistency with rest of app
      const isSuperAdminInThisOrg = evaluateRbac(userRoleId, { rRoles: ["superAdmin"] })

      // Log detailed information about each organization's access
      if (!orgIsActive) {
        console.log(`[${requestId}] Processing inactive organization:`, {
          orgId,
          orgName: org.organizations.org_name,
          userRoleId,
          roleName: roleMap[userRoleId] || 'Unknown',
          isSuperAdminInThisOrg,
          reason: isSuperAdminInThisOrg
            ? 'Included: User is superadmin in this organization'
            : 'Warning: Inactive org included - this should not happen'
        })
      }

      return {
        id: orgId,
        name: org.organizations.org_name || `Organization ${orgId.substring(0, 8)}`,
        org_icon: org.organizations.org_icon || '',
        org_member_role: userRoleId,
        org_member_is_active: org.org_member_is_active,
        isActive: orgIsActive, // Include organization's active status
        role: roleMap[userRoleId] || 'Unknown Role'
      }
    })

    console.log(`[${requestId}] Request completed successfully:`, {
      userId: user.id,
      organizationCount: transformedOrganizations.length,
      isPaginated,
      ...(isPaginated ? { page, pageSize, totalCount } : {}),
      duration: Date.now() - startTime,
      sample: transformedOrganizations.length > 0 ? {
        org: transformedOrganizations[0].name,
        orgActive: transformedOrganizations[0].isActive,
        memberActive: transformedOrganizations[0].org_member_is_active,
        role: transformedOrganizations[0].role
      } : null
    })

    // Return paginated response or direct organizations list based on what was requested
    if (isPaginated) {
      return NextResponse.json({
        organizations: transformedOrganizations,
        totalCount: totalCount || transformedOrganizations.length,
        page,
        pageSize
      } as PaginatedOrganizationsResponse)
    } else {
      return NextResponse.json(transformedOrganizations)
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, {
      error,
      duration: Date.now() - startTime
    })
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}