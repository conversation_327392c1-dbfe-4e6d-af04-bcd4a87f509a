// Re-export event bus related items
export { 
  emitter, 
  type Events, 
  useOrganizationContextEvents,
  useUserRoleEvents,
  useMemberStatusEvents,
  useOrgNameEvents,
  useAllMemberChanges,
  organizationEventBus
} from './eventBus'

// Re-export event types
export type {
  OrganizationContextEvent,
  UserRoleChangedEvent,
  MemberStatusChangedEvent,
  OrgNameChangedEvent,
  AnyMemberChangeEvent
} from './eventTypes' 