// Re-export event bus related items
export {
  emitter,
  type Events,
  useAllMemberChanges,
  organizationEventBus
} from './eventBus'

// Legacy hooks removed - use AUTH_CONTEXT_CHANGED event directly via useAuthContextEvents
// - useOrganizationContextEvents (removed)
// - useUserRoleEvents (removed)
// - useMemberStatusEvents (removed)
// - useOrgNameEvents (removed)

// Legacy event types removed - use AUTH_CONTEXT_CHANGED event instead
// - OrganizationContextEvent (removed)
// - UserRoleChangedEvent (removed)
// - MemberStatusChangedEvent (removed)
// - OrgNameChangedEvent (removed)
// - AnyMemberChangeEvent (still used by allMembers interpreter)