import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Text,
  Button,
  Hr,
} from '@react-email/components';

interface InvitationEmailProps {
  organizationName: string;
  inviterName?: string;
  roleName: string;
  acceptUrl: string;
  personalMessage?: string;
  expiresAt: string;
}

export function InvitationEmail({
  organizationName,
  inviterName,
  roleName,
  acceptUrl,
  personalMessage,
  expiresAt,
}: InvitationEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={section}>
            <Text style={title}>You've been invited to join {organizationName}</Text>

            <Text style={text}>
              You have been invited to join <strong>{organizationName}</strong> as a <strong>{roleName}</strong>.
            </Text>

            {inviterName && (
              <Text style={text}>
                This invitation was sent by <strong>{inviterName}</strong>.
              </Text>
            )}

            {personalMessage && (
              <Text style={messageText}>
                "{personalMessage}"
              </Text>
            )}

            <Button style={button} href={acceptUrl}>
              Accept Invitation
            </Button>

            <Text style={text}>
              Or copy and paste this URL into your browser:
            </Text>
            <Text style={link}>{acceptUrl}</Text>

            <Hr style={hr} />

            <Text style={footer}>
              This invitation will expire on {expiresAt}. If you didn't expect this invitation, you can safely ignore it.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles - matching magic link template
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const section = {
  padding: '0 48px',
};

const title = {
  fontSize: '24px',
  lineHeight: '1.3',
  fontWeight: '700',
  color: '#194852',
  margin: '30px 0',
};

const text = {
  fontSize: '16px',
  lineHeight: '1.4',
  color: '#51545e',
  margin: '16px 0',
};

const messageText = {
  fontSize: '16px',
  lineHeight: '1.4',
  color: '#51545e',
  fontStyle: 'italic',
  margin: '16px 0',
  padding: '16px',
  backgroundColor: '#f8f9fa',
  borderLeft: '4px solid #194852',
  borderRadius: '4px',
};

const button = {
  backgroundColor: '#194852',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '12px',
  margin: '24px 0',
};

const link = {
  fontSize: '14px',
  color: '#194852',
  textDecoration: 'underline',
  wordBreak: 'break-all' as const,
  margin: '16px 0',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  fontSize: '12px',
  color: '#8898aa',
  lineHeight: '1.4',
  margin: '8px 0',
};

export default InvitationEmail;
