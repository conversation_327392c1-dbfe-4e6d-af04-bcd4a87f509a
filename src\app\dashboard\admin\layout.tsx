import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { evaluateRbac } from "@/lib/rbac/rbac-utils";
import { RoleId } from "@/lib/rbac/roles";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();

  // Check if user is authenticated
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Check if user has superadmin role in app_metadata
  const appMetadata = user.app_metadata;
  const isSuperAdmin = appMetadata?.role === "superadmin";

  // Check if user has superadmin role in any organization
  const { data: memberData } = await supabase
    .from("organization_members")
    .select("org_member_role")
    .eq("user_id", user.id)
    .eq("org_member_role", RoleId.SUPERADMIN)
    .limit(1)
    .single();

  const isOrgSuperAdmin = memberData?.org_member_role !== undefined && 
                          evaluateRbac(memberData.org_member_role, { rRoles: ["superAdmin"] });

  // Allow access if user is either a global superadmin or an organization superadmin
  if (!isSuperAdmin && !isOrgSuperAdmin) {
    redirect("/dashboard");
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">{children}</main>
    </div>
  );
}
