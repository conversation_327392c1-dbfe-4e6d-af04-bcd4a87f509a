import React from 'react';
import * as LucideIcons from 'lucide-react';
import { IconProps } from '@/types/components/shared/IconProps';

export const Icon = ({ name, className }: IconProps) => {
  // Convert the first character to uppercase to match Lucide's naming convention
  const iconName = name.charAt(0).toUpperCase() + name.slice(1);
  // Cast to unknown first to avoid type errors
  const icons = LucideIcons as unknown as { [key: string]: React.FC<{ className?: string }> };
  const IconComponent = icons[iconName];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found, falling back to HelpCircle`);
    return <LucideIcons.HelpCircle className={className} />;
  }
  
  return <IconComponent {...(className && { className })} />;
};