import { useEffect } from 'react';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { toastMessages } from '@/lib/toast-messages';
import { usePathname } from 'next/navigation';

interface PendingOrgSwitch {
  orgId: string;
  roleId: number;
  isUserActiveInOrg: boolean;
  isOrgActive: boolean;
  activeOrgName: string;
  timestamp: number;
}

/**
 * Simple hook to handle organization switches after navigation
 * This completes the organization switch that was stored in sessionStorage
 */
export function usePostNavigationOrgSwitch() {
  const pathname = usePathname();

  useEffect(() => {
    const handlePendingSwitch = async () => {
      if (typeof window === 'undefined') {
        return;
      }

      // Check if there's pending data first - don't log if there isn't any
      const pendingData = sessionStorage.getItem('pendingOrgSwitch');
      if (!pendingData) {
        return; // Silent return - no need to log on every navigation
      }

      console.log('[PostNavOrgSwitch] Found pending switch, processing...', { pathname });

      // Wait for the next tick to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 0));

      try {
        const pendingSwitch: PendingOrgSwitch = JSON.parse(pendingData);
        
        // Check if the pending switch is recent (within 30 seconds)
        const isRecent = Date.now() - pendingSwitch.timestamp < 30000;
        if (!isRecent) {
          console.log('[PostNavOrgSwitch] Pending switch is too old, ignoring');
          sessionStorage.removeItem('pendingOrgSwitch');
          return;
        }

        console.log('[PostNavOrgSwitch] Processing pending organization switch:', {
          orgId: pendingSwitch.orgId,
          orgName: pendingSwitch.activeOrgName,
          roleId: pendingSwitch.roleId
        });

        // Update Zustand store with the new organization context
        const currentState = useAuthContextStore.getState();
        const newContext = {
          userId: currentState.userId,
          orgId: pendingSwitch.orgId,
          roleId: pendingSwitch.roleId,
          isUserActiveInOrg: pendingSwitch.isUserActiveInOrg,
          isOrgActive: pendingSwitch.isOrgActive,
          activeOrgName: pendingSwitch.activeOrgName,
          userEmail: currentState.userEmail,
          userFullName: currentState.userFullName,
          avatarUrl: currentState.avatarUrl,
        };

        useAuthContextStore.getState().updateFullContext(newContext);

        // Mark this tab as the initiator to ignore subsequent events
        const switchId = `switch_${Date.now()}_${Math.random()}`;
        sessionStorage.setItem('currentOrgSwitchId', switchId);

        // Call server action to update database
        try {
          const mod = await import('@/app/actions/switch-organization');
          const result = await mod.switchOrganization(pendingSwitch.orgId);

          if (result && result.success !== false) {
            console.log('[PostNavOrgSwitch] Server action completed successfully');
            toastMessages.organization.switchSuccess();
          } else {
            throw new Error(result?.error || 'Failed to switch organization');
          }
        } catch (error) {
          console.error('[PostNavOrgSwitch] Server action failed:', error);
          toastMessages.organization.switchError(error instanceof Error ? error.message : undefined);
        }

        // Clean up the pending switch data
        sessionStorage.removeItem('pendingOrgSwitch');

        // Signal completion to organization switcher
        window.dispatchEvent(new CustomEvent('orgSwitchCompleted'));

        // Clear the switch ID after a delay to allow events to settle
        setTimeout(() => {
          sessionStorage.removeItem('currentOrgSwitchId');
          console.log('[PostNavOrgSwitch] Cleared switch ID, will now process external events');
        }, 2000); // 2 second delay to ignore immediate feedback events

        console.log('[PostNavOrgSwitch] Organization switch completed successfully');

      } catch (error) {
        console.error('[PostNavOrgSwitch] Error processing pending switch:', error);
        sessionStorage.removeItem('pendingOrgSwitch');

        // Signal completion even on error to clear switching flags
        window.dispatchEvent(new CustomEvent('orgSwitchCompleted'));
      }
    };

    // Process pending switch on mount and pathname changes
    handlePendingSwitch();
  }, [pathname]); // Run when pathname changes to catch navigation
}
