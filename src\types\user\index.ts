import type { UserProfile } from './UserProfile';
import type { UserPersonalInfo } from './UserPersonalInfo';

/**
 * Combined user profile with personal information
 * Use this when both profile and personal info are needed together
 */
export interface UserProfileWithPersonalInfo extends UserProfile {
  personal_info: UserPersonalInfo | null;
}

// Re-export for convenience
export type { UserProfile, UserPersonalInfo }; 