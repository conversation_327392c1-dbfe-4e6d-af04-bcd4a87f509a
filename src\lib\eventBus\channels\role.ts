// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import type { OrganizationMemberInsertedEvent, OrganizationMemberUpdatedEvent, UserRoleChangedEvent, AuthEvent } from '@/lib/eventTypes'
import { AUTH_CONTEXT_CHANGED } from '../constants'

// Using debounce instead of throttle to avoid dependency issues
// This more specific type ensures proper event handler compatibility
function debounce<EventType>(func: (event: EventType) => void, wait: number): (event: EventType) => void {
  let timeout: NodeJS.Timeout | null = null;
  return (event: EventType) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(event);
    }, wait);
  };
}

// Use a module-level variable to track registration
let isRegistered = false

// Event deduplication cache
const recentEvents = new Map<string, number>();

// Cleanup old events periodically
if (typeof window !== 'undefined') {
  setInterval(() => {
    const now = Date.now();
    recentEvents.forEach((timestamp, key) => {
      // Remove events older than 1 second
      if (now - timestamp > 1000) {
        recentEvents.delete(key);
      }
    });
  }, 5000);
}

// Check if an event is a duplicate (same user, org, and values within a short time window)
function isDuplicateEvent(userId: string, orgId: string, data: Record<string, unknown>): boolean {
  const key = `${userId}:${orgId}:${JSON.stringify(data)}`;
  const now = Date.now();
  
  if (recentEvents.has(key)) {
    return true;
  }
  
  // Store this event as recently processed
  recentEvents.set(key, now);
  return false;
}

/**
 * Listen for raw db:organization_members:* events and emit user:role:changed events as appropriate.
 */
export function registerRoleInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    // console.log('[RoleInterpreter] Already registered, skipping duplicate registration') // Optional: keep if you want to know about skips
    return
  }
  
  isRegistered = true

  // Handle INSERT (new member added)
  emitter.on('db:organization_members:inserted', (event: OrganizationMemberInsertedEvent) => {
    const { orgId, userId, data, timestamp } = event
    console.log('[RoleInterpreter] db:organization_members:inserted handler fired:', event)
    
    if (!orgId || !userId || typeof data.org_member_role !== 'number') {
      console.log('[RoleInterpreter] Missing required data for role event, skipping')
      return
    }
    
    const roleEvent: UserRoleChangedEvent = {
      userId,
      orgId,
      newRoleId: data.org_member_role,
      timestamp,
      eventType: 'INSERT',
    }
    clientLog(`[EventBus] User ${userId} in org ${orgId} role changed to ${data.org_member_role}`)
    console.log('[RoleInterpreter] Emitting user:role:changed event:', roleEvent)
    emitter.emit('user:role:changed', roleEvent)
    
    // Also emit unified AUTH_CONTEXT_CHANGED event
    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'ROLE_CHANGE',
      data: {
        newRoleId: data.org_member_role
      }
    }
    console.log('[RoleInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  })

  // Handle UPDATE (role changed)
  // Debounced event handler to prevent multiple rapid-fire events
  const handleOrgMemberUpdated = debounce((event: OrganizationMemberUpdatedEvent) => {
    const { orgId, userId, data } = event
    const newRoleId = data.org_member_role

    // Early exit: Only process events for current organization context
    if (!data.is_current_context) {
      return; // Event is not for current organization context
    }

    // Early exit: Skip if role didn't change (most efficient check)
    if (newRoleId === data.old_org_member_role) {
      return; // No logging needed for unchanged roles
    }

    console.log('[RoleInterpreter] db:organization_members:updated handler fired:', event)
    
    if (!orgId || !userId || typeof newRoleId !== 'number') {
      console.log('[RoleInterpreter] Missing current role data, skipping')
      return
    }
    
    // Determine old role value safely, with fallbacks
    let oldRoleId: number | undefined;
    
    // Try direct property from enhanced payload
    if (typeof data.old_org_member_role === 'number') {
      oldRoleId = data.old_org_member_role;
    } 
    // Try to extract from the old record if it's present
    else if (data.old) {
      const oldData = data.old as Record<string, unknown>;
      if (typeof oldData['org_member_role'] === 'number') {
        oldRoleId = oldData['org_member_role'] as number;
      }
    }
    
    console.log('[RoleInterpreter] Role values:', { newRole: newRoleId, oldRole: oldRoleId });
    
    if (oldRoleId === undefined) {
      console.log('[RoleInterpreter] No previous role data could be found, skipping')
      return
    }
    
    if (newRoleId === oldRoleId) {
      console.log('[RoleInterpreter] Role did not change, skipping event emission')
      return
    }
    
    // Check for duplicate events within a short time window
    if (isDuplicateEvent(userId, orgId, { newRoleId, oldRoleId })) {
      console.log('[RoleInterpreter] Detected duplicate event, skipping to prevent event storms')
      return
    }
    
    clientLog(`[EventBus] User ${userId} in org ${orgId} role changed from ${oldRoleId} to ${newRoleId}`)
    console.log('[RoleInterpreter] Emitting AUTH_CONTEXT_CHANGED event for role change')
    
    // Emit only the unified AUTH_CONTEXT_CHANGED event
    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'ROLE_CHANGE',
      data: {
        oldRoleId,
        newRoleId: data.org_member_role
      }
    }
    console.log('[RoleInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  }, 250);  // Throttle to at most one event every 250ms
  
  emitter.on('db:organization_members:updated', handleOrgMemberUpdated)

  // Handle DELETE (member removed)
  emitter.on('db:organization_members:deleted', (event) => {
    console.log('[RoleInterpreter] db:organization_members:deleted handler fired:', event)
    // Optionally emit a role change event for cleanup if needed
  })
} 