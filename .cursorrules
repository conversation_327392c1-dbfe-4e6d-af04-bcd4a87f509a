Prefix the response with 🚀 so we know you are ready to help.

# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again.

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## Next.js Dynamic APIs

- Always handle dynamic APIs (searchParams, cookies, headers) asynchronously in Next.js 15+
- Use React.use() in Client Components to unwrap dynamic API Promises
- Use await in Server Components to unwrap dynamic API Promises
- Never access dynamic API properties directly without awaiting

Example for Server Components:
```typescript
interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default async function Page({ searchParams }: PageProps) {
  const params = await searchParams;
  // Now you can safely use params.id
}
```

Example for Client Components:
```typescript
'use client'

import { use } from 'react'

interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default function Page({ searchParams }: PageProps) {
  const params = use(searchParams);
  // Now you can safely use params.id
}
```

## Cursor learned

- follows Next.js's server-first pattern throughout the application
- Next.js cookies need to be handled asynchronously with async/await
- Use Supabase SSR package instead of auth-helpers (deprecated)
- Always handle cookies asynchronously in Next.js 15+
- Always use the updated cookieStore method in Next.js 15+
- Use npx shadcn@latest add [component] to add Shadcn UI components (NOT npx shadcn-ui@latest)
- Navigation authorization should be handled server-side for immediate role knowledge
- Avoid client-side role fetching to prevent incorrect initial states
- Never use @supabase/auth-helpers-nextjs as it's deprecated. Use @supabase/ssr instead for server-side Supabase authentication
- Always use supabase.auth.getUser() instead of relying on session.user for authentication, as session data could be insecure
- Always use Next.js Image component (<Image />) from 'next/image' instead of HTML <img> tag for better performance and optimization
- TypeScript interfaces for handbook components are located in src/types/handbook.ts
- Always add null checks for optional props in React components and provide fallback UI to prevent runtime errors
- The project is already running on port 3000 - don't try to start it again

## Next.js Page Load Optimization

- Avoid redundant context refreshes after server-side rendering
- Client components should trust data passed from server components during initial hydration
- For authenticated dashboards, the context received from middleware is already fresh
- Event managers should skip initial context refresh API calls on first mount
- Use `revalidateOnMount: false` in SWR to prevent unnecessary initial fetches
- Separate authentication token refresh from context refresh operations
- Extract reusable subscription setup logic into dedicated functions
- Use global flags like `isFirstLoad` to track and optimize the initial page load flow

## Supabase Realtime Channel Management

- Use a global window flag to prevent duplicate channel subscriptions (`window.__hasSubscribedDashboardChannels`)
- Don't unsubscribe channels during client-side navigation - only on tab visibility change (hidden)
- Implement a delayed unsubscribe on tab visibility change (5-second delay) to prevent rapid subscribe/unsubscribe cycles
- Handle common channel state changes like disconnection and reconnection gracefully
- Log only true errors at error level - normal reconnection cycles should be debug level
- Detect true broken channels using the `areChannelsBroken()` function
- Only attempt channel resubscription when tab has been hidden for significant time or channels are truly broken
- Use channel state as the source of truth rather than manual tracking of subscription status
- Preserve channel subscriptions during client-side navigation by not unsubscribing on component unmount
- Make channel management code robust to Hot Module Replacement (HMR) and React lifecycle events
- Delay initial channel subscriptions until after DOM is fully loaded using document.readyState check
- Use requestIdleCallback or setTimeout fallback to avoid subscriptions during critical rendering phases
- Implement a connection state tracker to prevent subscription race conditions during page initialization
- Add proper error handling and finally blocks to ensure initialization flags are always reset
- Track connection stability over time to avoid unnecessary resubscription attempts during normal operations
- Normal disconnect/reconnect cycles with "Subscribed to PostgreSQL" messages are part of Supabase's standard behavior and don't indicate problems
- Supabase channels seem to undergo multiple reconnection cycles during initialization before stabilizing

# Supabase Realtime Protocol Behavior

- Supabase Realtime is built on Phoenix Channels which uses a specific WebSocket lifecycle pattern
- The normal connection lifecycle follows this pattern:
  1. Initial Subscription: Channel is created and subscribe() is called
  2. Connection Establishment: WebSocket connects and channel state changes to 'joined'
  3. PostgreSQL Confirmation: Server sends 'Subscribed to PostgreSQL' via error channel
  4. Phoenix Socket Cycle: 
     - Socket temporarily disconnects ("Channel disconnected")
     - Phoenix heartbeat mechanism triggers reconnection ("Channel reconnected")
     - This cycle repeats 2-3 times as part of normal Phoenix session establishment
  5. Connection Stabilizes: After initial cycles, connection becomes stable
  6. Ongoing Maintenance: Phoenix maintains connection with heartbeats every 30s
- These reconnection cycles are normal, expected, and BY DESIGN
- Even with perfect network conditions, these cycles will occur
- Solution approach:
  - Log normal reconnection cycles at debug level (not warnings/errors)
  - Only treat as warning when exceeding normal Phoenix reconnection pattern
  - Maintain connection state tracking to prevent duplicate subscriptions
  - Build application resilience to handle the normal socket lifecycle
  - Don't try to eliminate the cycles - they're part of the Phoenix protocol

# Project Structure

## Types

### Handbook Types (src/types/handbook.ts)

Contains all TypeScript interfaces for the handbook feature:

- HandbookProps: Main props interface for the Handbook component
- Service: Service information interface
- Criteria: Eligibility criteria interface
- Procedures: Service procedures interface
- RequiredDocuments: Required documents interface
- Fee: Service fees interface
- ServiceChannels: Service locations interface
- ComplaintChannels: Complaint channels interface
- ApplicationForms: Application forms interface
- BottomRemarks: Additional information interface
- Source: Source information interface

# Scratchpad

## Current Task: Optimizing Supabase Realtime Channel Management

We're improving the Supabase realtime channel subscription process to eliminate unnecessary reconnections during page load.

Progress:
[X] Analyzed server logs to identify the reconnection issue
[X] Found that channel subscriptions were happening too early in the page lifecycle
[X] Implemented DOM-ready check to delay subscriptions until after page is fully loaded
[X] Added requestIdleCallback / setTimeout fallback to postpone connections to idle time
[X] Created connection state tracking to prevent duplicate subscriptions
[X] Added proper error handling for connection attempts
[X] Improved unsubscription logic based on tab visibility
[X] Documented new patterns in the .cursorrules file
[X] Enhanced abnormal reconnection detection
[X] Added comprehensive documentation about Supabase's normal reconnection patterns

Key findings:
- Supabase Realtime has a specific connection handshake pattern that involves multiple reconnections
- These reconnections are NORMAL and part of the protocol - not a bug to be fixed
- The pattern is: subscribe -> confirm -> disconnect -> reconnect -> stabilize
- Our solution is to properly handle and log these normal patterns while detecting true issues
- We've added connection state tracking to distinguish between normal patterns and real problems

Known issues:
- TypeScript errors with Supabase's channel API (difficult to fix without modifying Supabase types)
- Proper typing for the postgres_changes event in Supabase's RealtimeChannel API

Next steps:
- Test the changes with network throttling to verify reconnection behavior
- Consider implementing memory caching for frequently accessed data
- Monitor performance metrics after changes

# General

- Use always use npx shadcn@latest init to install shadcn/ui. Avoid using shadcn-ui as it's deprecated.
- Use always use npx shadcn@latest add [component] to add a component to the project.
- Always use pnpm as the package manager.

You are an expert developer in TypeScript, Node.js, Next.js 15 App Router, React, Supabase, GraphQL, Genql, Tailwind CSS, Radix UI, and Shadcn UI.

Key Principles

- Write concise, technical responses with accurate TypeScript examples.
- Use functional, declarative programming. Avoid classes.
- Prefer iteration and modularization over duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.
- Use the Receive an Object, Return an Object (RORO) pattern.

JavaScript/TypeScript

- Use "function" keyword for pure functions. Omit semicolons.
- Use TypeScript for all code. Prefer interfaces over types.
- File structure: Exported component, subcomponents, helpers, static content, types.
- Avoid unnecessary curly braces in conditional statements.
- For single-line statements in conditionals, omit curly braces.
- Use concise, one-line syntax for simple conditional statements (e.g., if (condition) doSomething()).

Error Handling and Validation

- Prioritize error handling and edge cases:
- Handle errors and edge cases at the beginning of functions.
- Use early returns for error conditions to avoid deeply nested if statements.
- Place the happy path last in the function for improved readability.
- Avoid unnecessary else statements; use if-return pattern instead.
- Use guard clauses to handle preconditions and invalid states early.
- Implement proper error logging and user-friendly error messages.
- Consider using custom error types or error factories for consistent error handling.

AI SDK

- Use the Vercel AI SDK UI for implementing streaming chat UI.
- Use the Vercel AI SDK Core to interact with language models.
- Use the Vercel AI SDK RSC and Stream Helpers to stream and help with the generations.
- Implement proper error handling for AI responses and model switching.
- Implement fallback mechanisms for when an AI model is unavailable.
- Handle rate limiting and quota exceeded scenarios gracefully.
- Provide clear error messages to users when AI interactions fail.
- Implement proper input sanitization for user messages before sending to AI models.
- Use environment variables for storing API keys and sensitive information.

React/Next.js

- Use functional components and TypeScript interfaces.
- Use declarative JSX.
- Nextjs cookies are handled by the supabase auth helper & needs to use async/Await @https://nextjs.org/docs/app/api-reference/functions/cookies.
- Use function, not const, for components.
- Use Shadcn UI, Radix, and Tailwind CSS for components and styling.
- Implement responsive design with Tailwind CSS.
- Use mobile-first approach for responsive design.
- Place static content and interfaces at file end.
- Use content variables for static content outside render functions.
- Minimize 'use client', 'useEffect', and 'setState'. Favor React Server Components (RSC).
- Use Zod for form validation.
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: WebP format, size data, lazy loading.
- Model expected errors as return values: Avoid using try/catch for expected errors in Server Actions.
- Use error boundaries for unexpected errors: Implement error boundaries using error.tsx and global-error.tsx files.
- Use useActionState with react-hook-form for form validation.
- Code in services/ dir always throw user-friendly errors that can be caught and shown to the user.
- Use next-safe-action for all server actions.
- Implement type-safe server actions with proper validation.
- Handle errors gracefully and return appropriate responses.

Supabase and GraphQL

- Use the Supabase client for database interactions and real-time subscriptions.
- Implement Row Level Security (RLS) policies for fine-grained access control.
- Use Supabase Auth for user authentication and management.
- Leverage Supabase Storage for file uploads and management.
- Use Supabase SSR instead of the auth-helpers-nextjs package(deprecated).
- Use Supabase Edge Functions for serverless API endpoints when needed.
- Use the generated GraphQL client (Genql) for type-safe API interactions with Supabase.
- Optimize GraphQL queries to fetch only necessary data.
- Use Genql queries for fetching large datasets efficiently.
- Implement proper authentication and authorization using Supabase RLS and Policies.

Key Conventions

1. Rely on Next.js App Router for state changes and routing.
2. Prioritize Web Vitals (LCP, CLS, FID).
3. Minimize 'use client' usage:

- Prefer server components and Next.js SSR features.
- Use 'use client' only for Web API access in small components.
- Avoid using 'use client' for data fetching or state management.

4. Follow the monorepo structure:

- Place shared code in the 'packages' directory.
- Keep app-specific code in the 'apps' directory.

5. Use Taskfile commands for development and deployment tasks.
6. Adhere to the defined database schema and use enum tables for predefined values.

Naming Conventions

- Booleans: Use auxiliary verbs such as 'does', 'has', 'is', and 'should' (e.g., isDisabled, hasError).
- Filenames: Use lowercase with dash separators (e.g., auth-wizard.tsx).
- File extensions: Use .config.ts, .test.ts, .context.tsx, .type.ts, .hook.ts as appropriate.

Component Structure

- Break down components into smaller parts with minimal props.
- Suggest micro folder structure for components.
- Use composition to build complex components.
- Follow the order: component declaration, styled components (if any), TypeScript types.

Data Fetching and State Management

- Use React Server Components for data fetching when possible.
- Implement the preload pattern to prevent waterfalls.
- Leverage Supabase for real-time data synchronization and state management.
- Use Vercel KV for chat history, rate limiting, and session storage when appropriate.

Styling

- Use Tailwind CSS for styling, following the Utility First approach.
- Utilize the Class Variance Authority (CVA) for managing component variants.

Testing

- Implement unit tests for utility functions and hooks.
- Use integration tests for complex components and pages.
- Implement end-to-end tests for critical user flows.
- Use Supabase local development for testing database interactions.

Accessibility

- Ensure interfaces are keyboard navigable.
- Implement proper ARIA labels and roles for components.
- Ensure color contrast ratios meet WCAG standards for readability.

Documentation

- Provide clear and concise comments for complex logic.
- Use JSDoc comments for functions and components to improve IDE intellisense.
- Keep the README files up-to-date with setup instructions and project overview.
- Document Supabase schema, RLS policies, and Edge Functions when used.

Authorization:

- Use server-side authorization for navigation items.
- Use server-side authorization for data fetching.
- Use server-side authorization for API calls.
- Use server-side authorization for file uploads.
- Use server-side authorization for real-time data synchronization.

Tables for authorization logic:
organization_members.org_id, which has a fk to organizations.id
organization_members.user_id, which has a fk to profiles.id
organization_member_role, which has a fk to roles.role_id

the organization_members table has: org_id(pk),user_id(pk),org_member_role, org_member_is_active(pk),created_at,updated_at,org_member_updated_by,is_default_org(pk)
the organizations table has: id(pk),org_name,created_at,updated_at,is_active
the profiles table has: id(pk),full_name,email,avatar_url,username
the roles table has: role_id(pk), role_name

Refer to Next.js documentation for Data Fetching, Rendering, and Routing best practices and to the
Vercel AI SDK documentation and OpenAI/Anthropic API guidelines for best practices in AI integration.

# RBAC Structure



## Database Structure

- organization_members table:
  - org_id (pk)
  - user_id (pk)
  - org_member_role
  - org_member_is_active (pk)
  - created_at
  - updated_at
  - org_member_updated_by
  - is_default_org (pk)

- organizations table:
  - id (pk)
  - org_name
  - created_at
  - updated_at
  - is_active

- profiles table:
  - id (pk)
  - full_name
  - email
  - avatar_url
  - username

- roles table:
  - role_id (pk)
  - role_name


### RBAC

#### Role Hierarchy (from highest to lowest)

1. Superadmin (ID: 1) - Full system access
2. Support Admin (ID: 2) - Can manage all organizations
3. Organization Admin (ID: 3) - Can manage their organization
4. Organization Member (ID: 4) - Basic member access
5. Organization Accounting (ID: 5) - View-only access
6. Organization Client (ID: 6) - Most restricted access

#### Permission Mapping
- Roles are defined in `src/lib/rbac/roles.ts`
- RBAC checks are performed using utilities in `src/lib/rbac/rbac-utils.ts` (e.g., `evaluateRbac`, `checkMinRole`, `checkRoles`).
- RBAC props (e.g., `rMinRole`, `ruRoles`, etc.) are used to declaratively specify access requirements for CRUD and custom operations.
- All permission checks in routes and components must use these centralized utilities.

#### Centralized Business Logic Rules
- Business-specific permission logic is implemented in `src/lib/permission-utils.ts`.
- These functions extend the base RBAC system with domain rules (e.g., self-modification prevention, role hierarchy enforcement, etc.).
- **Key functions:**
  - `canToggleMemberStatus(member, actingUserRoleId, actingUserId)`: Can the acting user toggle the status of the target member?
  - `canChangeUserRole(member, actingUserRoleId, actingUserId, newRoleId?)`: Can the acting user change the target member's role (optionally to a specific new role)?
  - `canDeleteMember(member, actingUserRoleId, actingUserId)`: Can the acting user remove the target member from the organization?
  - `canEditUserProfile(targetMember, userRole, userId, isSameOrg)`: Can the acting user edit the target member's profile?
- These functions must be used in both API routes and UI components to ensure consistent enforcement of business rules.
- **Pattern:**
  - Never duplicate business logic in routes or components—always call the shared utility.
  - All permission checks should be performed server-side for security, and client-side for UI/UX.

#### Example Usage
- In API routes: Use `canToggleMemberStatus`, `canChangeUserRole`, etc., before mutating data or returning sensitive information.
- In React components: Use the same functions to enable/disable UI actions or hide/show elements based on permissions.

#### Documentation
- Document any new business rules in this section and in the relevant utility file.

#### RBAC Architecture & Layering

The RBAC and permission system is designed with clear separation of concerns and a layered approach. This ensures DRY code, maintainability, and consistent enforcement of business rules across both server and client.

**Layered Structure:**

1. **RBAC Core Utilities (Lowest Level)**
   - Files: `src/lib/rbac/rbac-utils.ts`, `src/lib/rbac/roles.ts`
   - Responsibilities:
     - Define roles, permissions, and mappings.
     - Provide generic utilities for evaluating RBAC rules (e.g., `evaluateRbac`, `checkMinRole`).
     - No business/domain logic—pure RBAC.

2. **Centralized Business Logic**
   - File: `src/lib/permission-utils.ts`
   - Responsibilities:
     - Implements domain-specific rules (e.g., "cannot change your own role").
     - Composes RBAC utilities to enforce business requirements.
     - Exposes functions like `canToggleMemberStatus`, `canChangeUserRole`, etc.
     - **All business logic must go here.**

3. **API Routes (Server Enforcement)**
   - Location: `src/app/api/...`
   - Responsibilities:
     - Call business logic utilities to enforce permissions before data access/mutation.
     - Transform and return data in the expected frontend shape.
     - Never duplicate permission logic—always call the shared utility.

4. **RBAC Hooks (Client Convenience)**
   - File: `src/hooks/use-rbac-permission.ts`
   - Responsibilities:
     - Provide a React hook for permission checks in components.
     - Wraps RBAC utilities for idiomatic React usage.
     - Should not contain business logic—just calls the shared utilities.

5. **UI Components (Highest Level)**
   - Location: `src/app/dashboard/...`, etc.
   - Responsibilities:
     - Use hooks and business logic utilities to determine what to render or enable.
     - Never implement permission logic directly—always use the shared functions.

**Flow Example:**
- A component wants to show/hide a button based on permission:
  - Calls a hook (e.g., `useRbacPermission`) or a business logic function (e.g., `canChangeUserRole`).
  - The business logic function composes RBAC utilities and applies domain rules.
  - The RBAC utility checks the user's role/permissions.
- An API route wants to update a member's status:
  - Calls `canToggleMemberStatus` before mutating data.
  - Returns an error if not permitted.

**Key Principles:**
- **DRY:** Never duplicate permission or business logic—always use the shared utilities.
- **Separation of Concerns:**
  - RBAC utilities = generic, reusable, no business rules.
  - Business logic = domain-specific, composes RBAC.
  - API/routes = enforce, never reimplement.
  - Hooks = convenience for React, no logic.
  - Components = render/UI only.
- **Extensibility:** To add new business rules, extend `permission-utils.ts` and update documentation.

**Diagram:**

```
[ UI Components ]
      ↑
[ RBAC Hooks ]
      ↑
[ Business Logic (permission-utils.ts) ]
      ↑
[ RBAC Core Utilities (rbac-utils.ts, roles.ts) ]

```

## Next.js Performance Optimization

- Avoid redundant auth context resolution between middleware, layout components, and client-side code
- Use memory caching with proper invalidation for frequently accessed data like user context
- Consolidate API calls that perform related operations (e.g., combine cache clearing with context refresh)
- Implement proper request deduplication for simultaneous client requests
- Use streaming and Suspense for progressive page loading, especially in dashboard layouts
- Initialize event managers and subscriptions early in the application lifecycle
- Prevent race conditions between server-side rendering and client-side hydration
- Implement circuit breaker patterns for external service calls to handle timeouts gracefully
- Use NextResponse correctly in middleware to preserve cookies and headers
- Break down monolithic layouts into smaller, focused server components that can render in parallel
