"use client";

import { toast } from "sonner";

// Types for toast parameters
export type ToastOptions = {
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
};

/**
 * Centralized toast message system
 *
 * Usage:
 * import { toastMessages } from "@/lib/toast-messages";
 *
 * // Success message
 * toastMessages.auth.loginSuccess();
 *
 * // Error with custom message
 * toastMessages.organization.createError("Custom error message");
 *
 * // With options
 * toastMessages.organization.switchSuccess({
 *   duration: 5000,
 *   action: {
 *     label: "Undo",
 *     onClick: () => handleUndo()
 *   }
 * });
 */

// Generic toast function helpers
const createToast = {
  success: (message: string, options?: ToastOptions) => {
    toast.success(message, options);
  },
  error: (message: string, options?: ToastOptions) => {
    toast.error(message, options);
  },
  info: (message: string, options?: ToastOptions) => {
    toast.info(message, options);
  },
  warning: (message: string, options?: ToastOptions) => {
    toast.warning(message, options);
  },
};

// Organization related toast messages
const organization = {
  createSuccess: (options?: ToastOptions) => {
    createToast.success("Organization created successfully", options);
  },
  createError: (message: string = "Failed to create organization", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  deleteSuccess: (options?: ToastOptions) => {
    createToast.success("Organization deleted successfully", options);
  },
  deleteError: (message: string = "Failed to delete organization", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  updateSuccess: (options?: ToastOptions) => {
    createToast.success("Organization updated successfully", options);
  },
  updateError: (message: string = "Failed to update organization", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  switchSuccess: (options?: ToastOptions) => {
    createToast.success("Organization switched successfully", options);
  },
  switchError: (message: string = "Failed to switch organization", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  deletionCancelled: (options?: ToastOptions) => {
    createToast.info("Deletion cancelled", options);
  },
  removedFrom: (orgName: string = "the organization", options?: ToastOptions) => {
    createToast.info(`You have been removed from ${orgName}`, options);
  },
};

// Authentication related toast messages
const auth = {
  loginSuccess: (options?: ToastOptions) => {
    createToast.success("Logged in successfully", options);
  },
  loginError: (message: string = "Login failed", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  signupSuccess: (options?: ToastOptions) => {
    createToast.success("Account created successfully", options);
  },
  signupError: (message: string = "Signup failed", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  logoutSuccess: (options?: ToastOptions) => {
    createToast.success("Logged out successfully", options);
  },
  logoutError: (message: string = "Logout failed", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  sessionError: (options?: ToastOptions) => {
    createToast.error("Session error, please log in again", options);
  },
  magicLinkExpired: (options?: ToastOptions) => {
    createToast.error("Your login link has expired. Please request a new one.", options);
  },
  magicLinkInvalid: (options?: ToastOptions) => {
    createToast.error("This login link is invalid or has already been used.", options);
  },
  magicLinkSent: (options?: ToastOptions) => {
    createToast.success("Login link sent! Check your email.", options);
  },
  authenticationFailed: (message: string = "Authentication failed", options?: ToastOptions) => {
    createToast.error(message, options);
  },
};

// Invite related toast messages
const invite = {
  createSuccess: (options?: ToastOptions) => {
    createToast.success("Invite created successfully", options);
  },
  createError: (message: string = "Failed to create invite", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  deactivateSuccess: (options?: ToastOptions) => {
    createToast.success("Invite code deactivated successfully", options);
  },
  deactivateError: (message: string = "Failed to deactivate invite code", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  copySuccess: (options?: ToastOptions) => {
    createToast.success("Invite code copied to clipboard", options);
  },
  joinSuccess: (message: string = "Successfully joined organization", options?: ToastOptions) => {
    createToast.success(message, options);
  },
  joinError: (message: string = "Failed to join organization", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  missingCode: (options?: ToastOptions) => {
    createToast.error("Please enter an invite code", options);
  },
};

// User related toast messages
const user = {
  updateSuccess: (field: string = "Profile", options?: ToastOptions) => {
    createToast.success(`${field} updated successfully`, options);
  },
  updateError: (message: string = "Failed to update profile", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  removeSuccess: (name: string = "Member", options?: ToastOptions) => {
    createToast.info(`Member removed: ${name}`, options);
  },
  removeError: (message: string = "Failed to remove member", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  fetchError: (message: string = "Failed to fetch user data", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  roleUpdated: (options?: ToastOptions) => {
    createToast.info("User role updated", options);
  },
  statusUpdate: (name: string, status: string) => toast.info(`${name}'s status updated to ${status}`),
  roleUpdate: (name: string, role: string, org_name: string) => toast.info(`${name}'s role updated to ${role} in organization ${org_name}`),
  added: (name: string, org_name: string) => toast.success(`New member added: ${name} to organization ${org_name}`),
  removed: (name: string, org_name: string) => toast.success(`Successfully removed ${name} from organization ${org_name}`),
};

// Form/flow related toast messages
const form = {
  saveSuccess: (options?: ToastOptions) => {
    createToast.success("Form saved successfully", options);
  },
  saveError: (message: string = "Failed to save form", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  createSuccess: (options?: ToastOptions) => {
    createToast.success("Form created successfully", options);
  },
  createError: (message: string = "Failed to create form", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  deleteSuccess: (options?: ToastOptions) => {
    createToast.success("Form deleted successfully", options);
  },
  deleteError: (message: string = "Failed to delete form", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  fetchError: (message: string = "Failed to fetch forms", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  reorderSuccess: (options?: ToastOptions) => {
    createToast.success("Items reordered successfully", options);
  },
  reorderError: (message: string = "Failed to reorder items", options?: ToastOptions) => {
    createToast.error(message, options);
  },
};

// Generic common messages
const common = {
  unexpectedError: (message: string = "An unexpected error occurred", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  accessDenied: (options?: ToastOptions) => {
    createToast.error("Access denied", options);
  },
  copySuccess: (item: string = "Text", options?: ToastOptions) => {
    createToast.success(`${item} copied to clipboard`, options);
  },
  uploadSuccess: (options?: ToastOptions) => {
    createToast.success("File uploaded successfully", options);
  },
  uploadError: (message: string = "Failed to upload file", options?: ToastOptions) => {
    createToast.error(message, options);
  },
  loadError: (resource: string = "data", options?: ToastOptions) => {
    createToast.error(`Failed to load ${resource}`, options);
  },
};

// Combine all toast categories
export const toastMessages = {
  organization,
  auth,
  invite,
  user,
  form,
  common,

  // Direct access to the basic toast functions for custom cases
  success: createToast.success,
  error: createToast.error,
  info: createToast.info,
  warning: createToast.warning,
};