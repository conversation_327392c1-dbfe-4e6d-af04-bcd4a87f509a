import { createClient } from '@supabase/supabase-js'

/**
 * Admin Supabase Client for server-side operations requiring service role access
 *
 * This client uses the service role key and should ONLY be used on the server.
 * It bypasses Row Level Security (RLS) and has full database access.
 *
 * Use cases:
 * - Generating magic links via admin API
 * - User management operations
 * - Administrative database operations
 *
 * Security: This client should never be exposed to the client-side
 */

interface AdminClientConfig {
  url: string;
  serviceRoleKey: string;
}

/**
 * Validates required environment variables for admin client
 */
function validateAdminConfig(): AdminClientConfig {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY;

  // Log environment variable status (without exposing values)
  console.log('[Admin Client] Environment check:', {
    NEXT_PUBLIC_SUPABASE_URL: !!url,
    NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY: !!serviceRoleKey,
    USE_CUSTOM_MAGIC_LINKS: process.env.USE_CUSTOM_MAGIC_LINKS,
  });

  if (!url) {
    console.error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');
    throw new Error('Supabase URL is required for admin client');
  }

  if (!serviceRoleKey) {
    console.error('Missing NEXT_PRIVATE_SUPABASE_SERVICE_ROLE_KEY environment variable');
    throw new Error('Supabase service role key is required for admin client');
  }

  // Validate service role key format (should start with 'eyJ' for JWT)
  if (!serviceRoleKey.startsWith('eyJ')) {
    console.error('Invalid service role key format - should be a JWT token');
    throw new Error('Invalid service role key format');
  }

  return { url, serviceRoleKey };
}

/**
 * Creates an admin Supabase client with service role access
 *
 * @returns Supabase client with admin privileges
 * @throws Error if environment variables are missing or invalid
 */
export function createAdminClient() {
  try {
    const config = validateAdminConfig();

    const client = createClient(config.url, config.serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
      global: {
        headers: {
          'x-application-name': 'visa-automation-admin',
          'x-client-type': 'admin',
        },
      },
    });

    // Log successful admin client creation (without sensitive data)
    console.info('Admin Supabase client created successfully');

    return client;
  } catch (error) {
    console.error('Failed to create admin Supabase client:', error);
    throw error;
  }
}

/**
 * Test admin client connection and permissions
 *
 * @returns Promise<boolean> - true if admin client is working correctly
 */
export async function testAdminClient(): Promise<boolean> {
  try {
    const adminClient = createAdminClient();

    // Test basic admin API access by checking auth admin methods
    const { error } = await adminClient.auth.admin.listUsers({
      page: 1,
      perPage: 1,
    });

    if (error) {
      console.error('Admin client test failed:', error.message);
      return false;
    }

    console.info('Admin client test successful - can access admin API');
    return true;
  } catch (error) {
    console.error('Admin client test failed with exception:', error);
    return false;
  }
}

/**
 * Utility function to safely log admin operations (without sensitive data)
 *
 * @param operation - The operation being performed
 * @param details - Non-sensitive details about the operation
 */
export function logAdminOperation(operation: string, details: Record<string, any> = {}) {
  // Filter out sensitive information
  const safeDetails = Object.fromEntries(
    Object.entries(details).filter(([key]) =>
      !key.toLowerCase().includes('password') &&
      !key.toLowerCase().includes('token') &&
      !key.toLowerCase().includes('key') &&
      !key.toLowerCase().includes('secret')
    )
  );

  console.info(`[Admin Operation] ${operation}`, safeDetails);
}

/**
 * Type definitions for admin operations
 */
export interface AdminMagicLinkOptions {
  email: string;
  redirectTo?: string | undefined;
  captchaToken?: string | undefined;
}

export interface AdminMagicLinkResult {
  success: boolean;
  actionLink?: string;
  error?: string;
  emailOtp?: string;
  hashedToken?: string;
}
