import { withRbac<PERSON>ermission } from "@/lib/rbac/permissions-server";
import { createClient } from "@/lib/supabase/server";
import { RoleId } from "@/lib/rbac/roles";
import ClientInvitationsClient from "@/components/invitations/client-invitations-client";
import { PageProps } from "@/types/app/PageProps";

const ClientInvitesPageInternal = async (_props: PageProps) => {
  const supabase = await createClient();

  // SECURITY: Get current organization context (same logic as permissions-server.ts)
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('No authenticated user found');
  }

  const { data: currentOrg } = await supabase
    .from('organization_members')
    .select('org_id')
    .eq('user_id', user.id)
    .eq('is_current_context', true)
    .eq('org_member_is_active', true)
    .single();

  let targetOrgId = currentOrg?.org_id;
  if (!targetOrgId) {
    const { data: initializedOrgId } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: user.id
    });
    targetOrgId = initializedOrgId;
  }

  if (!targetOrgId) {
    throw new Error('No organization context available');
  }

  // Fetch client invitations for current organization ONLY
  const { data: invitations, error } = await supabase
    .from('email_invitations')
    .select(`
      id,
      org_id,
      email,
      role_id,
      status,
      created_at,
      expires_at,
      personal_message,
      resend_email_id,
      invited_by,
      organizations!inner (org_name)
    `)
    .eq('role_id', RoleId.ORGCLIENT) // Only client invitations
    .eq('org_id', targetOrgId) // SECURITY: Only current organization
    .order('created_at', { ascending: false });

  // Fetch inviter profiles separately to avoid foreign key join issues
  let enrichedInvitations = invitations || [];
  if (invitations && invitations.length > 0) {
    const inviterIds = [...new Set(invitations.map(inv => inv.invited_by).filter(Boolean))];

    if (inviterIds.length > 0) {
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, full_name')
        .in('id', inviterIds);

      // Map profiles to invitations
      enrichedInvitations = invitations.map(invitation => ({
        ...invitation,
        invited_by_profile: profiles?.filter(p => p.id === invitation.invited_by) || []
      }));
    }
  }

  if (error) {
    console.error('Failed to fetch client invitations:', error);
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Client Invitations</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage email invitations for new clients to join your organization.
        </p>
      </div>

      <ClientInvitationsClient initialInvitations={enrichedInvitations} />
    </div>
  );
};

const ClientInvitesPage = withRbacPermission(
  { rMinRole: "orgMember" }, // Accessible by orgMember and above
  { redirectTo: "/dashboard" }
)(ClientInvitesPageInternal);

export default ClientInvitesPage;
