"use client";

import { useState, useEffect } from "react"; // FIX: Removed unused useCallback
import { useDashboard } from "@/components/providers/dashboard-provider";
import { canEditUserProfile } from "@/lib/permission-utils"; // Import the permission utility
import { createClient } from "@/lib/supabase/client";
import type { OrganizationMemberFull } from "@/types/organization/"; // Use shared types
import type { UserPersonalInfo } from "@/types/user"; // Import the central type
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { toastMessages } from "@/lib/toast-messages";
import { format } from "date-fns";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  Di<PERSON>D<PERSON><PERSON>,
  Di<PERSON>Footer,
  Di<PERSON><PERSON>eader,
  Di<PERSON><PERSON>it<PERSON>,
} from "@/components/ui/dialog";

// Use the central type instead of local interface
type EditablePersonalInfo = Omit<UserPersonalInfo, 'id' | 'updated_by'>;

interface UserProfileDialogProps {
  member: OrganizationMemberFull | null;
  isOpen: boolean;
  onCloseAction: () => void;
  onProfileUpdate?: () => void; // Optional callback after successful update
}

function getStatusColor(isActive: boolean) {
  return isActive
    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
    : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
}

export function UserProfileDialog({
  member: initialMember,
  isOpen,
  onCloseAction,
  onProfileUpdate,
}: UserProfileDialogProps) {
  const { userId } = useDashboard();
  const [member, setMember] = useState<OrganizationMemberFull | null>(initialMember);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<EditablePersonalInfo>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [roleInTargetOrg, setRoleInTargetOrg] = useState<number | null>(null);
  const [isLoadingRole, setIsLoadingRole] = useState(false);

  // Reset state when the dialog opens with a new member or closes
  useEffect(() => {
    setMember(initialMember);
    if (initialMember) {
      setEditData((initialMember.personal_info as Partial<EditablePersonalInfo>) || {});
    } else {
      setEditData({});
    }
    setIsEditing(false);
    setIsSaving(false);
    setError(null);
    setRoleInTargetOrg(null); // Reset role when member changes
  }, [initialMember, isOpen]);

  // Fetch the user's role in the target organization
  useEffect(() => {
    async function fetchRoleInTargetOrg() {
      if (!member || !userId) return;
      
      try {
        setIsLoadingRole(true);
        const supabase = createClient();
        
        // Query to get the current user's role in the target member's organization
        const { data, error } = await supabase
          .from('organization_members')
          .select('org_member_role')
          .eq('user_id', userId)
          .eq('org_id', member.org_id)
          .single();
        
        if (error) {
          console.error('Error fetching role in target org:', error);
          // Check if superadmin in any org
          const { data: superAdminData } = await supabase
            .from('organization_members')
            .select('org_member_role')
            .eq('user_id', userId)
            .eq('org_member_role', 1) // SuperAdmin
            .limit(1);
          
          // If user is superadmin in any org, use that role
          if (superAdminData && superAdminData.length > 0) {
            setRoleInTargetOrg(superAdminData[0].org_member_role);
          } else {
            setRoleInTargetOrg(null);
          }
        } else {
          setRoleInTargetOrg(data.org_member_role);
        }
      } catch (err) {
        console.error('Unexpected error fetching role:', err);
        setRoleInTargetOrg(null);
      } finally {
        setIsLoadingRole(false);
      }
    }
    
    fetchRoleInTargetOrg();
  }, [member, userId]);

  // Determine if the current user has permission to edit using our permission utility
  const canEditPersonalInfo = member && roleInTargetOrg !== null && userId && canEditUserProfile(
    {
      user_id: member.user_id,
      org_id: member.org_id,
      org_member_role: member.org_member_role,
      org_member_is_active: member.org_member_is_active
    },
    roleInTargetOrg,
    userId,
    // Check if users are in the same organization - let permission utility handle the case
    // where user isn't in the same org but is a superadmin
    true
  );

  const handleInputChange = (fieldName: keyof EditablePersonalInfo, value: string | number | null) => {
    // Handle potential type conversion for number fields like height_cm
    let processedValue = value;
    if (fieldName === 'height_cm') {
        processedValue = value === '' || value === null ? null : Number(value);
        if (isNaN(processedValue as number)) {
           // Check if editData.height_cm exists before accessing
           processedValue = editData?.height_cm ?? null; // Revert if invalid number, default to null
        }
    }
    setEditData((prev) => ({ ...prev, [fieldName]: processedValue }));
  };

  const handleSave = async () => {
    if (!member || !canEditPersonalInfo) return;

    setIsSaving(true);
    setError(null);

    try {
      // Use the new API endpoint instead of direct database access
      const response = await fetch(`/api/organization-members/update-profile`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: member.user_id,
          profileData: editData
        }),
      });

      if (!response.ok) {
        let errorMsg = "Failed to update profile.";
        try {
            const errorData = await response.json();
            errorMsg = errorData.error || errorMsg;
        } catch {
             // Ignore if response body is not JSON
        }
        throw new Error(errorMsg);
      }

      const { data: updatedFields } = await response.json();

      // Update local member state with the saved data
      setMember((prev) => {
        if (!prev) return null;
        // Create a new personal_info object merging previous and updated fields
        const newPersonalInfo: UserPersonalInfo = {
          ...(prev.personal_info || {}),
          ...updatedFields,
        };
        return {
          ...prev,
          personal_info: newPersonalInfo as UserPersonalInfo,
        };
      });

      toastMessages.success("Profile updated successfully");
      setIsEditing(false);
      onProfileUpdate?.();

    } catch (err: unknown) {
      console.error("Error updating profile:", err);
      const message = err instanceof Error ? err.message : "An unexpected error occurred.";
      setError(message);
      toastMessages.error(message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
      // Reset editData to the original member data
      setEditData((member?.personal_info as Partial<EditablePersonalInfo>) || {});
      setIsEditing(false);
      setError(null); // Clear any previous errors
  };

  const renderField = (
      label: string,
      fieldName: keyof EditablePersonalInfo, // fieldName is strongly typed here
      type: string = "text"
  ) => {
      // FIX: Use optional chaining for safety when accessing potentially null member/personal_info
      const currentValue = member?.personal_info?.[fieldName];
      const editValue = editData[fieldName];

      // Handle display formatting for null/undefined or dates
      let displayValue: string | number = "—";
      if (currentValue !== null && currentValue !== undefined) {
         if (type === 'date' && typeof currentValue === 'string' && currentValue) {
            try {
                 // Attempt to format date, fallback to original string if invalid
                 displayValue = format(new Date(currentValue), 'PP');
            } catch {
                 displayValue = currentValue; // Show original string if formatting fails
            }
         } else {
            displayValue = String(currentValue);
         }
      }


      return (
          <div className="mb-3">
               {/* FIX: Cast fieldName to string for htmlFor */}
              <Label htmlFor={fieldName as string} className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {label}
              </Label>
              {isEditing && canEditPersonalInfo ? (
                   // FIX: Cast fieldName to string for id
                  <Input
                      id={fieldName as string}
                      type={type}
                      // Ensure value is string or number for input, default to empty string
                      value={editValue ?? ""}
                      onChange={(e) => handleInputChange(fieldName, e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      disabled={isSaving}
                      // Add specific input attributes like step="1" for number if needed
                      step={type === 'number' ? '1' : undefined}
                  />
              ) : (
                  <p className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                      {displayValue}
                  </p>
              )}
          </div>
      );
  };


  if (!member) return null; // Don't render if no member data

  return (
    <Dialog open={isOpen} onOpenChange={onCloseAction}>
      <DialogContent className="sm:max-w-[700px] max-h-[85vh] flex flex-col">
        <DialogHeader>
           {/* FIX: Escaped apostrophe */}
          <DialogTitle className="text-[#0A2C35] dark:text-gray-100">
            {member.user_full_name}&apos;s Profile
          </DialogTitle>
          <DialogDescription>
             {/* FIX: Escaped apostrophes */}
            Details for {member.user_full_name}.
             {!isLoadingRole && canEditPersonalInfo && !isEditing && <span className="text-sm block mt-1"> Click &apos;Edit Profile&apos; to make changes.</span>}
             {isEditing && <span className="text-sm block mt-1 text-blue-600 dark:text-blue-400"> You are in edit mode.</span>}
             {isLoadingRole && <span className="text-sm block mt-1">Checking permissions...</span>}
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable Content Area */}
        <div className="flex-grow overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
            <div className="grid gap-6 py-4">
                {/* Avatar and Basic Info */}
                <div className="flex flex-col sm:flex-row items-center gap-4">
                    <Avatar className="h-20 w-20 rounded-md">
                    <AvatarImage
                        src={member.avatar_url || undefined}
                        alt={member.user_full_name}
                    />
                    <AvatarFallback>{member.user_full_name?.charAt(0).toUpperCase() || '?'}</AvatarFallback>
                    </Avatar>
                    <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm flex-grow">
                        <div><span className="font-medium">Name:</span> {member.user_full_name}</div>
                        <div><span className="font-medium">User ID:</span> <span className="font-mono text-xs">{member.user_id}</span></div>
                        <div><span className="font-medium">Role:</span> {member.role_name}</div>
                        <div><span className="font-medium">Status:</span> <Badge className={`${getStatusColor(member.org_member_is_active)}`} variant="secondary">{member.org_member_is_active ? "Active" : "Inactive"}</Badge></div>
                        <div><span className="font-medium">Organization:</span> {member.organization?.org_name}</div>
                        <div><span className="font-medium">Default Org:</span> {member.is_default_org ? "Yes" : "No"}</div>
                    </div>
                </div>

                {/* Personal Information Section */}
                <div className="space-y-4">
                    <h3 className="font-semibold text-lg text-[#0A2C35] dark:text-gray-100 border-b pb-1">
                        Personal Information
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
                        {renderField("First Name", "first_name")}
                        {renderField("Middle Name", "middle_name")}
                        {renderField("Last Name", "last_name")}
                        {renderField("Date of Birth", "dob", "date")}
                        {renderField("Nationality", "nationality")}
                        {renderField("Sex", "sex")}
                        {renderField("Height (cm)", "height_cm", "number")}
                    </div>
                </div>

                {/* Passport Information Section */}
                 <div className="space-y-4">
                    <h3 className="font-semibold text-lg text-[#0A2C35] dark:text-gray-100 border-b pb-1">
                        Passport Information
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
                        {renderField("Passport ID", "passport_doc_id")}
                        {renderField("Issuing Country", "passport_country")}
                        {renderField("Issue Date", "passport_date_issue", "date")}
                        {renderField("Expiry Date", "passport_date_expiry", "date")}
                    </div>
                </div>

                {/* Metadata Section */}
                <div className="space-y-4">
                    <h3 className="font-semibold text-lg text-[#0A2C35] dark:text-gray-100 border-b pb-1">
                        Account Information
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-sm">
                         <div>
                           <span className="font-medium">Joined Org:</span> 
                           {member.created_at ? format(new Date(member.created_at), "PPpp") : 'N/A'}
                         </div>
                         <div>
                           <span className="font-medium">Last Updated:</span> 
                           {member.updated_at ? format(new Date(member.updated_at), "PPpp") : 'N/A'}
                         </div>
                         {member.updated_by_name && (
                             <div><span className="font-medium">Updated by:</span> {member.updated_by_name}</div>
                         )}
                    </div>
                </div>

                 {/* Error Display */}
                {error && (
                    <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        <strong>Error:</strong> {error}
                    </div>
                )}
            </div>
        </div>

        {/* Dialog Footer with Actions */}
        <DialogFooter className="pt-4 border-t mt-auto">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancelEdit} disabled={isSaving}>Cancel</Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Save Changes
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={onCloseAction}>Close</Button>
              {!isLoadingRole && canEditPersonalInfo && (
                <Button onClick={() => setIsEditing(true)}>Edit Profile</Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}