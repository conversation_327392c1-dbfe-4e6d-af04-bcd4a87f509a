# Organization Invitation System - REMOVED

## Status: COMPLETELY REMOVED

The old code-based organization invitation system has been completely removed from the codebase. This document is kept for historical reference only.

## What Was Removed

## Database Schema

### Primary Table: `organization_invites`

Based on the codebase analysis, the `organization_invites` table has the following structure:

```sql
-- Inferred schema from usage patterns
CREATE TABLE organization_invites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invite_code TEXT UNIQUE NOT NULL,
  org_id UUID NOT NULL REFERENCES organizations(id),
  role_id INTEGER NOT NULL REFERENCES roles(role_id),
  created_by UUID NOT NULL REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  used_by UUID REFERENCES profiles(id),
  used_at TIMESTAMP WITH TIME ZONE
);
```

### Related Tables
- **organizations**: Target organization for the invite
- **roles**: Role to be assigned to the invited user
- **profiles**: Creator and user of the invite
- **organization_members**: Target table for successful invitations

### Database Functions

#### `process_invite_code(p_invite_code text) → jsonb`
- **Location**: `supabase/migrations/20240601000040_fix_process_invite_code.sql`
- **Purpose**: Atomically processes invite acceptance
- **Operations**:
  1. Validates invite code (active, not expired, not used)
  2. Adds user to `organization_members` table
  3. Marks invite as used in `organization_invites`
- **Returns**: JSON object with success status and details

#### `has_valid_invite(org_id_param uuid) → boolean`
- **Purpose**: Checks if current user has valid invitation to organization
- **Used in**: Invite validation flows

#### `generate_invite_code() → text`
- **Purpose**: Generates random invite codes
- **Used in**: `set_invite_code` trigger (marked as deprecated)

### Database Triggers

#### `set_invite_code()`
- **Status**: Marked as deprecated in documentation
- **Purpose**: Auto-generates invite codes on insert
- **Note**: Documentation indicates move to email-based invitations planned

## Application Architecture

### Server Actions

#### `src/app/actions/process-invite.ts`
- **Function**: `processInvite(inviteCode: string)`
- **Purpose**: Main server action for processing invite acceptance
- **Flow**:
  1. Validates user authentication
  2. Checks invite existence and validity
  3. Verifies user isn't already a member
  4. Adds user to organization
  5. Marks invite as used
  6. Handles default organization logic
  7. Refreshes user session and revalidates paths
- **Returns**: `ProcessInviteResponse` interface

### Routes and Pages

#### Organization-Specific Invite Management
**Route**: `/dashboard/admin/organization/[orgId]/invites`
**File**: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx`
- **Access**: Organization admins and above
- **Features**:
  - Lists organization-specific invites
  - Create new invites for the organization
  - View invite status and usage
  - Copy invite codes
  - Delete unused invites

#### Global Invite Management (SuperAdmin)
**Route**: `/dashboard/admin/invites`
**File**: `src/app/dashboard/admin/invites/page.tsx`
- **Access**: SuperAdmin only
- **Features**:
  - View all invites across all organizations
  - Create invites for any organization
  - Global invite management and oversight

### UI Components

#### Core Components

##### `CreateInviteButton`
**File**: `src/components/organization/create-invite-button.tsx`
- **Purpose**: Simple invite creation for specific organization
- **Features**:
  - Role selection
  - Automatic expiry (7 days default)
  - Invite code display
  - Copy to clipboard functionality

##### `CreateInviteDialog`
**File**: `src/components/organization/create-invite-dialog.tsx`
- **Purpose**: Advanced invite creation with organization selection
- **Features**:
  - Organization dropdown (for SuperAdmins)
  - Role selection
  - Custom expiry period
  - Form validation with Zod schema
  - Success feedback

##### `InvitesList`
**File**: `src/components/organization/invites-list.tsx`
- **Purpose**: Display organization-specific invites
- **Features**:
  - Tabular display of invites
  - Status badges (Active, Used, Expired)
  - Copy invite codes
  - Delete unused invites
  - Creator and usage information

##### `AllInvitesList`
**File**: `src/components/organization/all-invites-list.tsx`
- **Purpose**: Global invite management for SuperAdmins
- **Features**:
  - Cross-organization invite display
  - Organization name column
  - Deactivate invites
  - Global invite oversight

##### `JoinOrganizationModal`
**File**: `src/components/organization/join-organization-modal.tsx`
- **Purpose**: User interface for accepting invitations
- **Features**:
  - Invite code input
  - Validation and error handling
  - Success feedback
  - Automatic redirection to organization

##### `InviteCodeDisplay`
**File**: `src/components/organization/invite-code-display.tsx`
- **Purpose**: Display created invite codes
- **Features**:
  - Formatted invite code display
  - Copy to clipboard
  - Expiry date display
  - Role information
  - Usage instructions

### Type Definitions

#### Core Types

##### `BaseInvite`
**File**: `src/types/app/dashboard/admin/invites/BaseInvite.ts`
```typescript
interface BaseInvite {
  id: string
  invite_code: string
  created_at: string
  expires_at: string
  is_active: boolean
  role_id: number
  org_id: string
  created_by: string
  used_by: string | null
  used_at: string | null
}
```

##### `Invite` (Component Type)
**File**: `src/types/components/organization/Invite.ts`
- Extends BaseInvite with joined data
- Includes organization, role, and profile information
- Used in UI components

##### `Invite` (Admin Type)
**File**: `src/types/app/dashboard/admin/invites/Invite.ts`
- Different structure for admin interfaces
- Includes related entity data

##### `ProcessInviteResponse`
**File**: `src/types/app/actions/ProcessInviteResponse.ts`
```typescript
interface ProcessInviteResponse {
  success: boolean
  message: string
  organizationId?: string
  debug?: unknown
}
```

#### Component Props Types
- `AllInvitesListProps`: Props for global invite list
- `CreateInviteButtonProps`: Props for invite creation button
- `InviteCodeDisplayProps`: Props for invite code display
- `InviteData`: Basic invite data structure
- `InvitesListProps`: Props for organization invite list
- `JoinOrganizationModalProps`: Props for join modal

## Security and Permissions

### Row Level Security (RLS)
The system relies on RLS policies on `organization_members` table that reference `organization_invites`:

```sql
-- Users can join with valid invite
CREATE POLICY "Users can join with valid invite"
ON public.organization_members
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = (SELECT auth.uid()) AND
  EXISTS (
    SELECT 1
    FROM public.organization_invites
    WHERE org_id = organization_members.org_id
    AND is_active = true
    AND expires_at > now()
    AND used_by IS NULL
  )
);
```

### RBAC Integration
- **Invite Creation**: Requires organization admin privileges or higher
- **Global Management**: SuperAdmin only for cross-organization access
- **Invite Usage**: Any authenticated user can use valid invite codes

## Current Limitations and Issues

### 1. Code-Based System Limitations
- **Security**: Invite codes can be shared and used by unintended recipients
- **Tracking**: No way to track intended recipient vs actual user
- **Revocation**: Limited ability to revoke specific invitations to specific users

### 2. User Experience Issues
- **Discovery**: Users must receive invite codes through external channels
- **Validation**: No pre-validation of user eligibility
- **Feedback**: Limited feedback on invitation status to inviter

### 3. Administrative Overhead
- **Manual Process**: Requires manual code sharing
- **Tracking**: Difficult to track invitation campaigns
- **Expiry Management**: Manual management of expiry periods

### 4. Technical Debt
- **Dual Type Systems**: Multiple `Invite` interfaces for different contexts
- **Deprecated Features**: `set_invite_code` trigger marked for removal
- **Code Generation**: Client-side random code generation (security concern)

## Integration Points

### Authentication System
- Integrates with Supabase Auth for user validation
- Updates user session after successful invitation acceptance
- Handles organization context switching

### Organization Management
- Tightly coupled with organization membership system
- Affects default organization selection
- Integrates with organization switching functionality

### Role-Based Access Control
- Respects role hierarchy for invite creation
- Assigns roles based on invite configuration
- Integrates with existing RBAC system

## Migration Considerations

### Database Dependencies
- `organization_invites` table is referenced in RLS policies
- Database function `process_invite_code` would need replacement
- Triggers and indexes would need cleanup

### Application Dependencies
- Multiple UI components depend on current data structure
- Server actions would need complete rewrite
- Type definitions would need updates

### User Impact
- Existing unused invite codes would become invalid
- Users familiar with current flow would need retraining
- Administrative workflows would change significantly

## Recommendations for Replacement

### 1. Email-Based Invitations
- Send invitations directly to email addresses
- Include invitation links with tokens
- Track invitation status per recipient

### 2. Enhanced Security
- Implement invitation tokens with stronger security
- Add invitation expiry and usage tracking
- Include recipient validation

### 3. Improved User Experience
- Direct email delivery with clear instructions
- Better feedback for both inviter and invitee
- Integration with notification systems

### 4. Administrative Features
- Bulk invitation capabilities
- Invitation campaign tracking
- Better analytics and reporting

This comprehensive overview provides the foundation for planning the complete replacement of the current invitation system with a more robust, secure, and user-friendly solution.