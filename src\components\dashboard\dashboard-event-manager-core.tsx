'use client'

import { useEffect, useRef, useLayoutEffect, useMemo, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import debounce from 'lodash.debounce'
import { useServerContextRefresher } from '@/hooks/use-server-context-refresher'
import { useOrganizationsList } from '@/hooks/use-organizations-list'
import {
  subscribeToDashboardChannels,
  unsubscribeAllDashboardChannels,
  areChannelsBroken,
  hasActiveChannels,
  getSubscriptionState,
} from '@/lib/eventBus/channels/manager'
import { evaluateUnifiedAccess } from '@/lib/navigation/route-access-evaluator'
import { useAuthContextStore } from '@/stores/useAuthContextStore'
import { useRouter } from 'next/navigation'
import { emitter } from '@/lib/eventBus/emitter'

const CONFIG = {
  SINGLETON_DOM_ID: 'dashboard-event-manager-core-instance',
  GLOBAL_INDICATOR_ID: 'global-event-system-indicator',
  DEBUG: process.env.NODE_ENV === 'development',
  DEBOUNCE_DELAY: 300,
  VISIBILITY_TIMEOUT: 5000,
  IDLE_CALLBACK_TIMEOUT: 2000,
  FALLBACK_DELAY: 500,
  HIDDEN_THRESHOLD: 30000, // 30 seconds
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
}

const INSTANCE_ID = Math.random().toString(36).substring(2, 15)
let hasCreatedGlobalMarker = false

if (typeof window !== 'undefined' && !hasCreatedGlobalMarker) {
  const globalMarker =
    document.getElementById(CONFIG.GLOBAL_INDICATOR_ID) ||
    (() => {
      const el = document.createElement('div')
      el.id = CONFIG.GLOBAL_INDICATOR_ID
      el.setAttribute('data-status', 'initializing')
      el.style.display = 'none'
      if (document.body) {
        document.body.appendChild(el)
      } else {
        document.addEventListener('DOMContentLoaded', () => document.body.appendChild(el))
      }
      hasCreatedGlobalMarker = true
      return el
    })()
  globalMarker.setAttribute('data-dashboard-event-manager', 'initializing')
}

function log(
  level: 'debug'|'info'|'warn'|'error',
  message: string,
  ...args: unknown[]
) {
  const currentConfig = (typeof CONFIG !== 'undefined') ? CONFIG : { DEBUG: false }
  if (level === 'debug' && !currentConfig.DEBUG) return
  const timestamp = new Date().toISOString()
  const prefix = `[DEMCore:${INSTANCE_ID.slice(-4)}]`
  switch(level) {
    case 'debug': console.debug(`${timestamp} ${prefix} ${message}`, ...args); break
    case 'info': console.log(`${timestamp} ${prefix} ${message}`, ...args); break
    case 'warn': console.warn(`${timestamp} ${prefix} ${message}`, ...args); break
    case 'error': console.error(`${timestamp} ${prefix} ${message}`, ...args); break
  }
}

// Error handling utilities
function handleError(error: unknown, context: string): void {
  const errorMessage = error instanceof Error ? error.message : String(error)
  const errorStack = error instanceof Error ? error.stack : undefined
  log('error', `Error in ${context}: ${errorMessage}`)
  if (errorStack && CONFIG.DEBUG) {
    log('error', `Stack trace: ${errorStack}`)
  }
}

async function withRetry<T>(
  operation: () => Promise<T>,
  context: string,
  maxAttempts: number = CONFIG.MAX_RETRY_ATTEMPTS
): Promise<T | null> {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      if (attempt === maxAttempts) {
        handleError(error, `${context} (final attempt ${attempt}/${maxAttempts})`)
        return null
      }
      log('warn', `${context} failed (attempt ${attempt}/${maxAttempts}), retrying...`)
      await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY * attempt))
    }
  }
  return null
}

export function DashboardEventManagerCore() {
  const isSingletonRef = useRef(false)
  const { forceRefreshContext } = useServerContextRefresher()
  const { refreshOrganizations } = useOrganizationsList()
  const lastVisibleTimestampRef = useRef<number | null>(null)
  const isInitialLoadRef = useRef(true)
  const isFullyInitializedRef = useRef(false)
  const timeoutRefs = useRef<Set<NodeJS.Timeout>>(new Set())
  const hiddenTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Add router for permission evaluation redirects
  const router = useRouter()

  const supabase = useMemo(() => createClient(), [])

  // Enhanced debounced functions with error handling
  const debouncedRefreshContext = useRef(
    debounce(async () => {
      try {
        log('debug', 'Executing debounced context refresh via forceRefreshContext()')
        await forceRefreshContext()
      } catch (error) {
        handleError(error, 'debounced context refresh')
      }
    }, CONFIG.DEBOUNCE_DELAY)
  ).current

  const debouncedRefreshOrgs = useRef(
    debounce(async () => {
      try {
        log('debug', 'Executing debounced organizations refresh')
        await refreshOrganizations()
      } catch (error) {
        handleError(error, 'debounced organizations refresh')
      }
    }, CONFIG.DEBOUNCE_DELAY)
  ).current

  // Cleanup function for timeouts
  const addTimeout = useCallback((timeout: NodeJS.Timeout) => {
    timeoutRefs.current.add(timeout)
  }, [])

  const clearAllTimeouts = useCallback(() => {
    timeoutRefs.current.forEach(timeout => clearTimeout(timeout))
    timeoutRefs.current.clear()
  }, [])

  // Enhanced cleanup effect
  useEffect(() => {
    return () => {
      log('debug', 'Cleaning up DashboardEventManagerCore resources')
      debouncedRefreshContext.cancel()
      debouncedRefreshOrgs.cancel()
      clearAllTimeouts()

      // Cancel any pending hidden timeout
      if (hiddenTimeoutRef.current) {
        clearTimeout(hiddenTimeoutRef.current)
        hiddenTimeoutRef.current = null
      }
    }
  }, [debouncedRefreshContext, debouncedRefreshOrgs, clearAllTimeouts])

  // ---- Singleton marker logic (unchanged) ----
  const checkSingleton = (): boolean => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return false
    const existingMarker = document.getElementById(CONFIG.SINGLETON_DOM_ID)
    if (existingMarker && existingMarker.getAttribute('data-instance-id') !== INSTANCE_ID) {
      log('debug', `Another singleton instance (${existingMarker.getAttribute('data-instance-id')}) is active.`)
      return false
    }
    if (!existingMarker) {
      const newMarker = document.createElement('div')
      newMarker.id = CONFIG.SINGLETON_DOM_ID
      newMarker.setAttribute('data-instance-id', INSTANCE_ID)
      newMarker.style.display = 'none'
      document.body.appendChild(newMarker)
      log('debug', `This instance (${INSTANCE_ID}) created the singleton marker.`)
      return true
    }
    return true
  }

  useLayoutEffect(() => {
    const isCurrentInstanceSingleton = checkSingleton()
    isSingletonRef.current = isCurrentInstanceSingleton
    const globalMarker = document.getElementById(CONFIG.GLOBAL_INDICATOR_ID)

    if (isCurrentInstanceSingleton) {
      log('debug', `Instance ${INSTANCE_ID} is now the singleton.`)
      if (globalMarker) {
        globalMarker.setAttribute('data-status', 'active')
        globalMarker.setAttribute('data-instance-id', INSTANCE_ID)
      }
      if (isInitialLoadRef.current) {
         performInitialSetup()
      }
    } else {
      log('debug', `Instance ${INSTANCE_ID} is NOT the singleton. Will not manage events.`)
      if (globalMarker && globalMarker.getAttribute('data-instance-id') !== INSTANCE_ID) {
        // Another instance is active
      } else if (globalMarker && globalMarker.getAttribute('data-instance-id') === INSTANCE_ID) {
        globalMarker.setAttribute('data-status', 'inactive')
        globalMarker.removeAttribute('data-instance-id')
        // DO NOT unsubscribe channels here!
      }
    }

    return () => {
      if (isSingletonRef.current) {
        log('debug', `Singleton instance ${INSTANCE_ID} unmounting. Cleaning up singleton markers.`)
        if (globalMarker && globalMarker.getAttribute('data-instance-id') === INSTANCE_ID) {
          globalMarker.setAttribute('data-status', 'inactive')
          globalMarker.removeAttribute('data-instance-id')
          const singletonMarker = document.getElementById(CONFIG.SINGLETON_DOM_ID)
          if (singletonMarker && singletonMarker.getAttribute('data-instance-id') === INSTANCE_ID) {
            singletonMarker.remove()
          }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [supabase])

  const performInitialSetup = async () => {
    log('debug', 'Performing initial setup for singleton instance...')
    isInitialLoadRef.current = false

    // Load initial organizations data with retry logic
    const orgResult = await withRetry(
      () => refreshOrganizations(),
      'initial organizations refresh'
    )

    if (orgResult !== null) {
      log('debug', 'Initial organizations data loaded successfully')
    } else {
      log('warn', 'Failed to load initial organizations data after retries')
    }

    // --- Subscribe channels only via channel manager ---
    const subscribeWhenReady = () => {
      const setupChannels = async () => {
        try {
          log('debug', 'Establishing channel subscriptions...')
          subscribeToDashboardChannels(supabase)
          isFullyInitializedRef.current = true
          log('debug', 'Initial setup complete.')
        } catch (error) {
          handleError(error, 'channel subscription setup')
          // Retry channel setup after a delay
          const retryTimeout = setTimeout(() => {
            log('debug', 'Retrying channel subscription setup...')
            setupChannels()
          }, CONFIG.RETRY_DELAY)
          addTimeout(retryTimeout)
        }
      }

      if (typeof window.requestIdleCallback === 'function') {
        window.requestIdleCallback(() => {
          log('debug', 'DOM ready and browser idle, establishing channel subscriptions now')
          setupChannels()
        }, { timeout: CONFIG.IDLE_CALLBACK_TIMEOUT })
      } else {
        const fallbackTimeout = setTimeout(() => {
          log('debug', 'DOM ready, establishing channel subscriptions now')
          setupChannels()
        }, CONFIG.FALLBACK_DELAY)
        addTimeout(fallbackTimeout)
      }
    }

    if (document.readyState === 'complete') {
      subscribeWhenReady()
    } else {
      log('debug', 'DOM not yet ready, delaying channel subscriptions')
      const loadHandler = () => subscribeWhenReady()
      window.addEventListener('load', loadHandler, { once: true })

      // Cleanup listener if component unmounts before load
      const cleanupTimeout = setTimeout(() => {
        window.removeEventListener('load', loadHandler)
      }, 10000) // 10 second timeout
      addTimeout(cleanupTimeout)
    }
  }

  // ---- Enhanced visibility change handler with error handling ----
  const onVisibilityChange = useCallback(() => {
    try {
      if (!isSingletonRef.current || !isFullyInitializedRef.current) {
        log('debug', 'Visibility change ignored: Not singleton or not fully initialized.')
        return
      }

      const now = Date.now()
      if (document.visibilityState === 'visible') {
        log('debug', 'Tab became visible.')

        // Cancel any pending hidden timeout since we're now visible
        if (hiddenTimeoutRef.current) {
          clearTimeout(hiddenTimeoutRef.current)
          hiddenTimeoutRef.current = null
          log('debug', 'Cancelled hidden timeout due to tab becoming visible')
        }

        // Block all dashboard rendering until route evaluation completes
        sessionStorage.setItem('dashboardRenderBlocked', 'true')
        log('debug', 'Blocked dashboard rendering until route evaluation completes')

        // SAFEGUARD: Set a backup timeout to clear the flag in case evaluation fails
        const safeguardTimeout = setTimeout(() => {
          log('warn', 'SAFEGUARD: Clearing render block flag after 10 seconds - evaluation may have failed')
          sessionStorage.removeItem('dashboardRenderBlocked')
        }, 10000) // 10 second backup timeout

        // Refresh context when tab becomes visible, then evaluate permissions with fresh data
        try {
          // Create a promise-based wrapper for the context refresh
          const refreshContextAndEvaluate = async () => {
            try {
              // Force refresh and wait for completion
              await forceRefreshContext()

              // Now evaluate with fresh data from the store
              const freshAuthState = useAuthContextStore.getState()
              const { userId: freshUserId, orgId: freshOrgId, roleId: freshRoleId,
                      isUserActiveInOrg: freshIsUserActive, isOrgActive: freshIsOrgActive,
                      isSuperAdmin: freshIsSuperAdmin } = freshAuthState

              log('debug', 'Using fresh auth data for permission evaluation:', {
                userId: freshUserId,
                orgId: freshOrgId,
                roleId: freshRoleId,
                isUserActive: freshIsUserActive,
                isOrgActive: freshIsOrgActive,
                isSuperAdmin: freshIsSuperAdmin
              })

              // Check if a recent redirect already happened (within last 5 seconds)
              // This prevents duplicate evaluations when DashboardScenarioManager already handled it
              const currentPath = window.location.pathname
              const recentRedirectKey = `recentRedirect_${currentPath}`
              const recentRedirectTime = sessionStorage.getItem(recentRedirectKey)

              if (recentRedirectTime) {
                const timeSinceRedirect = Date.now() - parseInt(recentRedirectTime)
                if (timeSinceRedirect < 5000) { // 5 seconds
                  log('debug', `Skipping tab reactivation evaluation - recent redirect ${timeSinceRedirect}ms ago`)
                  return false
                }
              }

              if (typeof window !== 'undefined' && freshUserId && freshOrgId &&
                  freshRoleId !== null && freshRoleId !== undefined) {
                const currentPath = window.location.pathname
                const redirectKey = `dashboardRedirectInProgress_${currentPath}`

                // Use unified evaluation to handle all scenarios (account disabled, org disabled, route permissions)
                const unifiedResult = evaluateUnifiedAccess({
                  currentPath,
                  userRoleId: freshRoleId,
                  isUserActiveInOrg: freshIsUserActive || false,
                  isOrgActive: freshIsOrgActive || false,
                  isSuperAdmin: freshIsSuperAdmin || false
                })

                log('debug', 'Tab reactivation unified access evaluation (with fresh data):', {
                  currentPath,
                  userRole: freshRoleId,
                  canAccess: unifiedResult.canAccess,
                  redirectTo: unifiedResult.redirectTo,
                  priority: unifiedResult.priority,
                  reason: unifiedResult.reason
                })

                // If user needs to be redirected (any priority except 'allowed')
                if (!unifiedResult.canAccess || unifiedResult.priority !== 'allowed') {
                  log('info', `User needs redirect after tab reactivation - redirecting to ${unifiedResult.redirectTo} (priority: ${unifiedResult.priority})`)

                  // Clear safeguard timeout since evaluation completed
                  clearTimeout(safeguardTimeout)

                  // Unblock dashboard rendering since evaluation is complete (redirect will happen)
                  sessionStorage.removeItem('dashboardRenderBlocked')
                  log('debug', 'Unblocked dashboard rendering - redirect required')

                  // Set redirect flag to prevent unnecessary processing during redirect
                  sessionStorage.setItem(redirectKey, 'true')
                  const redirectFlagTimeout = setTimeout(() => {
                    sessionStorage.removeItem(redirectKey)
                  }, 1000)
                  addTimeout(redirectFlagTimeout)

                  router.push(unifiedResult.redirectTo)
                  return true // Indicate redirect happened
                } else {
                  log('debug', 'User still has access to current page after tab reactivation')

                  // Clear safeguard timeout since evaluation completed
                  clearTimeout(safeguardTimeout)

                  // Unblock dashboard rendering since user is authorized
                  sessionStorage.removeItem('dashboardRenderBlocked')
                  sessionStorage.removeItem(redirectKey)
                  log('debug', 'Unblocked dashboard rendering - user is authorized')

                  // Emit tab reactivation event for data refresh ONLY if user is authorized
                  if (inactiveTime > 5000) { // 5 seconds minimum to avoid noise
                    log('debug', `User authorized - emitting tab reactivation event - tab was hidden for ${inactiveTime}ms`)
                    emitter.emit('tab:reactivated', {
                      hiddenDuration: inactiveTime,
                      timestamp: now
                    })
                  }

                  return false // Indicate no redirect
                }
              }
              return false // No evaluation needed
            } catch (error) {
              handleError(error, 'context refresh and permission evaluation')
              return false
            }
          }

          // Execute the refresh and evaluation
          refreshContextAndEvaluate().then((wasRedirected) => {
            // Only proceed with channel resubscription if user wasn't redirected
            if (!wasRedirected) {
              log('debug', 'Permission evaluation complete - proceeding with channel management')
            }
          })
        } catch (error) {
          handleError(error, 'visibility change context refresh and permission evaluation')
        }

        const inactiveTime = lastVisibleTimestampRef.current ? (now - lastVisibleTimestampRef.current) : 0
        const wasHiddenLongEnough = inactiveTime > CONFIG.HIDDEN_THRESHOLD

        // Channel (re)subscription with error handling:
        try {
          const subscriptionState = getSubscriptionState();
          log('debug', `Subscription state check:`, subscriptionState);

          if (wasHiddenLongEnough && areChannelsBroken()) {
            log('debug', `Detected broken channels and tab was hidden for ${inactiveTime}ms - re-subscribing`)
            unsubscribeAllDashboardChannels()
            subscribeToDashboardChannels(supabase)
          } else if (!hasActiveChannels()) {
            log('debug', 'No active channels detected - subscribing')
            subscribeToDashboardChannels(supabase)
          } else if (areChannelsBroken()) {
            log('debug', 'Channels appear broken but tab was not hidden long - attempting to subscribe without unsubscribe')
            subscribeToDashboardChannels(supabase)
          } else {
            log('debug', `All channels appear healthy (${subscriptionState.channelCount} active) - skipping re-subscribe`)
          }
        } catch (error) {
          handleError(error, 'channel resubscription on visibility change')
        }
      } else if (document.visibilityState === 'hidden') {
        log('debug', 'Tab became hidden.')
        lastVisibleTimestampRef.current = now

        // Cancel any existing hidden timeout to prevent race conditions
        if (hiddenTimeoutRef.current) {
          clearTimeout(hiddenTimeoutRef.current)
          hiddenTimeoutRef.current = null
          log('debug', 'Cancelled previous hidden timeout')
        }

        // Use managed timeout for cleanup
        const hiddenTimeout = setTimeout(() => {
          try {
            if (document.visibilityState === 'hidden' && isSingletonRef.current) {
              log('debug', 'Tab still hidden after delay - unsubscribing channels to conserve resources')
              unsubscribeAllDashboardChannels()
            }
            hiddenTimeoutRef.current = null
          } catch (error) {
            handleError(error, 'channel cleanup on hidden timeout')
          }
        }, CONFIG.VISIBILITY_TIMEOUT)

        hiddenTimeoutRef.current = hiddenTimeout
        addTimeout(hiddenTimeout)
      }
    } catch (error) {
      handleError(error, 'visibility change handler')
    }
  }, [supabase, debouncedRefreshContext, addTimeout])


  // Enhanced visibility change effect with error handling
  useEffect(() => {
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      try {
        log('debug', 'Setting up visibility change listener')
        document.addEventListener('visibilitychange', onVisibilityChange)

        // Initialize timestamp if currently visible
        if (document.visibilityState === 'visible') {
          lastVisibleTimestampRef.current = Date.now()
          log('debug', 'Initial visibility state: visible')
        } else {
          log('debug', 'Initial visibility state: hidden')
        }

        return () => {
          try {
            log('debug', 'Cleaning up visibility change listener')
            document.removeEventListener('visibilitychange', onVisibilityChange)
          } catch (error) {
            handleError(error, 'visibility change listener cleanup')
          }
        }
      } catch (error) {
        handleError(error, 'visibility change listener setup')
      }
    }
  }, [onVisibilityChange])

  return null
}
