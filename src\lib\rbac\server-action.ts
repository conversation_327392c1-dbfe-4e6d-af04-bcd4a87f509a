import { checkRbacPermission } from './permissions-server'
import { ActionConfig, ExtractRbacConditions } from '@/types/lib/rbac'

/**
 * Higher-order function to protect server actions with RBAC permissions
 * @param action The server action function to protect
 * @param config RBAC configuration for authorization checks
 * @returns Protected action function
 */
export function withPermissionAction<TArgs extends unknown[], TReturn>(
  action: (...args: TArgs) => Promise<TReturn>,
  config: ActionConfig
) {
  return async (...args: TArgs): Promise<TReturn> => {
    try {
      // Extract RBAC conditions from config
      const rbacConditions = config as ExtractRbacConditions<ActionConfig>
      
      // Check permission using RBAC system
      const hasAccess = await checkRbacPermission(rbacConditions, { silentFail: true })

      if (!hasAccess) {
        throw new Error('Unauthorized')
      }

      return await action(...args)
    } catch (error) {
      if (config.onError && error instanceof Error) {
        config.onError(error)
      }
      throw error
    }
  }
}

// Example usage:
// interface CreateUserData {
//   name: string
//   email: string
// }
//
// const createUser = withPermissionAction(
//   async (userData: CreateUserData) => {
//     // Create user logic
//     return { success: true }
//   },
//   { 
//     crMinRole: "orgAdmin", // Require org admin role for create operations
//     // Alternative: crRoles: ["superAdmin", "supportAdmin", "orgAdmin"]
//   }
// )