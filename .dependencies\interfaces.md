# 📐 Interface Usage

## ActionConfig
- Declared in: `src/types/lib/rbac/index.ts` (Line 278)
- Used in: `src/lib/rbac/server-action.ts` (Line 12)
- Used in: `src/lib/rbac/server-action.ts` (Line 17)

## AllInvitesListProps
- Declared in: `src/types/components/organization/AllInvitesListProps.ts` (Line 3)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 27)

## AnyMemberChangeEvent
- Declared in: `src/lib/eventTypes.ts` (Line 50)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 26)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 47)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 63)
- Used in: `src/lib/eventBus/emitter.ts` (Line 38)
- Used in: `src/lib/eventBus/hooks/useAllMemberChanges.ts` (Line 11)

## ApiRefreshedAuthContext
- Declared in: `src/hooks/use-server-context-refresher.ts` (Line 7)
- Used in: `src/app/api/auth/refresh-context/route.ts` (Line 167)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 12)
- Used in: `src/components/providers/auth-context-provider.tsx` (Line 11)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 23)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 28)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 46)
- Used in: `src/hooks/use-server-context-refresher.ts` (Line 70)

## ApplicationForm
- Declared in: `src/types/handbook.ts` (Line 90)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 529)
- Used in: `src/types/handbook.ts` (Line 97)

## ApplicationForms
- Declared in: `src/types/handbook.ts` (Line 96)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 515)
- Used in: `src/types/handbook.ts` (Line 128)

## AuthCardProps
- Declared in: `src/types/components/auth/AuthCardProps.ts` (Line 3)
- Used in: `src/components/auth/auth-card.tsx` (Line 22)

## AuthContainerProps
- Declared in: `src/types/components/auth/AuthContainerProps.ts` (Line 3)
- Used in: `src/components/auth/auth-container.tsx` (Line 8)

## AuthContext
- Declared in: `src/types/lib/auth-context.ts` (Line 4)
- Used in: `src/lib/auth-context.ts` (Line 88)
- Used in: `src/lib/auth-context.ts` (Line 91)
- Used in: `src/lib/auth-context.ts` (Line 185)
- Used in: `src/lib/auth-context.ts` (Line 209)

## AuthContextActions
- Declared in: `src/stores/useAuthContextStore.ts` (Line 36)
- Used in: `src/stores/useAuthContextStore.ts` (Line 144)

## AuthContextState
- Declared in: `src/stores/useAuthContextStore.ts` (Line 10)
- Used in: `src/stores/useAuthContextStore.ts` (Line 6)
- Used in: `src/stores/useAuthContextStore.ts` (Line 37)
- Used in: `src/stores/useAuthContextStore.ts` (Line 61)
- Used in: `src/stores/useAuthContextStore.ts` (Line 144)

## AuthEvent
- Declared in: `src/lib/eventTypes.ts` (Line 112)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 69)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 87)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 149)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 89)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 155)
- Used in: `src/lib/eventBus/emitter.ts` (Line 31)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 18)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 31)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 47)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 60)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 76)

## AvailableRole
- Declared in: `src/types/hooks/AvailableRole.ts` (Line 1)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 66)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 156)

## BadgeProps
- Declared in: `src/types/components/ui/BadgeProps.ts` (Line 5)
- Used in: `src/components/ui/badge.tsx` (Line 26)

## BaseInvite
- Declared in: `src/types/app/dashboard/admin/invites/BaseInvite.ts` (Line 1)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 67)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 6)

## BottomRemarks
- Declared in: `src/types/handbook.ts` (Line 104)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 557)
- Used in: `src/types/handbook.ts` (Line 129)

## BreadcrumbsProps
- Declared in: `src/types/components/BreadcrumbsProps.ts` (Line 1)
- Used in: `src/components/Breadcrumbs.tsx` (Line 5)

## ButtonProps
- Declared in: `src/types/components/ui/ButtonProps.ts` (Line 5)
- Used in: `src/components/ui/button.tsx` (Line 37)
- Used in: `src/components/ui/pagination.tsx` (Line 40)

## ClientDashboardShellProps
- Declared in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 11)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 21)

## ComplaintChannel
- Declared in: `src/types/handbook.ts` (Line 76)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 444)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 448)
- Used in: `src/types/handbook.ts` (Line 87)

## ComplaintChannels
- Declared in: `src/types/handbook.ts` (Line 86)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 429)
- Used in: `src/types/handbook.ts` (Line 127)

## ComponentProps
- Declared in: `src/types/components/auth/ComponentProps.ts` (Line 1)
- Used in: `src/components/auth/with-role-check.tsx` (Line 8)

## CreateInviteButtonProps
- Declared in: `src/types/components/organization/CreateInviteButtonProps.ts` (Line 1)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 35)

## CreateInviteProcessedData
- Declared in: `src/components/organization/create-invite-dialog.tsx` (Line 55)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 116)

## CreateOrganizationData
- Declared in: `src/app/dashboard/developer/create-organization/actions.ts` (Line 7)
- Used in: `src/app/dashboard/developer/create-organization/actions.ts` (Line 21)

## Criteria
- Declared in: `src/types/handbook.ts` (Line 16)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 146)
- Used in: `src/types/handbook.ts` (Line 122)

## CriteriaItem
- Declared in: `src/types/handbook.ts` (Line 5)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 169)
- Used in: `src/types/handbook.ts` (Line 13)

## CriteriaItems
- Declared in: `src/types/handbook.ts` (Line 11)
- Used in: `src/types/handbook.ts` (Line 18)

## DashboardContextType
- Declared in: `src/types/components/providers/DashboardContextType.ts` (Line 4)
- *No usage found based on simple type reference matching within the project.*

## DashboardLayoutClientProps
- Declared in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 11)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 27)

## DashboardProviderProps
- Declared in: `src/types/components/providers/DashboardProviderProps.ts` (Line 5)
- Used in: `src/components/providers/dashboard-provider.tsx` (Line 22)

## DatabaseLabelCategory
- Declared in: `src/types/app/dashboard/admin/labels/DatabaseLabelCategory.ts` (Line 1)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 663)

## Fee
- Declared in: `src/types/handbook.ts` (Line 72)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 387)
- Used in: `src/types/handbook.ts` (Line 125)

## FeeItem
- Declared in: `src/types/handbook.ts` (Line 66)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 407)
- Used in: `src/types/handbook.ts` (Line 73)

## Flow
- Declared in: `src/types/flows.ts` (Line 27)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 42)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 115)
- Used in: `src/types/components/flows/FlowCardProps.ts` (Line 4)
- Used in: `src/types/components/flows/FlowCardProps.ts` (Line 6)

## FlowCardProps
- Declared in: `src/types/components/flows/FlowCardProps.ts` (Line 3)
- Used in: `src/components/flows/FlowCard.tsx` (Line 29)

## FlowStep
- Declared in: `src/types/components/flowsteps/FlowStep.ts` (Line 14)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 28)
- Used in: `src/types/components/flowsteps/FlowStepEditDialogProps.ts` (Line 4)
- Used in: `src/types/components/flowsteps/FlowStepEditDialogProps.ts` (Line 7)

## FlowStepCategory
- Declared in: `src/types/app/dashboard/admin/flowsteps/FlowStepCategory.ts` (Line 1)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 105)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 331)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 336)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 504)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 510)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 20)
- Used in: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts` (Line 7)
- Used in: `src/types/components/flowsteps/FlowStepCategoryEditDialogProps.ts` (Line 4)
- Used in: `src/types/components/flowsteps/FlowStepCategoryEditDialogProps.ts` (Line 7)

## FlowStepCategoryEditDialogProps
- Declared in: `src/types/components/flowsteps/FlowStepCategoryEditDialogProps.ts` (Line 3)
- Used in: `src/components/flowsteps/FlowStepCategoryEditDialog.tsx` (Line 19)

## FlowStepData
- Declared in: `src/types/app/dashboard/admin/flowsteps/FlowStepData.ts` (Line 14)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 104)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 107)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 333)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 588)
- Used in: `src/types/app/dashboard/admin/flowsteps/FlowStepData.ts` (Line 26)
- Used in: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts` (Line 6)
- Used in: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts` (Line 9)

## FlowStepEditDialogProps
- Declared in: `src/types/components/flowsteps/FlowStepEditDialogProps.ts` (Line 3)
- Used in: `src/components/flowsteps/FlowStepEditDialog.tsx` (Line 27)

## FormData
- Declared in: `src/types/app/dashboard/admin/organization/FormData.ts` (Line 1)
- Used in: `src/app/dashboard/developer/create-organization/actions.ts` (Line 104)
- Used in: `src/app/dashboard/developer/create-organization/create-organization-form.tsx` (Line 71)
- Used in: `src/app/dashboard/profile/page.tsx` (Line 31)
- Used in: `src/types/components/profile/ProfileFormProps.ts` (Line 8)

## Handbook
- Declared in: `src/types/handbook.ts` (Line 120)
- Used in: `src/types/app/dashboard/handbooks/HandbookSearchProps.ts` (Line 4)
- Used in: `src/types/handbook.ts` (Line 134)

## HandbookProps
- Declared in: `src/types/handbook.ts` (Line 133)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 78)

## HandbookSearchProps
- Declared in: `src/types/app/dashboard/handbooks/HandbookSearchProps.ts` (Line 3)
- Used in: `src/app/dashboard/handbooks/handbook-search.tsx` (Line 23)

## HandbookSelectorProps
- Declared in: `src/types/components/HandbookSelectorProps.ts` (Line 1)
- Used in: `src/components/ClientHandbookSelector.tsx` (Line 16)

## IconProps
- Declared in: `src/types/components/shared/IconProps.ts` (Line 1)
- Used in: `src/components/shared/Icon.tsx` (Line 5)

## Invite
- Declared in: `src/types/components/organization/Invite.ts` (Line 5)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 64)
- Used in: `src/app/dashboard/admin/invites/page.tsx` (Line 107)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 131)
- Used in: `src/components/organization/all-invites-list.tsx` (Line 63)
- Used in: `src/components/organization/invites-list.tsx` (Line 63)
- Used in: `src/types/components/organization/AllInvitesListProps.ts` (Line 4)
- Used in: `src/types/components/organization/InvitesListProps.ts` (Line 4)

## InviteCodeDisplayProps
- Declared in: `src/types/components/organization/InviteCodeDisplayProps.ts` (Line 1)
- Used in: `src/components/organization/invite-code-display.tsx` (Line 21)

## InviteData
- Declared in: `src/types/components/organization/InviteData.ts` (Line 1)
- Used in: `src/components/organization/create-invite-button.tsx` (Line 40)

## InvitesListProps
- Declared in: `src/types/components/organization/InvitesListProps.ts` (Line 3)
- Used in: `src/components/organization/invites-list.tsx` (Line 27)

## JoinOrganizationModalProps
- Declared in: `src/types/components/organization/JoinOrganizationModalProps.ts` (Line 1)
- Used in: `src/components/organization/join-organization-modal.tsx` (Line 23)

## Label
- Declared in: `src/types/components/labels/Label.ts` (Line 14)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 339)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 16)
- Used in: `src/types/components/labels/LabelEditDialogProps.ts` (Line 4)
- Used in: `src/types/components/labels/LabelEditDialogProps.ts` (Line 7)

## LabelCategory
- Declared in: `src/types/app/dashboard/admin/labels/LabelCategory.ts` (Line 1)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 113)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 338)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 342)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 498)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 504)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 20)
- Used in: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts` (Line 6)
- Used in: `src/types/components/labels/LabelCategoryEditDialogProps.ts` (Line 4)
- Used in: `src/types/components/labels/LabelCategoryEditDialogProps.ts` (Line 7)

## LabelCategoryEditDialogProps
- Declared in: `src/types/components/labels/LabelCategoryEditDialogProps.ts` (Line 3)
- Used in: `src/components/labels/LabelCategoryEditDialog.tsx` (Line 19)

## LabelData
- Declared in: `src/types/app/dashboard/admin/labels/LabelData.ts` (Line 14)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 112)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 115)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 579)
- Used in: `src/types/app/dashboard/admin/labels/LabelData.ts` (Line 26)
- Used in: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts` (Line 5)
- Used in: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts` (Line 8)

## LabelEditDialogProps
- Declared in: `src/types/components/labels/LabelEditDialogProps.ts` (Line 3)
- Used in: `src/components/labels/LabelEditDialog.tsx` (Line 15)

## MembershipData
- Declared in: `src/lib/rbac/permissions-server.ts` (Line 13)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 109)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 125)

## MemberStatusChangedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 29)
- Used in: `src/components/developer/test-event-system.tsx` (Line 59)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 78)
- Used in: `src/lib/eventBus/emitter.ts` (Line 36)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 30)
- Used in: `src/lib/eventBus/hooks/useStatusEvents.ts` (Line 11)

## MemberTableBusinessRules
- Declared in: `src/components/shared/member-table.tsx` (Line 16)
- Used in: `src/app/dashboard/admin/members/page.tsx` (Line 9)
- Used in: `src/app/dashboard/clients/page.tsx` (Line 14)
- Used in: `src/app/dashboard/developer/users/page.tsx` (Line 27)
- Used in: `src/components/shared/member-table.tsx` (Line 93)

## MemberTablePagePermissions
- Declared in: `src/components/shared/member-table.tsx` (Line 28)
- Used in: `src/components/shared/member-table.tsx` (Line 91)
- Used in: `src/components/shared/member-table.tsx` (Line 205)

## MemberTableProps
- Declared in: `src/components/shared/member-table.tsx` (Line 88)
- Used in: `src/components/shared/member-table.tsx` (Line 199)

## MemberUpdatingState
- Declared in: `src/types/hooks/MemberUpdatingState.ts` (Line 1)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 103)
- Used in: `src/types/hooks/MemberUpdatingState.ts` (Line 8)

## MenuItemProps
- Declared in: `src/types/components/MenuItemProps.ts` (Line 3)
- Used in: `src/components/MenuItem.tsx` (Line 11)
- Used in: `src/types/components/MenuItemProps.ts` (Line 12)

## MultiPermissionConfig
- Declared in: `src/types/lib/rbac/index.ts` (Line 306)
- Used in: `src/lib/rbac/server-action-multi.ts` (Line 13)
- Used in: `src/lib/rbac/server-action-multi.ts` (Line 18)

## NavigationItem
- Declared in: `src/types/navigation.ts` (Line 3)
- Used in: `src/components/Sidebar.tsx` (Line 97)
- Used in: `src/types/navigation.ts` (Line 8)
- Used in: `src/types/navigation.ts` (Line 13)

## NavigationSection
- Declared in: `src/types/navigation.ts` (Line 11)
- Used in: `src/data/navigation.ts` (Line 14)

## NoAccessProps
- Declared in: `src/types/components/NoAccessProps.ts` (Line 1)
- Used in: `src/components/no-access.tsx` (Line 9)

## OrgActionConfig
- Declared in: `src/types/lib/rbac/index.ts` (Line 292)
- Used in: `src/lib/rbac/server-action-org.ts` (Line 13)
- Used in: `src/lib/rbac/server-action-org.ts` (Line 20)

## Organization
- Declared in: `src/types/organization.ts` (Line 1)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 10)
- Used in: `src/app/api/organizations/create/route.ts` (Line 24)
- Used in: `src/app/api/organizations/create/route.ts` (Line 104)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 174)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 15)
- Used in: `src/app/dashboard/DashboardLayoutClient.tsx` (Line 16)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 55)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 202)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 209)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 295)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 15)
- Used in: `src/components/dashboard/client-dashboard-shell/index.tsx` (Line 13)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 70)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 66)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 84)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 366)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 425)
- Used in: `src/components/shared/member-table.tsx` (Line 291)
- Used in: `src/components/Sidebar.tsx` (Line 33)
- Used in: `src/components/Sidebar.tsx` (Line 49)
- Used in: `src/components/Sidebar.tsx` (Line 53)
- Used in: `src/components/Sidebar.tsx` (Line 54)
- Used in: `src/components/Sidebar.tsx` (Line 72)
- Used in: `src/components/TopBar.tsx` (Line 15)
- Used in: `src/hooks/use-organization-context.ts` (Line 37)
- Used in: `src/hooks/use-organization-context.ts` (Line 114)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 65)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 117)
- Used in: `src/hooks/use-organization-storage.ts` (Line 10)
- Used in: `src/hooks/use-organization-storage.ts` (Line 11)
- Used in: `src/hooks/use-organizations-list.ts` (Line 6)
- Used in: `src/hooks/use-organizations-list.ts` (Line 15)
- Used in: `src/hooks/use-organizations-list.ts` (Line 61)
- Used in: `src/hooks/use-organizations-list.ts` (Line 89)
- Used in: `src/hooks/use-organizations-list.ts` (Line 112)
- Used in: `src/hooks/use-organizations-list.ts` (Line 116)
- Used in: `src/hooks/use-organizations-list.ts` (Line 157)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 73)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 90)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 94)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 25)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 51)
- Used in: `src/lib/get-user-active-organization.ts` (Line 32)
- Used in: `src/lib/get-user-active-organization.ts` (Line 64)
- Used in: `src/lib/organization-utils-server.ts` (Line 44)
- Used in: `src/lib/organization-utils-server.ts` (Line 164)
- Used in: `src/lib/organization-utils-server.ts` (Line 228)
- Used in: `src/lib/organization-utils-server.ts` (Line 291)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 8)
- Used in: `src/types/components/organization/Invite.ts` (Line 17)
- Used in: `src/types/components/organization/OrganizationListProps.ts` (Line 4)
- Used in: `src/types/components/organization/OrganizationSwitcherProps.ts` (Line 4)
- Used in: `src/types/components/organization/OrganizationSwitcherProps.ts` (Line 5)
- Used in: `src/types/components/organization/OrganizationTableProps.ts` (Line 4)
- Used in: `src/types/components/providers/DashboardContextType.ts` (Line 11)
- Used in: `src/types/components/providers/DashboardContextType.ts` (Line 12)
- Used in: `src/types/components/providers/DashboardProviderProps.ts` (Line 10)
- Used in: `src/types/components/providers/DashboardProviderProps.ts` (Line 11)
- Used in: `src/types/organization/index.ts` (Line 18)
- Used in: `src/types/organization/index.ts` (Line 28)

## OrganizationActiveStatusChangedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 101)
- Used in: `src/lib/eventBus/channels/organizationStatusInterpreter.ts` (Line 38)
- Used in: `src/lib/eventBus/emitter.ts` (Line 40)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 43)

## OrganizationContextEvent
- Declared in: `src/lib/eventTypes.ts` (Line 8)
- Used in: `src/components/developer/test-event-system.tsx` (Line 35)
- Used in: `src/hooks/use-organization-context.ts` (Line 137)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 29)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 47)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 60)
- Used in: `src/lib/eventBus/emitter.ts` (Line 34)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 59)
- Used in: `src/lib/eventBus/hooks/useContextEvents.ts` (Line 11)

## OrganizationData
- Declared in: `src/lib/rbac/permissions-server.ts` (Line 9)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 16)

## OrganizationDataChangedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 91)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 276)
- Used in: `src/lib/eventBus/channels/name.ts` (Line 24)
- Used in: `src/lib/eventBus/channels/organizationStatusInterpreter.ts` (Line 18)
- Used in: `src/lib/eventBus/emitter.ts` (Line 50)
- Used in: `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 13)
- Used in: `src/lib/eventBus/hooks/useOrganizationEvents.ts` (Line 31)

## OrganizationListItem
- Declared in: `src/components/shared/member-table.tsx` (Line 81)
- Used in: `src/components/shared/member-table.tsx` (Line 312)

## OrganizationListProps
- Declared in: `src/types/components/organization/OrganizationListProps.ts` (Line 3)
- Used in: `src/components/organization/organization-list.tsx` (Line 20)

## OrganizationMember
- Declared in: `src/types/organization.ts` (Line 15)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 24)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 48)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 58)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 59)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 70)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 70)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 83)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 83)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 106)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 107)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 233)
- Used in: `src/lib/eventBus/channels/manager.ts` (Line 234)

## OrganizationMemberBasic
- Declared in: `src/types/organization/OrganizationMember.ts` (Line 22)
- Used in: `src/app/api/organization-members/remove/route.ts` (Line 63)
- Used in: `src/app/api/organization-members/update-profile/route.ts` (Line 64)
- Used in: `src/app/api/organization-members/update-role/route.ts` (Line 51)
- Used in: `src/app/api/organization-members/update-status/route.ts` (Line 51)
- Used in: `src/app/api/user-profile/[userId]/route.ts` (Line 81)
- Used in: `src/components/shared/member-table.tsx` (Line 347)
- Used in: `src/components/shared/member-table.tsx` (Line 374)
- Used in: `src/components/shared/member-table.tsx` (Line 401)
- Used in: `src/components/shared/member-table.tsx` (Line 490)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 263)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 270)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 277)
- Used in: `src/lib/permission-utils.ts` (Line 18)
- Used in: `src/lib/permission-utils.ts` (Line 58)
- Used in: `src/lib/permission-utils.ts` (Line 106)
- Used in: `src/lib/permission-utils.ts` (Line 154)

## OrganizationMemberDB
- Declared in: `src/app/api/organization-members/route.ts` (Line 9)
- Used in: `src/app/api/organization-members/route.ts` (Line 192)

## OrganizationMemberDeletedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 81)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 60)
- Used in: `src/lib/eventBus/emitter.ts` (Line 49)

## OrganizationMemberFull
- Declared in: `src/types/organization/index.ts` (Line 26)
- Used in: `src/app/api/organization-members/route.ts` (Line 193)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 30)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 49)
- Used in: `src/components/shared/member-table.tsx` (Line 257)
- Used in: `src/components/shared/member-table.tsx` (Line 328)
- Used in: `src/components/shared/member-table.tsx` (Line 356)
- Used in: `src/components/shared/member-table.tsx` (Line 383)
- Used in: `src/components/shared/member-table.tsx` (Line 410)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 49)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 192)
- Used in: `src/types/components/UserProfileDialogProps.ts` (Line 5)
- Used in: `src/types/components/UserTableProps.ts` (Line 5)

## OrganizationMemberInsertedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 61)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 23)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 26)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 66)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 67)
- Used in: `src/lib/eventBus/emitter.ts` (Line 47)

## OrganizationMemberResponse
- Declared in: `src/types/app/dashboard/admin/users/OrganizationMemberResponse.ts` (Line 3)
- *No usage found based on simple type reference matching within the project.*

## OrganizationMembershipResponse
- Declared in: `src/types/organization/OrganizationMembershipResponse.ts` (Line 4)
- Used in: `src/hooks/use-organization-context.ts` (Line 112)
- Used in: `src/lib/get-user-active-organization.ts` (Line 97)
- Used in: `src/lib/organization-utils-server.ts` (Line 139)
- Used in: `src/lib/organization-utils-server.ts` (Line 202)
- Used in: `src/lib/organization-utils-server.ts` (Line 265)
- Used in: `src/lib/organization-utils-server.ts` (Line 350)

## OrganizationMemberUpdatedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 71)
- Used in: `src/lib/eventBus/channels/allMembers.ts` (Line 38)
- Used in: `src/lib/eventBus/channels/context.ts` (Line 39)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 101)
- Used in: `src/lib/eventBus/channels/status.ts` (Line 102)
- Used in: `src/lib/eventBus/emitter.ts` (Line 48)

## OrganizationMemberWithOrg
- Declared in: `src/types/organization/OrganizationMemberWithOrg.ts` (Line 1)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 187)

## OrganizationNavProps
- Declared in: `src/types/components/organization/OrganizationNavProps.ts` (Line 1)
- Used in: `src/components/organization/organization-nav.tsx` (Line 6)

## OrganizationResponse
- Declared in: `src/app/api/organizations/switch/route.ts` (Line 6)
- Used in: `src/app/api/organizations/switch/route.ts` (Line 53)

## OrganizationSwitcherProps
- Declared in: `src/types/components/organization/OrganizationSwitcherProps.ts` (Line 3)
- Used in: `src/components/organization/organization-switcher.tsx` (Line 61)

## OrganizationTableProps
- Declared in: `src/types/components/organization/OrganizationTableProps.ts` (Line 3)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 194)

## OrganizationWithRole
- Declared in: `src/types/organization/index.ts` (Line 9)
- *No usage found based on simple type reference matching within the project.*

## OrgMemberRecord
- Declared in: `src/lib/auth-context.ts` (Line 252)
- Used in: `src/lib/auth-context.ts` (Line 291)

## OrgNameChangedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 41)
- Used in: `src/components/developer/test-event-system.tsx` (Line 71)
- Used in: `src/lib/eventBus/channels/name.ts` (Line 30)
- Used in: `src/lib/eventBus/emitter.ts` (Line 37)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 72)
- Used in: `src/lib/eventBus/hooks/useNameEvents.ts` (Line 11)

## PageProps
- Declared in: `src/types/app/PageProps.ts` (Line 1)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 56)

## PaginatedOrganizationsResponse
- Declared in: `src/lib/organization-utils-server.ts` (Line 43)
- Used in: `src/app/api/organizations/authorized/route.ts` (Line 244)
- Used in: `src/app/api/organizations/complete/route.ts` (Line 22)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 227)
- Used in: `src/app/dashboard/developer/organizations/page.tsx` (Line 19)
- Used in: `src/hooks/use-organizations-list.ts` (Line 15)
- Used in: `src/hooks/use-organizations-list.ts` (Line 59)
- Used in: `src/hooks/use-organizations-list.ts` (Line 112)
- Used in: `src/hooks/use-organizations-list.ts` (Line 116)
- Used in: `src/lib/organization-utils-server.ts` (Line 56)

## PermissionCheckOptions
- Declared in: `src/types/lib/rbac/index.ts` (Line 244)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 25)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 155)

## PermissionMiddlewareConfig
- Declared in: `src/types/lib/rbac/index.ts` (Line 260)
- Used in: `src/lib/rbac/middleware.ts` (Line 13)

## PermissionResult
- Declared in: `src/types/lib/rbac/index.ts` (Line 65)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 157)
- Used in: `src/lib/rbac/role-utils.ts` (Line 63)
- Used in: `src/lib/rbac/role-utils.ts` (Line 63)
- Used in: `src/lib/rbac/role-utils.ts` (Line 76)
- Used in: `src/lib/rbac/role-utils.ts` (Line 77)
- Used in: `src/lib/rbac/role-utils.ts` (Line 91)
- Used in: `src/lib/rbac/role-utils.ts` (Line 92)
- Used in: `src/lib/rbac/role-utils.ts` (Line 103)
- Used in: `src/lib/rbac/role-utils.ts` (Line 103)
- Used in: `src/lib/rbac/role-utils.ts` (Line 112)
- Used in: `src/lib/rbac/role-utils.ts` (Line 112)

## Procedure
- Declared in: `src/types/handbook.ts` (Line 39)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 281)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 285)
- Used in: `src/types/handbook.ts` (Line 50)

## Procedures
- Declared in: `src/types/handbook.ts` (Line 48)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 261)
- Used in: `src/types/handbook.ts` (Line 123)

## ProcedureStep
- Declared in: `src/types/handbook.ts` (Line 34)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 295)
- Used in: `src/types/handbook.ts` (Line 42)

## ProcessInviteResponse
- Declared in: `src/types/app/actions/ProcessInviteResponse.ts` (Line 2)
- Used in: `src/app/actions/process-invite.ts` (Line 9)

## Profile
- Declared in: `src/types/app/dashboard/admin/invites/Profile.ts` (Line 1)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 10)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 11)
- Used in: `src/types/components/organization/Invite.ts` (Line 19)
- Used in: `src/types/components/organization/Invite.ts` (Line 21)
- Used in: `src/types/components/profile/ProfileFormProps.ts` (Line 6)

## ProfileFormProps
- Declared in: `src/types/components/profile/ProfileFormProps.ts` (Line 5)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 88)

## RawInvite
- Declared in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 20)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 128)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 131)

## RbacConditions
- Declared in: `src/types/lib/rbac/index.ts` (Line 156)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 66)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 124)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 135)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 139)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 143)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 147)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 157)
- Used in: `src/lib/permissions-service-client.ts` (Line 130)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 24)
- Used in: `src/lib/rbac/permissions-server.ts` (Line 154)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 78)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 110)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 130)
- Used in: `src/types/lib/rbac/index.ts` (Line 200)

## RequiredDocument
- Declared in: `src/types/handbook.ts` (Line 53)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 353)
- Used in: `src/types/handbook.ts` (Line 63)

## RequiredDocuments
- Declared in: `src/types/handbook.ts` (Line 62)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 331)
- Used in: `src/types/handbook.ts` (Line 124)

## RestrictedProps
- Declared in: `src/components/rbac/restricted.tsx` (Line 7)
- Used in: `src/components/rbac/restricted.tsx` (Line 38)

## Role
- Declared in: `src/types/components/organization/Role.ts` (Line 2)
- Used in: `src/components/organization/create-invite-dialog.tsx` (Line 71)
- Used in: `src/types/app/dashboard/admin/invites/Invite.ts` (Line 9)

## RoleHelpers
- Declared in: `src/types/lib/rbac/index.ts` (Line 85)
- Used in: `src/lib/rbac/role-utils.ts` (Line 30)

## RolePermission
- Declared in: `src/types/lib/rbac/index.ts` (Line 213)
- *No usage found based on simple type reference matching within the project.*

## RoleRecord
- Declared in: `src/types/lib/rbac/index.ts` (Line 224)
- *No usage found based on simple type reference matching within the project.*

## Service
- Declared in: `src/types/handbook.ts` (Line 115)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 129)
- Used in: `src/types/handbook.ts` (Line 121)

## ServiceChannels
- Declared in: `src/types/handbook.ts` (Line 30)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 201)
- Used in: `src/types/handbook.ts` (Line 126)

## ServicePlace
- Declared in: `src/types/handbook.ts` (Line 22)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 217)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 222)
- Used in: `src/types/handbook.ts` (Line 31)

## SheetContentProps
- Declared in: `src/types/components/ui/SheetContentProps.ts` (Line 6)
- Used in: `src/components/ui/sheet.tsx` (Line 57)

## SidebarProps
- Declared in: `src/components/Sidebar.tsx` (Line 14)
- Used in: `src/components/Sidebar.tsx` (Line 18)

## SortableFlowStepProps
- Declared in: `src/types/app/dashboard/admin/flowsteps/SortableFlowStepProps.ts` (Line 5)
- Used in: `src/app/dashboard/admin/flowsteps/page.tsx` (Line 238)

## SortableLabelProps
- Declared in: `src/types/app/dashboard/admin/labels/SortableLabelProps.ts` (Line 4)
- Used in: `src/app/dashboard/admin/labels/page.tsx` (Line 246)

## Source
- Declared in: `src/types/handbook.ts` (Line 110)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 585)
- Used in: `src/types/handbook.ts` (Line 130)

## State
- Declared in: `src/types/hooks/State.ts` (Line 16)
- Used in: `src/hooks/use-toast.ts` (Line 62)
- Used in: `src/hooks/use-toast.ts` (Line 62)
- Used in: `src/hooks/use-toast.ts` (Line 116)
- Used in: `src/hooks/use-toast.ts` (Line 118)
- Used in: `src/hooks/use-toast.ts` (Line 171)

## Step
- Declared in: `src/types/flows.ts` (Line 17)
- Used in: `src/app/dashboard/admin/flows/page.tsx` (Line 38)
- Used in: `src/components/flows/FlowCard.tsx` (Line 33)
- Used in: `src/components/flows/FlowCard.tsx` (Line 43)
- Used in: `src/types/flows.ts` (Line 33)

## SubItem
- Declared in: `src/types/handbook.ts` (Line 1)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 174)
- Used in: `src/types/handbook.ts` (Line 8)

## SubRemark
- Declared in: `src/types/handbook.ts` (Line 100)
- Used in: `src/app/dashboard/handbooks/page.tsx` (Line 573)
- Used in: `src/types/handbook.ts` (Line 107)

## TablePaginationProps
- Declared in: `src/components/shared/member-table.tsx` (Line 99)
- Used in: `src/app/dashboard/developer/organizations/organization-table.tsx` (Line 118)
- Used in: `src/components/shared/member-table.tsx` (Line 117)

## TableSkeletonProps
- Declared in: `src/types/app/dashboard/admin/users/TableSkeletonProps.ts` (Line 2)
- Used in: `src/app/dashboard/admin/users/table-skeleton.tsx` (Line 8)

## Toast
- Declared in: `src/hooks/use-toast.ts` (Line 127)
- Used in: `src/hooks/use-toast.ts` (Line 141)

## TooltipProps
- Declared in: `src/types/components/shared/TooltipProps.ts` (Line 4)
- Used in: `src/components/shared/Tooltip.tsx` (Line 5)

## TopBarProps
- Declared in: `src/components/TopBar.tsx` (Line 11)
- Used in: `src/components/TopBar.tsx` (Line 18)

## UpdatingStates
- Declared in: `src/types/hooks/MemberUpdatingState.ts` (Line 7)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 68)

## UseOrganizationMembersProps
- Declared in: `src/hooks/use-organization-members-event-bus.ts` (Line 29)
- Used in: `src/components/shared/member-table.tsx` (Line 261)
- Used in: `src/hooks/use-organization-members-event-bus.ts` (Line 45)

## UseOrganizationsListOptions
- Declared in: `src/hooks/use-organizations-list.ts` (Line 86)
- Used in: `src/hooks/use-organizations-list.ts` (Line 93)

## UserAuthFormProps
- Declared in: `src/types/components/auth/UserAuthFormProps.ts` (Line 4)
- Used in: `src/components/auth/user-auth-form.tsx` (Line 23)

## UserAvatarMenuProps
- Declared in: `src/types/components/UserAvatarMenuProps.ts` (Line 4)
- Used in: `src/components/user-avatar-menu.tsx` (Line 19)

## UseRbacPermissionProps
- Declared in: `src/hooks/use-rbac-permission.ts` (Line 14)
- Used in: `src/hooks/use-rbac-permission.ts` (Line 26)

## UserPermissionsMatrix
- Declared in: `src/lib/permissions-service-client.ts` (Line 11)
- Used in: `src/lib/permissions-service-client.ts` (Line 10)
- Used in: `src/lib/permissions-service-client.ts` (Line 90)
- Used in: `src/lib/permissions-service-client.ts` (Line 93)
- Used in: `src/lib/permissions-service-client.ts` (Line 117)
- Used in: `src/lib/permissions-service-client.ts` (Line 200)
- Used in: `src/lib/permissions-service-client.ts` (Line 210)
- Used in: `src/lib/permissions-service-client.ts` (Line 235)
- Used in: `src/lib/permissions-service-client.ts` (Line 252)
- Used in: `src/lib/permissions-service-client.ts` (Line 279)

## UserPersonalInfo
- Declared in: `src/types/user/UserPersonalInfo.ts` (Line 5)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 27)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 182)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 188)
- Used in: `src/components/profile/ProfileForm.tsx` (Line 289)
- Used in: `src/types/app/dashboard/admin/users/OrganizationMemberResponse.ts` (Line 15)
- Used in: `src/types/components/profile/PersonalInfo.ts` (Line 9)
- Used in: `src/types/organization/index.ts` (Line 38)
- Used in: `src/types/user/index.ts` (Line 9)

## UserProfile
- Declared in: `src/types/user/UserProfile.ts` (Line 4)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 133)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 142)
- Used in: `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (Line 151)
- Used in: `src/types/components/profile/Profile.ts` (Line 9)

## UserProfileDialogProps
- Declared in: `src/types/components/UserProfileDialogProps.ts` (Line 4)
- Used in: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 47)

## UserProfileWithPersonalInfo
- Declared in: `src/types/user/index.ts` (Line 8)
- *No usage found based on simple type reference matching within the project.*

## UserRole
- Declared in: `src/types/lib/rbac/index.ts` (Line 16)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 109)
- Used in: `src/lib/rbac/rbac-utils.ts` (Line 117)

## UserRoleChangedEvent
- Declared in: `src/lib/eventTypes.ts` (Line 17)
- Used in: `src/components/developer/test-event-system.tsx` (Line 47)
- Used in: `src/lib/eventBus/channels/role.ts` (Line 75)
- Used in: `src/lib/eventBus/emitter.ts` (Line 35)
- Used in: `src/lib/eventBus/hooks/useAuthEvents.ts` (Line 17)
- Used in: `src/lib/eventBus/hooks/useRoleEvents.ts` (Line 11)

## UserTableProps
- Declared in: `src/types/components/UserTableProps.ts` (Line 4)
- *No usage found based on simple type reference matching within the project.*

## Window
- Declared in: `src/types/globals.d.ts` (Line 8)
- *No usage found based on simple type reference matching within the project.*


*Note: Usage detection is based on finding type references matching the interface name. It might miss complex type manipulations or include matches for identically named types from external sources.*
