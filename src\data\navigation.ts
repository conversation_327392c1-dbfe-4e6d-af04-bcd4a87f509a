import { NavigationSection } from '../types/navigation';

// Local interface definitions for NavigationItem and NavigationSection are removed.
// The data below will be typed by the imported NavigationSection.

// Role IDs from the database:
// 1: Superadmin (most permissive)
// 2: Supportadmin
// 3: Orgadmin
// 4: Orgmember
// 5: Orgaccounting
// 6: Orgclient (most restricted)

export const navigationData: NavigationSection[] = [
  {
    title: 'Main',
    items: [
      { label: 'Dashboard', icon: 'layoutDashboard', href: '/dashboard' },
      { label: 'My Forms', icon: 'shoppingBag', href: '/dashboard/my-forms' },
      {
        label: 'Create Form',
        icon: 'plus',
        href: '/dashboard/create-form',
        RbacConditions: { rMinRole: 'orgMember' }
      },
      {
        label: 'Forms',
        icon: 'formInput',
        href: '/dashboard/forms',
        RbacConditions: { rMinRole: 'orgClient' }
      },
      {
        label: 'Handbooks',
        icon: 'book',
        href: '/dashboard/handbooks',
        RbacConditions: { rMinRole: 'orgClient' }
      },
      {
        label: 'Clients',
        icon: 'users',
        href: '/dashboard/clients',
        RbacConditions: { rMinRole: 'orgMember' }
      },
      {
        label: 'Client Invites',
        icon: 'userPlus',
        href: '/dashboard/clients/invites',
        RbacConditions: { rMinRole: 'orgMember' }
      },
      {
        label: 'Settings',
        icon: 'settings',
        href: '/dashboard/settings',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
    ],
  },
  {
    title: 'Admin',
    RbacConditions: { rMinRole: 'orgAdmin' },
    items: [
      {
        label: 'Organization Settings',
        icon: 'settings',
        href: '/dashboard/admin/organization-settings',
      },
      {
        label: 'Members',
        icon: 'users',
        href: '/dashboard/admin/members',
      },
      {
        label: 'Invites',
        icon: 'userPlus',
        href: '/dashboard/admin/invites',
      },
      {
        label: 'Member Invites',
        icon: 'mail',
        href: '/dashboard/admin/members/invites',
      },
      {
        label: 'Labels',
        icon: 'library',
        href: '/dashboard/admin/labels',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
      {
        label: 'Flows',
        icon: 'Workflow',
        href: '/dashboard/admin/flows',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
      {
        label: 'Billing',
        icon: 'CircleDollarSign',
        href: '/dashboard/profile/billing',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
    ],
  },
  {
    title: 'Developer',
    RbacConditions: { rRoles: ['superAdmin', 'supportAdmin'] },
    items: [
      {
        label: 'Organizations',
        icon: 'building',
        href: '/dashboard/developer/organizations'
      },
      {
        label: 'All Users',
        icon: 'Users',
        href: '/dashboard/developer/users'
      },
      {
        label: 'User Invites',
        icon: 'mail',
        href: '/dashboard/developer/users/invites'
      },
      {
        label: 'Create Organization',
        icon: 'PlusCircle',
        href: '/dashboard/developer/create-organization',
      },
      {
        label: 'Test Event System',
        icon: 'TestTube2',
        href: '/dashboard/developer/test-event'
      },
      {
        label: 'RBAC Test',
        icon: 'shield',
        href: '/dashboard/rbac-test'
      }
    ],
  },
];