export const LABEL_TYPES = [
  "text",
  "number",
  "date",
  "email",
  "phone",
  "select",
  "checkbox",
  "radio",
] as const

export type LabelType = (typeof LABEL_TYPES)[number]

export interface LabelData {
  id: number
  uid: string
  name: string
  description: string
  type: LabelType
  category_id: number
  position: number
  created_at?: string
  updated_at?: string
}

export type Label = LabelData 