# Cookie Cleanup Instructions

This document outlines the necessary steps to clean up cookie-based references in the codebase after migrating to the DB-driven organization context.

## Files to Update

### 1. Organization Switch Route
- Delete `/src/app/actions/switch-organization.ts` (cookie-based approach)
- Ensure API endpoints use the new DB functions

### 2. Middleware
- Replace `src/middleware.ts` with `src/middleware-update.ts`
- Remove/replace any references to `active_organization_id` cookie

### 3. RBAC Permissions
- Replace `src/lib/rbac/permissions-server.ts` with `src/lib/rbac/permissions-server-update.ts`
- Remove all cookie references

### 4. Organization Utils
- Replace any instances where `organization-utils-server.ts` uses cookies
- Add/use the new `getUserActiveOrganization` functions

### 5. Client-Side Components
- Ensure all components use the new `useOrganizationContext` hook
- Remove any code that directly reads/writes the organization cookie

## Generic Search & Replace

Use the following command to find all remaining references to the cookie:

```bash
grep -r "active_organization_id" --include="*.ts" --include="*.tsx" src/
```

## Cookie Removal Code

For any remaining direct cookie manipulation code, replace with the appropriate DB function:

### Reading Cookie (Server-Side)
```typescript
// REMOVE THIS
const cookieStore = await cookies()
const activeOrgCookie = cookieStore.get('active_organization_id')?.value

// REPLACE WITH
const { organization } = await getUserActiveOrganization()
const activeOrgId = organization?.id
```

### Setting Cookie (Server-Side)
```typescript
// REMOVE THIS
cookieStore.set({
  name: 'active_organization_id',
  value: orgId,
  // ...options
})

// REPLACE WITH
const supabase = await createClient()
await supabase.rpc('set_user_organization_context', {
  p_user_id: userId,
  p_org_id: orgId
})
```

### Client-Side Usage
```typescript
// REMOVE THIS
const switchOrganization = async (orgId) => {
  // Old cookie-based approach
}

// REPLACE WITH
const { switchOrganization } = useOrganizationContext()
// Then use switchOrganization(orgId)
```

## Verification Process

After removing all cookie references:

1. Test login flow
2. Test organization switching
3. Test page refreshes
4. Test admin routes
5. Test multiple browsers/devices
6. Test realtime updates

## Rollback Plan

If issues arise:

1. Keep the DB functions in place
2. Reintroduce cookie setting in `set_user_organization_context`
3. Add cookie reading as a fallback mechanism in `getUserActiveOrganization`
4. Gradually migrate components to use the DB approach 