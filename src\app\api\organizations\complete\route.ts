import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getUserOrganizations, PaginatedOrganizationsResponse } from '@/lib/organization-utils-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
    const search = searchParams.get('search') || undefined;
    // const scope = searchParams.get('scope'); // Scope param is not used by getUserOrganizations currently for fetching all

    // getUserOrganizations with includeInactive:true already fetches all for superAdmin
    // and filters for non-superAdmins. Pagination is now handled.
    const paginatedResult: PaginatedOrganizationsResponse = await getUserOrganizations(
      user.id,
      true, // includeInactive members and orgs (superadmin sees all, others see active orgs they are in)
      page,
      pageSize,
      search
    );

    return NextResponse.json(paginatedResult);

  } catch (error) {
    console.error('Error in complete organizations endpoint:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}