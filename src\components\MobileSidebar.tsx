'use client'

import React, { useState } from "react";
import { Sidebar } from "./Sidebar";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";

export function MobileSidebar() {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  return (
    <div className="lg:hidden">
      <Sheet open={isMobileSidebarOpen} onOpenChange={setIsMobileSidebarOpen}>
        <SheetContent side="left" className="p-0 w-64">
          <VisuallyHidden.Root>
            <SheetTitle>Menu</SheetTitle>
          </VisuallyHidden.Root>
          <SheetHeader>
            <h2 className="text-lg font-bold px-4 py-2 text-white">Menu</h2>
          </SheetHeader>
          <Sidebar isSidebarCollapsed={false} />
        </SheetContent>
      </Sheet>
    </div>
  );
}
