"use client";

import React, { useState, useEffect, useLayoutEffect } from "react";
// import { DashboardEventManager } from "@/components/dashboard/dashboard-event-manager";
import { DashboardEventManagerCore } from "@/components/dashboard/dashboard-event-manager-core";
import { DashboardScenarioManager } from "@/components/dashboard/dashboard-scenario-manager";
import { createClient } from "@/lib/supabase/client";
import { attachMittDebugger } from "@/tools/mitt-debugger";
import { emitter } from "@/lib/eventBus";
import { initializeEventSystem } from "@/lib/eventBus/initializeInterpreters";
import { initializeGlobalFetchInterceptor } from "@/lib/global-fetch-interceptor";

// Global flag to signal event system init status
// This is intentionally outside the component to be globally available
// even before hydration completes
if (typeof window !== 'undefined') {
  // Create a global indicator element immediately for early detection
  document.addEventListener('DOMContentLoaded', () => {
    if (!document.getElementById('global-event-system-indicator')) {
      const indicator = document.createElement('div');
      indicator.id = 'global-event-system-indicator';
      indicator.setAttribute('data-status', 'initializing');
      indicator.style.display = 'none';
      document.body.appendChild(indicator);
    }
  });
}

/**
 * GlobalDashboardProvider ensures DashboardEventManager is mounted exactly once
 * at the application root, regardless of route changes or component re-renders.
 *
 * This provider should be placed in the root layout.
 */
export function GlobalDashboardProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const supabase = createClient();

  // Check authentication status once on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setIsAuthenticated(!!session);

        // Listen for auth state changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (_event, session) => {
            setIsAuthenticated(!!session);
          }
        );

        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error("[GlobalDashboardProvider] Auth check error:", error);
        setIsAuthenticated(false);
      }
    };

    checkAuth();
  }, [supabase]);

  // Initialize the event system as early as possible
  useLayoutEffect(() => {
    // Initialize the global fetch interceptor for 401 error handling
    initializeGlobalFetchInterceptor();

    // Initialize event system unconditionally
    initializeEventSystem();

    if (process.env.NODE_ENV === 'development') {
      // Use the enhanced attachMittDebugger which internally checks if it's already attached
      attachMittDebugger(emitter);
    }

    // Set the global indicator to 'ready' to allow components to detect event system availability
    if (typeof window !== 'undefined') {
      const indicator = document.getElementById('global-event-system-indicator');
      if (indicator) {
        indicator.setAttribute('data-status', 'ready');
      } else {
        // If for some reason the indicator doesn't exist yet, create it
        const newIndicator = document.createElement('div');
        newIndicator.id = 'global-event-system-indicator';
        newIndicator.setAttribute('data-status', 'ready');
        newIndicator.style.display = 'none';
        document.body.appendChild(newIndicator);
      }
    }
  }, []);

  // Render event managers only when authenticated, but ensure they mount as early as possible
  return (
    <>
      {isAuthenticated === true && (
        <>
          <DashboardEventManagerCore />
          <DashboardScenarioManager />
        </>
      )}
      {children}
    </>
  );
}