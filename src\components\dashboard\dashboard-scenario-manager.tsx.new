'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { useServerContextRefresher } from '@/hooks/use-server-context-refresher';
import { useAuthContextEvents } from '@/hooks/use-auth-context-events';
import type { AuthEvent } from '@/lib/eventTypes';
import { AUTH_CONTEXT_CHANGED } from '@/lib/eventBus/constants';
import { emitter } from '@/lib/eventBus';

// Unique instance ID for singleton management
const INSTANCE_ID = Math.random().toString(36).substring(2, 12);

// Configuration
const CONFIG = {
  SINGLETON_DOM_ID: 'dashboard-scenario-manager-singleton',
  DEBUG: false,
};

// Simple console logger with tag
function log(level: 'debug' | 'info' | 'warn' | 'error', ...args: any[]) {
  if (level === 'debug' && !CONFIG.DEBUG) return;
  console[level](`[DashboardScenarioManager ${INSTANCE_ID.slice(0, 4)}]`, ...args);
}

/**
 * DashboardScenarioManager
 * 
 * This component handles authentication context events and manages navigation
 * based on organization and user status. It ensures users are redirected to
 * appropriate pages when their permissions or organization status changes.
 * 
 * Key responsibilities:
 * 1. Monitors authentication context changes
 * 2. Redirects users based on their status and permissions
 * 3. Ensures only one instance handles events (singleton pattern)
 */
export function DashboardScenarioManager() {
  const router = useRouter();
  
  // Use the centralized refresh hooks - no need to directly call methods
  useServerContextRefresher();
  useAuthContextEvents();
  
  // Track if this component is the active singleton instance
  const isSingletonRef = useRef(false);
  
  // Get active organization state from the store
  const { userId, orgId, isOrgActive, isUserActiveInOrg, isSuperAdmin } = useAuthContextStore();

  // Implement client-side navigation based on auth context
  useEffect(() => {
    // Skip if not in browser context
    if (typeof window === 'undefined') return;
    
    // Skip if we don't have all the required data
    if (orgId === null || isOrgActive === null || isUserActiveInOrg === null) return;
    
    const currentPath = window.location.pathname;
    log('debug', `Checking navigation rules with: orgId=${orgId}, isOrgActive=${isOrgActive}, isUserActiveInOrg=${isUserActiveInOrg}, path=${currentPath}`);
    
    // Check for user disabled in organization state
    if (isUserActiveInOrg === false && currentPath !== '/dashboard/account-disabled') {
      log('info', 'User is inactive in organization - redirecting to account-disabled page');
      router.push('/dashboard/account-disabled');
      return;
    }
    
    // Check for organization disabled state (for non-super admins)
    if (isOrgActive === false && !isSuperAdmin && currentPath !== '/dashboard/organization-disabled') {
      log('info', 'Organization is inactive and user is not SuperAdmin - redirecting to organization-disabled page');
      router.push('/dashboard/organization-disabled');
      return;
    }
    
    // Handle redirects back to dashboard when status changes to active
    if (isOrgActive === true && isUserActiveInOrg === true) {
      if (currentPath === '/dashboard/organization-disabled' || currentPath === '/dashboard/account-disabled') {
        log('info', 'Both organization and user are now active - redirecting to dashboard');
        router.push('/dashboard');
        return;
      }
    }
  }, [orgId, isOrgActive, isUserActiveInOrg, isSuperAdmin, router]);

  // Set up a singleton marker in the DOM
  useEffect(() => {
    if (typeof window === 'undefined') return; // Only run on client
    
    // Check if this instance should be the singleton
    const isNowSingleton = (() => {
      const marker = document.getElementById(CONFIG.SINGLETON_DOM_ID);
      
      if (!marker) {
        // No marker exists, create one and make this instance the singleton
        const newMarker = document.createElement('div');
        newMarker.id = CONFIG.SINGLETON_DOM_ID;
        newMarker.setAttribute('data-instance-id', INSTANCE_ID);
        newMarker.setAttribute('data-status', 'ready');
        newMarker.style.display = 'none';
        document.body.appendChild(newMarker);
        return true;
      }
      
      // Marker already exists, check if it belongs to this instance
      return marker.getAttribute('data-instance-id') === INSTANCE_ID;
    })();
    
    // Update the singleton status
    isSingletonRef.current = isNowSingleton;
    
    if (!isNowSingleton) {
      log('info', `Instance ${INSTANCE_ID} is NOT the singleton.`);
      return;
    }
    
    log('info', `Instance ${INSTANCE_ID} is now the active singleton.`);
    
    // Set up auth context change handler
    const handleAuthContextChanged = (event: AuthEvent) => {
      const currentStoreState = useAuthContextStore.getState();
      log('info', `Auth context changed - userId: ${event.userId}, reason: ${event.reason}`);
      
      // Only handle events for the current user
      if (event.userId !== currentStoreState.userId) {
        log('debug', 'Skipping auth context event - not for current user');
        return;
      }
      
      // Only handle events for the current org, if orgId is provided
      if (event.orgId && event.orgId !== currentStoreState.orgId) {
        log('debug', 'Skipping auth context event - not for current org');
        return;
      }
      
      // Navigation is handled by the effect that watches auth context state
      // No need to manually trigger navigation here
    };
    
    // Register auth context changed event handler
    emitter.on(AUTH_CONTEXT_CHANGED, handleAuthContextChanged);
    
    return () => {
      // Cleanup auth context changed event handler
      emitter.off(AUTH_CONTEXT_CHANGED, handleAuthContextChanged);
      
      // Clean up singleton marker if appropriate
      if (isSingletonRef.current) {
        const marker = document.getElementById(CONFIG.SINGLETON_DOM_ID);
        if (marker && marker.getAttribute('data-instance-id') === INSTANCE_ID) {
          marker.remove();
          log('info', `Singleton instance ${INSTANCE_ID} cleanup: Removing DOM marker.`);
        }
      }
    };
  }, [userId]); // Only re-run if userId changes

  return null;
}
