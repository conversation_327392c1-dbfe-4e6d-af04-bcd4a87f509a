/**
 * Event Bus - Centralized event system for organization context
 */

// Re-export event types directly from eventTypes.ts
export type {
  OrganizationContextEvent,
  UserRoleChangedEvent,
  MemberStatusChangedEvent,
  OrgNameChangedEvent,
  AnyMemberChangeEvent,
} from '@/lib/eventTypes'

// Import emitter first so we can use it in type definitions
import { emitter } from './emitter'

// Re-export the Events type and emitter from emitter.ts
export type { Events } from './emitter'
export { emitter }


// Create backward compatibility types/objects for legacy code
// @deprecated - Use emitter directly instead
export type OrganizationEventBus = typeof emitter
// @deprecated - Use emitter directly instead
export const organizationEventBus = emitter

// Re-export remaining hook functions
export { useAllMemberChanges } from './hooks/useAllMemberChanges'
export { useOrganizationEvents, useAllOrganizationEvents } from './hooks/useOrganizationEvents'

// Legacy hooks removed - use AUTH_CONTEXT_CHANGED event directly via useAuthContextEvents
// - useOrganizationContextEvents (removed)
// - useUserRoleEvents (removed)
// - useMemberStatusEvents (removed)
// - useOrgNameEvents (removed)