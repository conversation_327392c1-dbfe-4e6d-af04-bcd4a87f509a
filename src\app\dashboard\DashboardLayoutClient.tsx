"use client";

import React from "react";
import { Sidebar } from "@/components/Sidebar";
import { TopBar } from "@/components/TopBar";
import { MobileSidebar } from "@/components/MobileSidebar";
import { DashboardProvider } from "@/components/providers/dashboard-provider";
import type { User } from "@supabase/supabase-js";
import type { Organization } from "@/types/organization";

interface DashboardLayoutClientProps {
  user: User;
  userRole: number | null;
  avatarUrl: string | null;
  organizations: Organization[];
  activeOrganization: Organization;
  children: React.ReactNode;
}

export default function DashboardLayoutClient({
  user,
  userRole,
  avatarUrl,
  organizations,
  activeOrganization,
  children,
}: DashboardLayoutClientProps) {
  // Sidebar state (local to client)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(false);
  const toggleSidebar = React.useCallback(() => {
    setIsSidebarCollapsed((prev) => !prev);
  }, []);

  return (
    <DashboardProvider
      user={user}
      userRole={userRole}
      avatarUrl={avatarUrl}
      organizations={organizations}
      activeOrganization={activeOrganization}
    >
      <div className="h-screen bg-gray-50 dark:bg-[#050520]">
        <MobileSidebar />
        <div className="hidden lg:block">
          <Sidebar isSidebarCollapsed={isSidebarCollapsed} />
        </div>
        <div
          id="main-content"
          className="flex-1 flex flex-col transition-all duration-300 data-[collapsed=true]:lg:ml-16 data-[collapsed=false]:lg:ml-64"
          data-collapsed={isSidebarCollapsed}
        >
          <TopBar
            isSidebarCollapsed={isSidebarCollapsed}
            toggleSidebar={toggleSidebar}
            user={user}
            activeOrganization={activeOrganization}
          />
          <main className="flex-1 px-6 h-full overflow-x-hidden overflow-y-auto mt-16">
            <div className="w-full mx-auto px-6 py-8 relative">
              {/* No conditional rendering needed here - middleware has already handled redirects */}
              {children}
            </div>
          </main>
        </div>
      </div>
    </DashboardProvider>
  );
} 