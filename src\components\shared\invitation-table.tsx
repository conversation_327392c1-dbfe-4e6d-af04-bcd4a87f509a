"use client";

import { useState, useMemo, memo } from "react";
import { RoleKey } from "@/lib/rbac/rbac-utils";
import { useRbacPermission } from "@/hooks/use-rbac-permission";
import { useAuthContextStore } from "@/stores/useAuthContextStore";
import { useOrganizationsList } from "@/hooks/use-organizations-list";
import { format } from "date-fns";
import { roleUtils } from "@/lib/rbac/role-utils";
import {
  Search, PlusCircle, Loader2, Trash2, RefreshCw, MoreHorizontal,
  ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


// Invitation table specific types
export interface InvitationTableBusinessRules {
  inviteMinRole?: RoleKey;
  deleteMinRole?: RoleKey;
  resendMinRole?: RoleKey;
  allowedRoles?: RoleKey[];
  maxAssignableRole?: RoleKey;
}

export interface InvitationTablePagePermissions {
  canInvite?: boolean;
  canDelete?: boolean;
  canResend?: boolean;
}

export interface EmailInvitation {
  id: string;
  org_id: string;
  email: string;
  role_id: number;
  status: 'created' | 'sent' | 'delivered' | 'failed' | 'accepted' | 'declined' | 'expired' | 'added';
  created_at: string;
  expires_at: string;
  personal_message?: string;
  resend_email_id?: string;
  invited_by?: string; // UUID of the user who sent the invitation
  organizations?: { org_name: string }[];
  invited_by_profile?: { full_name: string }[]; // Profile of the user who sent the invitation (Supabase returns array)
}

interface InvitationTableProps {
  organizationIdScope: 'CURRENT_CONTEXT' | 'ALL_WITH_SELECTOR' | string;
  businessRules?: InvitationTableBusinessRules;
  showOrganizationColumn?: boolean;
  onInviteUser?: () => void;
  tableTitle?: string;
  invitations: EmailInvitation[];
  isLoading?: boolean;
  onDeleteInvitation?: (invitationId: string) => void;
  onResendInvitation?: (invitationId: string) => void;
  onOrganizationChange?: (orgId: string) => void;
}

interface TablePaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalInvitations: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  availablePageSizes?: number[];
}

function TablePagination({
  currentPage,
  totalPages,
  pageSize,
  totalInvitations,
  onPageChange,
  onPageSizeChange,
  availablePageSizes = [10, 20, 50, 100],
}: TablePaginationProps) {
  if (totalPages === 0) return null;

  return (
    <div className="flex items-center justify-between px-2 py-4 border-t bg-white dark:bg-gray-800 rounded-b-lg shadow">
      <div className="flex-1 text-sm text-gray-600 dark:text-gray-300">
        Showing {Math.min((currentPage - 1) * pageSize + 1, totalInvitations)} - {Math.min(currentPage * pageSize, totalInvitations)} of {totalInvitations} invitations
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Rows per page</p>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize.toString()} />
            </SelectTrigger>
            <SelectContent side="top">
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium text-gray-600 dark:text-gray-300">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export const InvitationTable = memo(function InvitationTable({
  organizationIdScope,
  businessRules,
  showOrganizationColumn = true,
  onInviteUser,
  tableTitle = "Email Invitations",
  invitations,
  isLoading = false,
  onDeleteInvitation,
  onResendInvitation,
  onOrganizationChange,
}: InvitationTableProps) {
  const rbac = useRbacPermission();
  const { orgId: currentOrgId } = useAuthContextStore();
  const { organizations: organizationsList } = useOrganizationsList({});

  // Local state for pagination and search
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedOrgId, setSelectedOrgId] = useState<string>(
    organizationIdScope === 'ALL_WITH_SELECTOR' ? currentOrgId || 'all' : currentOrgId || ''
  );

  // Calculate permissions based on business rules
  const pagePermissions = useMemo((): InvitationTablePagePermissions => {
    if (!businessRules) {
      return { canInvite: false, canDelete: false, canResend: false };
    }

    return {
      canInvite: businessRules.inviteMinRole ? rbac.checkPermission({ crMinRole: businessRules.inviteMinRole }) : false,
      canDelete: businessRules.deleteMinRole ? rbac.checkPermission({ rdMinRole: businessRules.deleteMinRole }) : false,
      canResend: businessRules.resendMinRole ? rbac.checkPermission({ cruMinRole: businessRules.resendMinRole }) : false,
    };
  }, [businessRules, rbac]);

  // Filter and paginate invitations
  const filteredInvitations = useMemo(() => {
    if (!searchQuery) return invitations;

    const query = searchQuery.toLowerCase();
    return invitations.filter(invitation => {
      const roleName = roleUtils.getRoleName(invitation.role_id as any).toLowerCase();
      return invitation.email.toLowerCase().includes(query) ||
        invitation.organizations?.[0]?.org_name.toLowerCase().includes(query) ||
        roleName.includes(query) ||
        invitation.invited_by_profile?.[0]?.full_name.toLowerCase().includes(query);
    });
  }, [invitations, searchQuery]);

  const paginatedInvitations = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredInvitations.slice(startIndex, startIndex + pageSize);
  }, [filteredInvitations, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredInvitations.length / pageSize) || 1;

  const getStatusBadge = (status: EmailInvitation['status']) => {
    const statusConfig = {
      created: { label: 'Created', variant: 'secondary' as const },
      sent: { label: 'Sent', variant: 'default' as const },
      delivered: { label: 'Delivered', variant: 'default' as const },
      failed: { label: 'Failed', variant: 'destructive' as const },
      accepted: { label: 'Accepted', variant: 'default' as const },
      declined: { label: 'Declined', variant: 'secondary' as const },
      expired: { label: 'Expired', variant: 'secondary' as const },
      added: { label: 'Added', variant: 'default' as const },
    };

    const config = statusConfig[status] || { label: status, variant: 'secondary' as const };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const formatDateSafe = (dateString: string | undefined | null): string => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM d, yyyy 'at' h:mm a");
    } catch {
      return "Invalid Date";
    }
  };

  const canResendInvitation = (invitation: EmailInvitation) => {
    return pagePermissions.canResend && ['failed', 'expired'].includes(invitation.status);
  };

  const canDeleteInvitation = (invitation: EmailInvitation) => {
    return pagePermissions.canDelete && !['accepted', 'added'].includes(invitation.status);
  };

  const handleOrgFilterChange = (orgId: string) => {
    setSelectedOrgId(orgId);
    if (onOrganizationChange) {
      onOrganizationChange(orgId);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-200">{tableTitle}</h2>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none" />
            <Input
              type="search"
              placeholder="Search invitations..."
              className="w-full sm:w-[300px] pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {organizationIdScope === 'ALL_WITH_SELECTOR' && (
            <Select value={selectedOrgId} onValueChange={handleOrgFilterChange}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Select Organization" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Organizations</SelectItem>
                {organizationsList?.map((org) => (
                  <SelectItem key={org.id} value={org.id}>{org.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          {pagePermissions.canInvite && onInviteUser && (
            <Button onClick={onInviteUser}>
              <PlusCircle className="h-4 w-4 mr-2" /> Send Invitation
            </Button>
          )}
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Email</TableHead>
              {showOrganizationColumn && <TableHead>Organization</TableHead>}
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Invited By</TableHead>
              <TableHead>Sent</TableHead>
              <TableHead>Expires</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading && paginatedInvitations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showOrganizationColumn ? 8 : 7} className="h-24 text-center">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : paginatedInvitations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showOrganizationColumn ? 8 : 7} className="h-24 text-center">
                  No invitations found.
                </TableCell>
              </TableRow>
            ) : (
              paginatedInvitations.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium">{invitation.email}</span>
                      {invitation.personal_message && (
                        <span className="text-sm text-gray-500 truncate max-w-[200px]">
                          "{invitation.personal_message}"
                        </span>
                      )}
                    </div>
                  </TableCell>
                  {showOrganizationColumn && (
                    <TableCell>{invitation.organizations?.[0]?.org_name || 'N/A'}</TableCell>
                  )}
                  <TableCell>{roleUtils.getRoleName(invitation.role_id as any)}</TableCell>
                  <TableCell>{getStatusBadge(invitation.status)}</TableCell>
                  <TableCell>{invitation.invited_by_profile?.[0]?.full_name || 'N/A'}</TableCell>
                  <TableCell>{formatDateSafe(invitation.created_at)}</TableCell>
                  <TableCell>{formatDateSafe(invitation.expires_at)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {canResendInvitation(invitation) && onResendInvitation && (
                          <DropdownMenuItem onClick={() => onResendInvitation(invitation.id)}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Resend
                          </DropdownMenuItem>
                        )}
                        {canDeleteInvitation(invitation) && onDeleteInvitation && (
                          <DropdownMenuItem
                            onClick={() => onDeleteInvitation(invitation.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {filteredInvitations.length > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalInvitations={filteredInvitations.length}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
          />
        )}
      </div>
    </div>
  );
});
