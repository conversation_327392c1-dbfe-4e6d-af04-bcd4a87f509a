'use client'

import type { MemberStatusChangedEvent } from '@/lib/eventTypes'
import { useBusEvent } from '@/lib/useBusEvent'

/**
 * Subscribe to member status change events for a specific user
 */
export function useMemberStatusEvents(
  userId: string | null,
  handler: (event: MemberStatusChangedEvent) => void
): void {
  useBusEvent(
    'member:status:changed',
    (payload) => {
      if (!userId || payload.userId !== userId) return
      handler(payload)
    },
    [userId, handler]
  )
}
