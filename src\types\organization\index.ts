import type { Organization } from './Organization';
import type { OrganizationMember, OrganizationMemberBasic } from './OrganizationMember';
import type { UserPersonalInfo } from '../user';
import { OrganizationMembershipResponse } from './OrganizationMembershipResponse'

/**
 * Organization with role information
 */
export interface OrganizationWithRole extends Organization {
  role_id?: number;
  role_name?: string;
}

/**
 * Organization member with organization data
 */
export interface OrganizationMemberWithOrg extends OrganizationMember {
  organization: Organization;
  role_name?: string;
}

/**
 * Full organization member with user profile data
 * Used in admin interfaces and detailed views
 */
export interface OrganizationMemberFull extends OrganizationMember {
  // Organization data
  organization: Organization;
  role_name: string;
  
  // User data
  user_full_name: string;
  user_email?: string;
  avatar_url?: string | null;
  updated_by_name?: string | null;
  
  // Personal information
  personal_info?: UserPersonalInfo | null;
}

// Re-export for convenience
export type { 
  Organization, 
  OrganizationMember,
  OrganizationMemberBasic
}; 

export * from './Organization'
export * from './OrganizationMember'
export type { OrganizationMembershipResponse } 