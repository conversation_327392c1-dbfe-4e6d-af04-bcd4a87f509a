'use server'

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  try {
    // Await the cookies() promise to get the cookie store.
    const cookieStore = await cookies()

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Missing Supabase environment variables')
      throw new Error('Missing required environment variables for Supabase client')
    }

    return createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        // Use the new cookie methods instead of the deprecated ones.
        cookies: {
          getAll() {
            try {
              // Retrieve all cookies from the store.
              // The Next.js cookie API returns an array of cookie objects.
              return cookieStore.getAll().map(({ name, value }) => ({ name, value }))
            } catch (error) {
              console.error('Error getting cookies in Supabase client:', error)
              return []
            }
          },
          setAll(cookiesToSet: Array<{ name: string; value: string; options?: Parameters<typeof cookieStore.set>[2] }>) {
            try {
              cookiesToSet.forEach(({ name, value, options }) => {
                if (options) {
                  cookieStore.set(name, value, options)
                } else {
                  cookieStore.set(name, value)
                }
              })
            } catch (error) {
              console.error('Error setting cookies in Supabase client:', error)
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )
  } catch (error) {
    console.error('Error creating Supabase client:', error)
    throw error
  }
}