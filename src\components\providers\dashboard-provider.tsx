"use client";

import React, { useEffect, useState } from "react";
import type { DashboardProviderProps } from "@/types/components/providers/DashboardProviderProps";
import { toast } from '@/hooks/use-toast'
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { useBusEvent } from "@/lib/useBusEvent";
import { useAuthContextEvents } from '@/hooks/use-auth-context-events';
import { useAuthEventEmitters } from '@/lib/eventBus/hooks/useAuthEvents'; // Centralized auth context events

// Zustand-powered hook for consumers
export function useDashboard() {
  return useAuthContextStore();
}

export function DashboardProvider({
  children,
  user,
  userRole,
  avatarUrl,
  activeOrganization,
}: DashboardProviderProps) {
  const [isSidebarCollapsed] = useState(false) // Sidebar state can remain local

  // Actions from the new useAuthContextStore
  const updateAuthContextFull = useAuthContextStore(state => state.updateFullContext);
  const startOptimistic = useAuthContextStore(state => state.startOptimisticUpdate);
  const finishOptimistic = useAuthContextStore(state => state.finishOptimisticUpdate);
  const revertOptimistic = useAuthContextStore(state => state.revertOptimisticUpdate);
  const setAuthContextPartially = useAuthContextStore(state => state.setAuthContext);


  // Hydrate Zustand (useAuthContextStore) on mount and whenever server props change
  useEffect(() => {
    if (user && activeOrganization) {
      updateAuthContextFull({
        userId: user.id,
        userEmail: user.email ?? null,
        userFullName: user.user_metadata?.full_name ?? user.email ?? null,
        avatarUrl,
        orgId: activeOrganization.id,
        activeOrgName: activeOrganization.name,
        roleId: userRole,
        isUserActiveInOrg: activeOrganization.org_member_is_active === true,
        isOrgActive: activeOrganization.isActive === true,
      });
    }
  }, [
    user,
    userRole,
    avatarUrl,
    activeOrganization,
    updateAuthContextFull
  ]);

  // Listen for optimistic context events (now for useAuthContextStore)
  // Optimistic context start
  useBusEvent(
    "organization:context:optimistic",
    (event) => {
      startOptimistic(event.orgId, event.role);
    },
    [startOptimistic]
  );

  // Optimistic context done
  useBusEvent(
    "organization:context:optimistic:done",
    () => {
      finishOptimistic();
    },
    [finishOptimistic]
  );

  // Optimistic context error
  useBusEvent(
    "organization:context:optimistic:error",
    () => {
      revertOptimistic('Organization switch failed');
      toast({ title: 'Organization switch failed', description: 'Could not switch organization. Reverting.' });
    },
    [revertOptimistic, toast]
  );


  // Listen for client:authContextUpdated events and update Zustand state
  useBusEvent(
    "client:authContextUpdated",
    (event) => {
      const updatePayload: Parameters<typeof setAuthContextPartially>[0] = {};

      if (typeof event.isUserActive === 'boolean') {
        updatePayload.isUserActiveInOrg = event.isUserActive;
      }
      if (typeof event.isOrgActive === 'boolean') {
        updatePayload.isOrgActive = event.isOrgActive;
      }
      if (typeof event.newRoleId === 'number') {
        updatePayload.roleId = event.newRoleId;
      }

      if (Object.keys(updatePayload).length > 0) {
        console.debug('[DashboardProvider] [LOG] client:authContextUpdated handler fired:', event, 'Updating AuthContextStore with:', updatePayload);
        setAuthContextPartially(updatePayload);
      }
    },
    [setAuthContextPartially]
  );


  // Sidebar logic (local only) - remains unchanged
  useEffect(() => {
    if (typeof window === "undefined") return;
    const mainContent = document.getElementById("main-content");
    if (mainContent) {
      mainContent.setAttribute("data-collapsed", isSidebarCollapsed.toString());
    }
  }, [isSidebarCollapsed]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        // No-op
      }
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const optimisticLoading = useAuthContextStore(state => state.optimisticLoading);

  // Use the centralized auth context events hook to handle all AUTH_CONTEXT_CHANGED events
  // This replaces individual event handlers for role changes, status changes, etc.
  useAuthContextEvents();

  // Mount the auth event emitters to convert legacy events to AUTH_CONTEXT_CHANGED
  // This ensures organization status changes trigger auth context refreshes
  useAuthEventEmitters();

  return (
    <>
      {optimisticLoading && (
        <div className="fixed top-0 left-0 w-full z-50 bg-yellow-100 text-yellow-900 text-center py-2 shadow">
          <span>Switching organization...</span>
        </div>
      )}
      {/*
        DashboardEventManager is now mounted at the root level via GlobalDashboardProvider
        This ensures it's only mounted once and prevents duplicate subscriptions
      */}
      {/* Render the children passed from the layout */}
      {children}
    </>
  );
}
