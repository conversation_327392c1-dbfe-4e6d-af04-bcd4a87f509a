import { withRbacPermission } from '@/lib/rbac/permissions-server'
import { createClient } from '@/lib/supabase/server'
import { getUserOrganizations } from '@/lib/organization-utils-server'
import { OrganizationTable } from '@/app/dashboard/developer/organizations/organization-table'

export default async function OrganizationPage() {
  return withRbacPermission(
    { rRoles: ["superAdmin", "supportAdmin", "orgAdmin"] },
    { redirectTo: "/dashboard" }
  )(async () => {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return <></>
    }

    const paginatedResult = await getUserOrganizations(user.id, true)
    const organizationsForTable = paginatedResult.organizations;

    return <OrganizationTable initialOrganizations={organizationsForTable} />
  })
}
