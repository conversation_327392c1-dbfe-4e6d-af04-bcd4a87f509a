-- Add migration script to create/update the get_user_permissions_matrix function
-- This function efficiently retrieves the user's permissions for an organization

-- Drop the function if it already exists (for idempotent migrations)
DROP FUNCTION IF EXISTS get_user_permissions_matrix(p_user_id UUID, p_org_id UUID);

-- Create the function
CREATE OR REPLACE FUNCTION get_user_permissions_matrix(
  p_user_id UUID,
  p_org_id UUID DEFAULT NULL
)
RETURNS TABLE (
  org_id UUID,
  org_name TEXT,
  role_id INT,
  role_name TEXT,
  is_org_active BOOLEAN,
  is_member_active BOOLEAN,
  can_create BO<PERSON>EA<PERSON>,
  can_update BOOLEAN,
  can_delete BOOLEAN,
  can_view BOOLEAN,
  is_admin BOOLEAN,
  is_super_admin BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_default_org_id UUID;
BEGIN
  -- If org_id is not provided, get the active organization
  IF p_org_id IS NULL THEN
    SELECT om.org_id INTO v_default_org_id
    FROM organization_members om
    WHERE om.user_id = p_user_id
      AND om.is_current_context = TRUE
      AND om.org_member_is_active = TRUE;
      
    -- If there's still no active org, get the default one
    IF v_default_org_id IS NULL THEN
      SELECT om.org_id INTO v_default_org_id
      FROM organization_members om
      WHERE om.user_id = p_user_id
        AND om.is_default_org = TRUE
        AND om.org_member_is_active = TRUE;
    END IF;
    
    -- Use the resolved org_id
    p_org_id := v_default_org_id;
  END IF;
  
  -- Fetch and return the user's permissions
  RETURN QUERY
  SELECT 
    o.id AS org_id,
    o.org_name,
    om.org_member_role AS role_id,
    r.role_name,
    o.is_active AS is_org_active,
    om.org_member_is_active AS is_member_active,
    -- Compute permissions based on role
    CASE 
      -- Super admin and support admin can create in any org
      WHEN om.org_member_role IN (1, 2) THEN TRUE
      -- Org admin and org member can create in active orgs
      WHEN om.org_member_role IN (3, 4) AND o.is_active = TRUE THEN TRUE
      ELSE FALSE
    END AS can_create,
    
    CASE 
      -- Super admin and support admin can update in any org
      WHEN om.org_member_role IN (1, 2) THEN TRUE
      -- Org admin and org member can update in active orgs
      WHEN om.org_member_role IN (3, 4) AND o.is_active = TRUE THEN TRUE
      ELSE FALSE
    END AS can_update,
    
    CASE 
      -- Super admin and support admin can delete in any org
      WHEN om.org_member_role IN (1, 2) THEN TRUE
      -- Only org admin can delete in active orgs
      WHEN om.org_member_role = 3 AND o.is_active = TRUE THEN TRUE
      ELSE FALSE
    END AS can_delete,
    
    CASE 
      -- Super admin can view in any org
      WHEN om.org_member_role = 1 THEN TRUE
      -- Others can view only in active orgs
      WHEN o.is_active = TRUE THEN TRUE
      ELSE FALSE
    END AS can_view,
    
    -- Is admin (role_id <= 3)
    om.org_member_role <= 3 AS is_admin,
    
    -- Is super admin (role_id = 1)
    om.org_member_role = 1 AS is_super_admin
  FROM 
    organizations o
    INNER JOIN organization_members om ON o.id = om.org_id
    INNER JOIN roles r ON om.org_member_role = r.role_id
  WHERE 
    om.user_id = p_user_id
    AND (p_org_id IS NULL OR om.org_id = p_org_id);
END;
$$;

-- Add a function to efficiently check a single permission
DROP FUNCTION IF EXISTS check_user_permission(p_user_id UUID, p_org_id UUID, p_permission TEXT);

CREATE OR REPLACE FUNCTION check_user_permission(
  p_user_id UUID,
  p_org_id UUID,
  p_permission TEXT -- 'create', 'update', 'delete', 'view'
)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_result BOOLEAN;
BEGIN
  EXECUTE FORMAT('
    SELECT 
      CASE 
        WHEN p.is_super_admin THEN TRUE
        WHEN NOT p.is_org_active AND NOT p.is_super_admin THEN FALSE
        WHEN NOT p.is_member_active THEN FALSE
        ELSE p.can_%I 
      END
    FROM get_user_permissions_matrix($1, $2) p
    LIMIT 1
  ', p_permission)
  INTO v_result
  USING p_user_id, p_org_id;

  -- Default to FALSE if no result
  RETURN COALESCE(v_result, FALSE);
END;
$$;

-- Grant access to authenticated users
ALTER FUNCTION get_user_permissions_matrix(UUID, UUID) SECURITY DEFINER;
ALTER FUNCTION check_user_permission(UUID, UUID, TEXT) SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_user_permissions_matrix(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_user_permission(UUID, UUID, TEXT) TO authenticated;

-- Create an index to optimize permission lookups
CREATE INDEX IF NOT EXISTS idx_organization_members_user_role ON organization_members(user_id, org_id, org_member_role);
CREATE INDEX IF NOT EXISTS idx_organization_members_context ON organization_members(user_id, is_current_context);
CREATE INDEX IF NOT EXISTS idx_organizations_active ON organizations(is_active);

COMMENT ON FUNCTION get_user_permissions_matrix IS 'Efficiently retrieves all permissions for a user in an organization.';
COMMENT ON FUNCTION check_user_permission IS 'Efficiently checks a specific permission for a user in an organization.'; 