'use server'

import { createClient } from '@/lib/supabase/server'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'
import { z } from 'zod'

const deleteSchema = z.object({
  invitationId: z.string().uuid(),
})

export interface DeleteInvitationResult {
  success: boolean
  error?: string
}

/**
 * Delete an email invitation
 */
export async function deleteEmailInvitation(
  invitationId: string
): Promise<DeleteInvitationResult> {
  try {
    // 1. Validate input
    const validatedData = deleteSchema.parse({ invitationId })
    
    // 2. Get current user
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'Authentication required' }
    }
    
    // 3. Get invitation details
    const { data: invitation, error: inviteError } = await supabase
      .from('email_invitations')
      .select('id, org_id, status')
      .eq('id', validatedData.invitationId)
      .single()
    
    if (inviteError || !invitation) {
      return { success: false, error: 'Invitation not found' }
    }
    
    // 4. Check if invitation can be deleted (not accepted or added)
    if (['accepted', 'added'].includes(invitation.status)) {
      return { success: false, error: 'Cannot delete accepted invitations' }
    }
    
    // 5. Check RBAC permissions for the organization
    const hasPermission = await checkRbacPermission({
      rdMinRole: 'orgMember', // Minimum permission to delete
      resourceOrgId: invitation.org_id
    })
    
    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions to delete invitation' }
    }
    
    // 6. Delete the invitation
    const { error: deleteError } = await supabase
      .from('email_invitations')
      .delete()
      .eq('id', invitation.id)
    
    if (deleteError) {
      console.error('Failed to delete invitation:', deleteError)
      return { success: false, error: 'Failed to delete invitation' }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Delete invitation error:', error)
    
    if (error instanceof z.ZodError) {
      return { success: false, error: 'Invalid invitation ID' }
    }
    
    return { success: false, error: 'Internal server error' }
  }
}
