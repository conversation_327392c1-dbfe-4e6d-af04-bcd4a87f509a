# Console Debugging & Error Resolution Guide

## 🔍 **Common Console Issues & Solutions**

### **Issue 1: Browser Console Jumping to Sources Panel**

#### **Symptoms**
- Browser DevTools automatically opens Sources panel
- Execution pauses with "debugger" statement
- Happens when visiting login page

#### **Root Causes**
1. **DevTools Settings**: "Pause on exceptions" or "Pause on caught exceptions" enabled
2. **Event Bus Debugger**: Development mitt-debugger attachment
3. **Actual debugger statements**: `debugger;` in code

#### **Solutions**

##### **A. Check DevTools Settings**
1. Open Chrome DevTools (F12)
2. Go to Sources tab
3. Look for pause icons in the right panel:
   - 🔵 **Pause on exceptions** - should be OFF
   - ⏸️ **Pause on caught exceptions** - should be OFF
4. Click to disable if enabled

##### **B. Disable Development Debuggers**
The mitt-debugger is now controlled by environment variables:

```env
# Add to .env.local to enable debugging (optional)
NEXT_PUBLIC_ENABLE_MITT_DEBUGGER=true
NEXT_PUBLIC_ENABLE_EVENT_LOGGING=true
```

**Default behavior**: Debuggers are disabled to prevent console jumping.

##### **C. Manual Debugging (When Needed)**
If you need to debug events:
```javascript
// In browser console
window.__appEmitter // Access the event emitter
window.__mittDebug() // Show all event listeners (if debugger enabled)
```

### **Issue 2: Cloudflare Turnstile Console Messages**

#### **Expected Messages (Normal Behavior)**
```
Request for the Private Access Token challenge.
The next request for the Private Access Token challenge may return a 401...
The resource https://challenges.cloudflare.com/cdn-cgi/challenge-platform/h/g/cmg/1 was preloaded...
[Violation] Avoid using document.write().
```

#### **Why These Occur**
- **Private Access Token**: Cloudflare's advanced bot detection
- **Preload warnings**: Cloudflare optimizations not used immediately
- **document.write() violations**: Cloudflare's legacy compatibility code
- **401 Unauthorized**: Normal challenge validation process

#### **Are These Errors?**
❌ **NO** - These are normal Cloudflare Turnstile operations
✅ **Expected behavior** for CAPTCHA functionality
🔧 **Cannot be eliminated** - they're from Cloudflare's infrastructure

#### **What You Can Do**
1. **Ignore these messages** - they don't affect functionality
2. **Filter console** - Use browser console filters to hide Cloudflare messages
3. **Focus on actual errors** - Look for red error messages from your code

### **Issue 3: Rate Limiting & CAPTCHA Timing**

#### **Symptoms**
- CAPTCHA tokens expire quickly
- Timing mismatches between requests
- 401 errors from Cloudflare

#### **Solutions Implemented**
```typescript
// Enhanced CAPTCHA configuration
options={{
  theme: 'light',
  size: 'normal',
  action: 'magic-link-request',
  cData: 'auth-form',
}}

// Auto-retry logic for network errors
if (error.includes('network') || error.includes('timeout')) {
  setIsRetrying(true);
  setTimeout(() => {
    setIsRetrying(false);
    setCaptchaError(null);
  }, 3000);
}

// Enhanced error handling
const handleError = (error: string) => {
  if (error !== 'expired') {
    console.warn('Turnstile error:', error);
  }
  onError?.(error);
};
```

## 🛠️ **Development Environment Setup**

### **Clean Console Experience**
For a cleaner development experience, keep these environment variables **unset**:

```env
# .env.local - DO NOT ADD THESE unless debugging
# NEXT_PUBLIC_ENABLE_MITT_DEBUGGER=true
# NEXT_PUBLIC_ENABLE_EVENT_LOGGING=true
```

### **When to Enable Debugging**
Only enable when you need to debug specific issues:

1. **Event System Issues**: Set `NEXT_PUBLIC_ENABLE_EVENT_LOGGING=true`
2. **Event Listener Problems**: Set `NEXT_PUBLIC_ENABLE_MITT_DEBUGGER=true`
3. **Production Debugging**: Keep both disabled

### **Browser Console Filters**
To hide Cloudflare noise:

1. Open DevTools Console
2. Click the filter icon (funnel)
3. Add negative filters:
   - `-challenges.cloudflare.com`
   - `-Private Access Token`
   - `-document.write`
   - `-preloaded using link`

## 🎯 **Error Categorization**

### **🟢 Safe to Ignore**
- Cloudflare Turnstile messages
- Private Access Token requests
- Preload resource warnings
- document.write() violations from Cloudflare

### **🟡 Development Only**
- Event bus initialization messages
- Mitt debugger attachments
- Auth context rehydration logs

### **🔴 Actual Errors (Fix These)**
- TypeScript compilation errors
- Network request failures (not from Cloudflare)
- React component errors
- Authentication failures (not CAPTCHA related)

## 🔧 **Troubleshooting Steps**

### **Step 1: Identify Error Source**
1. Check if error comes from `challenges.cloudflare.com` → Ignore
2. Check if error comes from your code → Investigate
3. Check if it's a development log → Consider disabling

### **Step 2: Browser DevTools Check**
1. Disable "Pause on exceptions"
2. Clear console and reload
3. Filter out Cloudflare messages
4. Focus on remaining errors

### **Step 3: Environment Variables**
1. Check `.env.local` for debug flags
2. Remove debug flags for cleaner console
3. Only enable when actively debugging

### **Step 4: CAPTCHA Issues**
1. Check network tab for 401s from Cloudflare → Normal
2. Check for actual authentication errors → Fix these
3. Verify CAPTCHA tokens are being sent → Should work automatically

## 📋 **Quick Reference**

### **Normal Cloudflare Messages**
```
✅ "Request for the Private Access Token challenge"
✅ "The next request...may return a 401"
✅ "The resource...was preloaded using link preload"
✅ "[Violation] Avoid using document.write()"
```

### **Problematic Messages**
```
❌ "TypeError: Cannot read property..."
❌ "ReferenceError: ... is not defined"
❌ "Failed to fetch" (from your API)
❌ "Uncaught Error in component..."
```

### **Environment Variables**
```env
# Clean development (recommended)
# No debug variables set

# Debug mode (when needed)
NEXT_PUBLIC_ENABLE_MITT_DEBUGGER=true
NEXT_PUBLIC_ENABLE_EVENT_LOGGING=true
```

The console should now be much cleaner with fewer distracting messages and no automatic debugger pausing! 🚀
