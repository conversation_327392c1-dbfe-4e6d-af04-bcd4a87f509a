#!/bin/bash

# Apply migrations to fix recursive policies
echo "Applying migrations to fix recursive policies..."

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Please install it first."
    echo "Visit https://supabase.com/docs/guides/cli for installation instructions."
    exit 1
fi

# Check if SUPABASE_DB_URL is set
if [ -z "$SUPABASE_DB_URL" ]; then
    echo "SUPABASE_DB_URL environment variable is not set."
    echo "Please set it to your Supabase database URL."
    exit 1
fi

# Apply the migrations
echo "Applying migration: fix_recursive_policies.sql"
supabase db push --db-url "$SUPABASE_DB_URL" supabase/migrations/20240601000000_fix_recursive_policies.sql

echo "Applying migration: fix_organizations_policies.sql"
supabase db push --db-url "$SUPABASE_DB_URL" supabase/migrations/20240601000001_fix_organizations_policies.sql

echo "Applying migration: add_user_has_organizations_function.sql"
supabase db push --db-url "$SUPABASE_DB_URL" supabase/migrations/20240601000002_add_user_has_organizations_function.sql

echo "Applying migration: create_organization_invites_table.sql"
supabase db push --db-url "$SUPABASE_DB_URL" supabase/migrations/20240601000003_create_organization_invites_table.sql

echo "Applying migration: add_superadmin_invites_policy.sql"
supabase db push --db-url "$SUPABASE_DB_URL" supabase/migrations/20240601000004_add_superadmin_invites_policy.sql

# Deploy Edge Function
echo "Deploying Edge Function: process-invite-code"
supabase functions deploy process-invite-code

echo "Migrations and functions deployed successfully!"
echo "Please restart your application to see the changes." 