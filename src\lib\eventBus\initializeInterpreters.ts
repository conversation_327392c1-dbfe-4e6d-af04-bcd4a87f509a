import { registerRoleInterpreter } from './channels/role';
import { registerStatusInterpreter } from './channels/status';
import { registerNameInterpreter } from './channels/name';
import { registerContextInterpreter } from './channels/context';
import { registerAllMembersInterpreter } from './channels/allMembers';
import { registerOrganizationStatusInterpreter } from './channels/organizationStatusInterpreter';

// Add a module-level flag to track initialization
let eventSystemInitialized = false

export function initializeEventSystem(): void {
  // Bail early if already initialized
  if (eventSystemInitialized) {
    return
  }

  if (process.env.NEXT_PUBLIC_ENABLE_EVENT_LOGGING === 'true') {
    console.log('[EventBus] Initializing event interpreters...')
  }

  // Mark as initialized immediately to prevent concurrent initializations
  eventSystemInitialized = true

  try {
    registerRoleInterpreter();
    registerStatusInterpreter();
    registerNameInterpreter();
    registerContextInterpreter();
    registerAllMembersInterpreter();
    registerOrganizationStatusInterpreter();

    if (process.env.NEXT_PUBLIC_ENABLE_EVENT_LOGGING === 'true') {
      console.log('[EventBus] All event interpreters registered successfully.');
    }
  } catch (error) {
    console.error('[EventBus] Error during interpreter registration:', error);
    // Depending on the error, you might want to set interpretersInitialized to false
    // or handle it in a way that allows a retry if applicable, though typically this is a one-shot deal.
  }
}