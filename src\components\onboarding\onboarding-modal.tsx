'use client'

import { useState } from 'react'
import { acceptInvitationAnonymous, declineInvitationAnonymous } from '@/app/actions/accept-invitation-anonymous'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Building, UserCheck, X } from 'lucide-react'
import { TurnstileCaptcha } from '@/components/auth/turnstile-captcha'

const nameSchema = z.string()
  .min(1, 'Name is required')
  .max(25, 'Name must be 25 characters or less')
  .regex(/^[\p{L}\-'\s]+$/u, 'Name contains invalid characters')

interface OnboardingModalProps {
  invite: {
    id: string
    org_id: string
    role_id: number
    email: string
    status: string
    expires_at: string
    personal_message?: string
    organizations: { org_name: string }[]
    roles: { role_name: string }[]
  }
  token: string
}

export default function OnboardingModal({ invite, token }: OnboardingModalProps) {
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [captchaToken, setCaptchaToken] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [fieldsEnabled, setFieldsEnabled] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [firstNameError, setFirstNameError] = useState<string | null>(null)
  const [lastNameError, setLastNameError] = useState<string | null>(null)

  const handleCaptchaSuccess = (token: string) => {
    setCaptchaToken(token)
    setFieldsEnabled(true) // Enable name fields after CAPTCHA
    setError(null)
  }

  const handleCaptchaError = () => {
    setCaptchaToken(null)
    setFieldsEnabled(false)
    setError('CAPTCHA verification failed. Please try again.')
  }

  const handleCaptchaExpire = () => {
    setCaptchaToken(null)
    setFieldsEnabled(false)
    setError('CAPTCHA expired. Please verify again.')
  }

  const validateName = (name: string, fieldName: string) => {
    try {
      nameSchema.parse(name.trim())
      return null
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || `Invalid ${fieldName.toLowerCase()}`
      }
      return `Invalid ${fieldName.toLowerCase()}`
    }
  }

  const handleFirstNameChange = (value: string) => {
    setFirstName(value)
    setFirstNameError(validateName(value, 'First name'))
  }

  const handleLastNameChange = (value: string) => {
    setLastName(value)
    setLastNameError(validateName(value, 'Last name'))
  }

  const canSubmit = () => {
    return (
      captchaToken &&
      firstName.trim() &&
      lastName.trim() &&
      !firstNameError &&
      !lastNameError &&
      !isSubmitting
    )
  }

  const handleAccept = async () => {
    if (!canSubmit()) return

    try {
      setIsSubmitting(true)
      setError(null)

      const result = await acceptInvitationAnonymous({
        token,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        email: invite.email,
      })

      if (result.success) {
        // Redirect to login page immediately
        window.location.href = '/auth/login?message=account-created'
      } else {
        setError(result.error || 'Failed to accept invitation')
      }
    } catch (error) {
      console.error('Accept invitation error:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDecline = async () => {
    try {
      setIsSubmitting(true)
      const result = await declineInvitationAnonymous(token)

      if (result.success) {
        window.location.href = '/?message=invitation-declined'
      } else {
        setError(result.error || 'Failed to decline invitation')
        setIsSubmitting(false)
      }
    } catch (error) {
      console.error('Decline invitation error:', error)
      setError('Failed to decline invitation. Please try again.')
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
            <Building className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <CardTitle className="text-xl font-bold">
          You've been invited!
        </CardTitle>
        <CardDescription className="text-center">
          <div className="space-y-1">
            <p>Join <strong>{invite.organizations[0]?.org_name}</strong></p>
            <p>as a <strong>{invite.roles[0]?.role_name}</strong></p>
          </div>
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {invite.personal_message && (
          <Alert>
            <AlertDescription className="italic">
              "{invite.personal_message}"
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <X className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* CAPTCHA - Always present, enables fields when completed */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">
            Security Verification
          </Label>
          <TurnstileCaptcha
            onSuccess={handleCaptchaSuccess}
            onError={handleCaptchaError}
            onExpire={handleCaptchaExpire}
          />
        </div>

        {/* Name fields - disabled until CAPTCHA completed */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium">
              First Name
            </Label>
            <Input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e) => handleFirstNameChange(e.target.value)}
              disabled={!fieldsEnabled}
              className={`${!fieldsEnabled ? 'bg-gray-100 dark:bg-gray-800' : ''} ${
                firstNameError ? 'border-red-500' : ''
              }`}
              maxLength={25}
              placeholder="Enter your first name"
            />
            {firstNameError && (
              <p className="text-sm text-red-600 dark:text-red-400">{firstNameError}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-sm font-medium">
              Last Name
            </Label>
            <Input
              id="lastName"
              type="text"
              value={lastName}
              onChange={(e) => handleLastNameChange(e.target.value)}
              disabled={!fieldsEnabled}
              className={`${!fieldsEnabled ? 'bg-gray-100 dark:bg-gray-800' : ''} ${
                lastNameError ? 'border-red-500' : ''
              }`}
              maxLength={25}
              placeholder="Enter your last name"
            />
            {lastNameError && (
              <p className="text-sm text-red-600 dark:text-red-400">{lastNameError}</p>
            )}
          </div>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          If you decline this invitation or leave this page, your invitation will expire.
        </div>
      </CardContent>

      <CardFooter className="flex space-x-3">
        <Button
          variant="outline"
          onClick={handleDecline}
          disabled={isSubmitting}
          className="flex-1"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : null}
          Decline
        </Button>
        <Button
          onClick={handleAccept}
          disabled={!canSubmit()}
          className="flex-1"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <UserCheck className="h-4 w-4 mr-2" />
          )}
          Accept Invitation
        </Button>
      </CardFooter>
    </Card>
  )
}
