import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'
import { revalidatePath } from 'next/cache'

// Define the shape of an organization
export interface Organization {
  id: string
  org_name: string
  is_active: boolean
  org_icon?: string | null
  created_at?: string
  updated_at?: string
}

export type CreateOrganizationRequest = {
  org_name: string
  is_active: boolean
  org_icon?: string | null
}

export type CreateOrganizationResponse = {
  success: boolean
  organization?: Organization
  message?: string
  error?: string
}

/**
 * API route handler for creating a new organization
 * Protected by RBAC to allow only superadmin and supportadmin roles
 */
export async function POST(req: NextRequest) {
  try {
    // Check RBAC permissions first
    const hasAccess = await checkRbacPermission(
      { crRoles: ['superAdmin', 'supportAdmin'] },
      { silentFail: true }
    )
    
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Insufficient permissions' },
        { status: 403 }
      )
    }
    
    // Parse the request body
    const body: CreateOrganizationRequest = await req.json()
    
    // Validate request data
    if (!body.org_name || body.org_name.trim() === '') {
      return NextResponse.json(
        { success: false, error: 'Organization name is required' },
        { status: 400 }
      )
    }
    
    if (typeof body.is_active !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'Organization status must be a boolean' },
        { status: 400 }
      )
    }
    
    // Connect to Supabase
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication error: Unable to get current user' },
        { status: 401 }
      )
    }
    
    // Create the organization
    const { data: newOrg, error: orgError } = await supabase
      .from('organizations')
      .insert([
        {
          org_name: body.org_name,
          is_active: body.is_active,
          org_icon: body.org_icon || null,
        },
      ])
      .select()
      .single()
      
    if (orgError || !newOrg) {
      return NextResponse.json(
        { success: false, error: `Failed to create organization: ${orgError?.message}` },
        { status: 500 }
      )
    }
    
    // Revalidate the organizations page path to refresh the data
    revalidatePath('/dashboard/admin/organization')
    
    // Return success response
    return NextResponse.json({
      success: true,
      organization: newOrg as Organization,
      message: 'Organization created successfully'
    })
    
  } catch (error: unknown) {
    console.error('Organization creation error:', error)
    
    // Return error response with safe error message handling
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    )
  }
}

// Default error handler for unauthorized requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Allow': 'POST, OPTIONS'
    }
  })
} 