export const STEP_TYPES = [
    "upload_passport_bio",
    "upload_visa_page",
    "upload_personal_info",
    "upload_arrival_stamp",
    "upload_departure_card",
    "upload_documents",
    "form_template",
    "check_preview",
    "display_requirements",
    "save_details",
    "download_pdf",
  ] as const;
  
  export type StepType = typeof STEP_TYPES[number];
  
  export interface Step {
    id: number;
    uid: string;
    type: StepType;
    title: string;
    description?: string;
    position: number;
    flowId: number;
  }
  
  export interface Flow {
    id: number;
    uid: string;
    name: string;
    description?: string;
    position: number;
    steps?: Step[];
  } 