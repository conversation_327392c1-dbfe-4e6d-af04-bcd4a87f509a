import {
  Html,
  Head,
  Body,
  Container,
  Text,
  Button,
  Section,
  Hr,
} from '@react-email/components';

interface MagicLinkEmailProps {
  magicLink: string;
  userEmail: string;
  expiresAt: string;
}

export function MagicLinkEmail({ 
  magicLink, 
  userEmail, 
  expiresAt 
}: MagicLinkEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={section}>
            <Text style={title}>Sign in to AgencyForms</Text>
            
            <Text style={text}>
              Hello! Click the button below to sign in to your AgencyForms account.
            </Text>
            
            <Button style={button} href={magicLink}>
              Sign in to AgencyForms
            </Button>
            
            <Text style={text}>
              Or copy and paste this URL into your browser:
            </Text>
            <Text style={link}>{magicLink}</Text>
            
            <Hr style={hr} />
            
            <Text style={footer}>
              This link will expire at {expiresAt}. If you didn't request this email, you can safely ignore it.
            </Text>
            
            <Text style={footer}>
              Email sent to: {userEmail}
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const section = {
  padding: '0 48px',
};

const title = {
  fontSize: '24px',
  lineHeight: '1.3',
  fontWeight: '700',
  color: '#194852',
  margin: '30px 0',
};

const text = {
  fontSize: '16px',
  lineHeight: '1.4',
  color: '#51545e',
  margin: '16px 0',
};

const button = {
  backgroundColor: '#194852',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '12px',
  margin: '24px 0',
};

const link = {
  fontSize: '14px',
  color: '#194852',
  textDecoration: 'underline',
  wordBreak: 'break-all' as const,
  margin: '16px 0',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  fontSize: '12px',
  color: '#8898aa',
  lineHeight: '1.4',
  margin: '8px 0',
};
