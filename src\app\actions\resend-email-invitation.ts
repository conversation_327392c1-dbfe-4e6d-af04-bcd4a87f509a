'use server'

import { createClient } from '@/lib/supabase/server'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'
import { resend, EMAIL_SENDERS, EMAIL_CONFIG } from '@/lib/email/resend-client'
import { render } from '@react-email/render'
import { InvitationEmail } from '@/lib/email/templates/invitation-email'
import { z } from 'zod'
import { randomBytes, createHash } from 'crypto'
import { roleUtils } from '@/lib/rbac/role-utils'

const resendSchema = z.object({
  invitationId: z.string().uuid(),
})

export interface ResendInvitationResult {
  success: boolean
  error?: string
}

/**
 * Resend an email invitation
 */
export async function resendEmailInvitation(
  invitationId: string
): Promise<ResendInvitationResult> {
  try {
    // 1. Validate input
    const validatedData = resendSchema.parse({ invitationId })

    // 2. Get current user
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return { success: false, error: 'Authentication required' }
    }

    // 3. Get invitation details
    const { data: invitation, error: inviteError } = await supabase
      .from('email_invitations')
      .select(`
        id,
        org_id,
        email,
        role_id,
        status,
        expires_at,
        personal_message,
        invitation_token,
        organizations (org_name)
      `)
      .eq('id', validatedData.invitationId)
      .single()

    if (inviteError || !invitation) {
      return { success: false, error: 'Invitation not found' }
    }

    // 4. Check if invitation can be resent (failed or expired)
    if (!['failed', 'expired'].includes(invitation.status)) {
      return { success: false, error: 'Only failed or expired invitations can be resent' }
    }

    // 5. Check RBAC permissions for the organization
    const hasPermission = await checkRbacPermission(
      { cruMinRole: 'orgMember' }, // Minimum permission to resend
      invitation.org_id
    )

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions to resend invitation' }
    }

    // 6. Generate new token and hash for resend
    const newToken = randomBytes(32).toString('base64url')
    const newHash = createHash('sha256').update(newToken).digest('hex')
    const newExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

    // 7. Get inviter name for personalization
    const { data: inviterProfile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('id', user.id)
      .single()

    // 8. Send email via Resend
    try {
      const inviteUrl = `${EMAIL_CONFIG.BASE_URL}/onboarding?token=${newToken}`
      const expiresAtFormatted = newExpiresAt.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })

      // Render the email template
      const displayRoleName = roleUtils.getRoleName(invitation.role_id as any)
      const emailProps = {
        organizationName: invitation.organizations?.[0]?.org_name || 'our organization',
        inviterName: inviterProfile?.full_name,
        roleName: displayRoleName,
        acceptUrl: inviteUrl,
        expiresAt: expiresAtFormatted,
        ...(invitation.personal_message && { personalMessage: invitation.personal_message }),
      }

      const emailHtml = await render(InvitationEmail(emailProps))

      const { data: emailResult } = await resend.emails.send({
        from: EMAIL_SENDERS.INVITATION,
        to: invitation.email,
        subject: `Reminder: You've been invited to join ${invitation.organizations?.[0]?.org_name || 'our organization'}`,
        html: emailHtml,
        text: `
Reminder: You've been invited to join ${invitation.organizations?.[0]?.org_name || 'our organization'} as a ${displayRoleName}.

${invitation.personal_message ? `"${invitation.personal_message}"\n\n` : ''}

Accept your invitation: ${inviteUrl}

This invitation will expire on ${expiresAtFormatted}. If you didn't expect this invitation, you can safely ignore this email.
        `.trim(),
        headers: {
          'X-Email-Type': 'invitation-resend',
          'X-Organization-Id': invitation.org_id,
          'X-Original-Invitation-Id': invitation.id,
        },
      })

      // 9. Update invitation with new hash, status and expiry
      await supabase
        .from('email_invitations')
        .update({
          status: 'delivered',
          invitation_hash: newHash,
          expires_at: newExpiresAt.toISOString(),
          resend_email_id: emailResult?.id,
        })
        .eq('id', invitation.id)

      return { success: true }

    } catch (emailError) {
      console.error('Failed to resend email:', emailError)

      // Update status to failed
      await supabase
        .from('email_invitations')
        .update({ status: 'failed' })
        .eq('id', invitation.id)

      return { success: false, error: 'Failed to send email. Please try again.' }
    }

  } catch (error) {
    console.error('Resend invitation error:', error)

    if (error instanceof z.ZodError) {
      return { success: false, error: 'Invalid invitation ID' }
    }

    return { success: false, error: 'Internal server error' }
  }
}
