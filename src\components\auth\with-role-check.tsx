'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { hasRequiredRole } from '@/lib/auth-utils'
import type { ComponentProps } from '@/types/components/auth/ComponentProps'

export function withRoleCheck<T extends ComponentProps>(
  Component: React.ComponentType<T>, 
  requiredRole: number
) {
  return function ProtectedRoute(props: T) {
    const router = useRouter()

    useEffect(() => {
      async function checkAuth() {
        const hasRole = await hasRequiredRole(requiredRole)
        if (!hasRole) {
          router.push('/dashboard')
        }
      }
      checkAuth()
    }, [router])

    return <Component {...props} />
  }
} 