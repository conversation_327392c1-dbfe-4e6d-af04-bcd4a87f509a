# Dashboard Authentication and Data Fetching Optimization Plan

## Objective

Optimize the initial dashboard load by reducing redundant data fetching and ensuring a smooth loading experience that keeps the main layout visible, while the main content area shows a loading state during re-evaluations triggered by real-time events.

## Current Issues

- Redundant data fetching calls during initial load in `src/app/dashboard/layout.tsx`.
- Multiple initializations of `DashboardProvider`.
- Flickering or blank screen in the main content area during initial load and event-triggered re-evaluations.

## Proposed Solution

Consolidate initial data fetching into fewer, more efficient calls in the server component (`src/app/dashboard/layout.tsx`) and streamline the `DashboardProvider` to consume this pre-fetched data. The `DashboardProvider` will manage the loading state for the *main content area* only, allowing the surrounding layout elements (sidebar, topbar, etc.) to remain visible.

## Steps

1.  **Refactor Data Fetching in `src/app/dashboard/layout.tsx`:**
    *   Fetch the authenticated user using `supabase.auth.getUser()`.
    *   Execute a single query to the `organization_members` table to get all memberships for the authenticated user. Select the following fields: `org_id`, `org_member_role`, `org_member_is_active`, `is_current_context`, and join with the `organizations` table to select `org_name`, `org_icon`, and `is_active`. This single query with a join will retrieve most of the necessary information efficiently.
    *   Process the query result to identify the `activeOrganization` object where `is_current_context` is true. This object should contain the nested `organizations` data (name, icon, active status) due to the join.
    *   Construct the `organizations` array from the query result, mapping the membership details and the nested `organizations` data into the desired `Organization` type structure.
    *   Construct the `authContext` object based on the `activeOrganization` data (if found), including `userId`, `orgId`, `orgName`, `orgRoleId`, `isSuperAdmin`, `isOrgActive`, and `isUserActive`. Determine `isSuperAdmin` based on the `org_member_role` (assuming role ID 1 is superadmin).
    *   Pass the fully constructed `user`, `organizations`, `activeOrganization`, and relevant authorization flags (`isActiveOrgDisabled`, `isUserDisabled`, `userRole`) as props to the `DashboardProvider`.

2.  **Simplify `src/components/providers/dashboard-provider.tsx`:**
    *   Remove the `useEffect` hook that was previously responsible for authorization checks and redirects.
    *   Initialize the `isReadyToRender` state based on the initial props received. If the user is authorized to view the current page based on the initial `isActiveOrgDisabled`, `isUserDisabled`, and `userRole` props, set `isReadyToRender` to `true` initially. If a redirect is necessary based on these initial props, perform the redirect immediately in a `useEffect` that runs only on mount (`[]` dependency array).
    *   Keep the conditional rendering in the return statement: render `children` when `isReadyToRender` is true, and render a loading indicator (like the "Loading..." message or a spinner) when it's false. This ensures the main content area shows a loading state during initial evaluation and subsequent soft refreshes.
    *   Ensure the `DashboardProvider` relies solely on the props passed from the server component for its initial state and authorization checks.

3.  **Review `src/components/dashboard/dashboard-event-manager.tsx`:**
    *   Confirm that the `refreshServerContextAndSoftRefresh` function correctly triggers a soft refresh (`router.refresh()`) after updating server context and clearing cache.
    *   Ensure the event handlers call `refreshServerContextAndSoftRefresh` only for events relevant to the active organization or context changes.
    *   Verify that `router.refresh()` triggers a re-fetch of the data in `src/app/dashboard/layout.tsx`, which in turn updates the props passed to `DashboardProvider`, causing it to re-evaluate the rendering state and potentially show the loading indicator in the main content area.

## Optimized Data Flow (Initial Load) - Sequence Diagram

```mermaid
sequenceDiagram
    participant Browser
    participant Next.js Server
    participant Supabase DB

    Browser->>Next.js Server: Request /dashboard
    Next.js Server->>Next.js Server: Run src/middleware.ts
    Next.js Server->>Supabase DB: Authenticate User
    Supabase DB-->>Next.js Server: User Authentication Status
    Next.js Server->>Next.js Server: Middleware sets auth headers
    Next.js Server->>Next.js Server: Render src/app/dashboard/layout.tsx (Server Component)
    Next.js Server->>Supabase DB: Query organization_members (memberships + joined org details)
    Supabase DB-->>Next.js Server: User Memberships Data (including org details)
    Next.js Server->>Next.js Server: Process data, identify activeOrg, construct organizations array and authContext
    Next.js Server->>Browser: Send HTML (including DashboardProvider with pre-fetched data)
    Browser->>Browser: Hydrate DashboardProvider (Client Component)
    Browser->>Browser: DashboardProvider initializes state based on props
    alt Initial Redirect Needed (e.g., disabled org/user)
        Browser->>Browser: DashboardProvider performs redirect (useEffect on mount)
    else Initial Redirect Not Needed
        Browser->>Browser: DashboardProvider sets isReadyToRender = true initially
        Browser->>Browser: Render main content (children)
    end
    Browser->>Supabase DB: Establish Realtime Subscriptions (via DashboardEventManager)