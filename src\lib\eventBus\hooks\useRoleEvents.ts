'use client'

import type { UserRoleChangedEvent } from '@/lib/eventTypes'
import { useBusEvent } from '@/lib/useBusEvent'

/**
 * Subscribe to user role change events for a specific user
 */
export function useUserRoleEvents(
  userId: string | null,
  handler: (event: UserRoleChangedEvent) => void
): void {
  useBusEvent(
    'user:role:changed',
    (payload) => {
      if (!userId || payload.userId !== userId) return
      handler(payload)
    },
    [userId, handler]
  )
}
