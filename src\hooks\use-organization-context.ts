'use client';

import { useEffect, useState, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import type { Organization } from '@/types/organization';
import { useBusEvent } from '@/lib/useBusEvent';
import { AUTH_CONTEXT_CHANGED } from '@/lib/eventBus/constants';
import type { AuthEvent } from '@/lib/eventTypes';

/**
 * Type representing the response from the database for organization membership queries
 */
interface OrganizationMembershipResponse {
  org_id: string;
  org_member_role: number; 
  org_member_is_active: boolean;
  is_default_org: boolean;
  is_current_context: boolean;
  organizations: {
    id: string;
    org_name: string;
    org_icon: string | null;
    created_at: string;
    updated_at: string;
    is_active: boolean;
  };
  roles: {
    role_id: number;
    role_name: string;
  };
}

/**
 * Hook for managing organization context with realtime updates
 * This replaces the cookie-based approach with a DB-driven solution
 */
export function useOrganizationContext() {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const supabase = createClient();
  
  // Fetch the current organization context from DB
  const fetchCurrentOrganization = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session) {
        setError(sessionError || new Error('No session found'));
        setOrganization(null);
        return;
      }

      // Store the user ID for event bus subscription
      setUserId(session.user.id);

      // Get current context organization
      const { data, error } = await supabase
        .from('organization_members')
        .select(`
          org_id,
          org_member_role,
          is_default_org,
          is_current_context,
          organizations!inner (
            id,
            org_name,
            org_icon,
            created_at,
            updated_at,
            is_active
          ),
          roles!inner (
            role_id,
            role_name
          )
        `)
        .eq('user_id', session.user.id)
        .eq('is_current_context', true)
        .eq('org_member_is_active', true)
        .eq('organizations.is_active', true)
        .single();

      if (error) {
        console.error('[OrganizationContext] Error fetching organization:', error);
        
        // If no context is set, try to initialize one
        const { data: orgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
          p_user_id: session.user.id
        });
        
        if (initError || !orgId) {
          setError(new Error(`No organization context available: ${initError?.message || 'No organization found'}`));
          setOrganization(null);
          return;
        }
        
        // Retry fetching after initialization
        return fetchCurrentOrganization();
      }

      if (!data) {
        // No organization found with current context
        setOrganization(null);
        return;
      }

      // Cast the data to the proper type
      const typedData = data as unknown as OrganizationMembershipResponse;

      const org: Organization = {
        id: typedData.organizations.id,
        name: typedData.organizations.org_name,
        org_icon: typedData.organizations.org_icon || '',
        role: typedData.roles.role_name,
        org_member_role: typedData.org_member_role,
        isActive: typedData.organizations.is_active,
        isDefault: typedData.is_default_org,
        createdAt: typedData.organizations.created_at,
        updatedAt: typedData.organizations.updated_at
      };
      
      console.log('[OrganizationContext] Current organization:', org.name);
      setOrganization(org);
    } catch (err) {
      console.error('[OrganizationContext] Unexpected error:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase]);
  
  // Handler for auth context change events
  const handleAuthContextChange = useCallback((event: AuthEvent) => {
    // Only process events for the current user and organization context changes
    if (event.userId !== userId || !event.reason.includes('context')) return;

    console.log('[OrganizationContext] Received auth context change event:', event);
    fetchCurrentOrganization();
  }, [fetchCurrentOrganization, userId]);

  // Use the unified auth context events instead of legacy organization context events
  useBusEvent(AUTH_CONTEXT_CHANGED, handleAuthContextChange, [handleAuthContextChange]);

  // Log when user ID changes
  useEffect(() => {
    if (userId) {
      console.log(`[OrganizationContext] User ID set to ${userId}, event bus subscription updated`);
      setIsConnected(true);
    }
  }, [userId]);

  // Switch to a different organization
  const switchOrganization = useCallback(async (orgId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      console.log(`[OrganizationContext] Switching organization to: ${orgId}`);

      // Use the server action to ensure consistent handling
      const result = await import('@/app/actions/switch-organization').then(mod => 
        mod.switchOrganization(orgId)
      );

      if (!result || result.success === false) {
        throw new Error(result?.error || "Failed to switch organization");
      }

      // We'll fetch immediately rather than waiting for the event
      // This helps ensure a responsive UI while events propagate
      fetchCurrentOrganization();
    } catch (err) {
      console.error('[OrganizationContext] Error switching organization:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setLoading(false);
    }
  }, [fetchCurrentOrganization]);

  // Initial fetch
  useEffect(() => {
    fetchCurrentOrganization();
  }, [fetchCurrentOrganization]);

  return {
    organization,
    loading,
    error,
    isConnected,
    switchOrganization,
    refreshOrganization: fetchCurrentOrganization
  };
} 