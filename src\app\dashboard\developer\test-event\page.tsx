import { Suspense } from 'react'
import { createClient } from '@/lib/supabase/server'
import { TestEventSystem } from '@/components/developer/test-event-system'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'

export const metadata = {
  title: 'Event System Test | Developer',
  description: 'Monitor and test the realtime event system'
}

export default async function TestEventPage() {
  // Use the RBAC system to check if user has superadmin access
  await checkRbacPermission({ 
    rRoles: ['superAdmin'] 
  })
  
  // If checkRbacPermission didn't redirect, the user has access
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-4">Event System Test Console</h1>
      <p className="text-slate-600 mb-6">
        Monitor and test the real-time event system. This page allows you to view events as they occur 
        and test event connections.
      </p>
      
      <Suspense fallback={<div>Loading event system...</div>}>
        <TestEventSystem userId={user!.id} />
      </Suspense>
    </div>
  )
} 