"use client";

import { ReactNode } from 'react';
import { useRbacPermission } from '@/hooks/use-rbac-permission';
import type { RbacConditions } from '@/types/lib/rbac';

interface RestrictedProps extends RbacConditions {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Restricted - Component that conditionally renders children based on RBAC permissions
 * 
 * @example
 * // Only show for admins
 * <Restricted crudMinRole="orgAdmin">
 *   <Button>Admin Action</Button>
 * </Restricted>
 * 
 * // Only show for specific roles with fallback
 * <Restricted 
 *   rRoles={["superAdmin", "supportAdmin"]} 
 *   fallback={<p>Not authorized</p>}
 * >
 *   <AdminPanel />
 * </Restricted>
 * 
 * // Organization-specific permission
 * <Restricted cMinRole="orgMember" resourceOrgId={item.orgId}>
 *   <Button>Create in this org</Button>
 * </Restricted>
 */
export function Restricted({ 
  children, 
  fallback = null, 
  ...RbacConditions 
}: RestrictedProps) {
  const hasPermission = useRbacPermission(RbacConditions);
  
  if (hasPermission) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
} 