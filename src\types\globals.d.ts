/**
 * Global application type extensions
 */
declare global {
  /**
   * Extended Window interface for our application-specific global properties
   */
  interface Window {
    /**
     * Flag to track subscription status to avoid duplicate dashboard channel subscriptions.
     * This is maintained between page refreshes within the same tab session.
     * 
     * The Supabase Realtime connection pattern follows:
     * 1. subscribe - Initial request
     * 2. confirm - 'Subscribed to PostgreSQL' message
     * 3. disconnect - Normal post-subscription disconnect (internal supabase behavior)
     * 4. reconnect - Automatic reconnection by Supabase
     * 5. stabilize - Connection becomes stable
     * 
     * This flag remains true through this entire lifecycle to prevent duplicate subscriptions
     * during the natural reconnection cycles.
     */
    __hasSubscribedDashboardChannels: boolean;

    /**
     * Debugging utility for inspecting mitt event emitter
     */
    __mittDebug?: (eventName?: string) => void;
  }
}

export {}; 