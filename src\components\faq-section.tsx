"use client";
import Image from "next/image";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Check } from "lucide-react";

const faqs = [
  {
    question: "What services do you offer?",
    answer: (
      <div>
        <p className="mb-4">
          We offer comprehensive immigration and visa consulting services,
          including visa application assistance, document preparation,
        </p>
        <ul className="space-y-2">
          <li className="flex items-center">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Comprehensive Visa Assistance</span>
          </li>
          <li className="flex items-center">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Visa Category Expertise</span>
          </li>
          <li className="flex items-center">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Transparency and Communication</span>
          </li>
        </ul>
      </div>
    ),
  },
  {
    question: "What is the consultation process like?",
    answer: "Our consultation process involves...",
  },
  {
    question: "How much do your services cost?",
    answer: "Our service costs vary depending on...",
  },
  {
    question: "How do I get started with your services?",
    answer: "To get started with our services, you can...",
  },
  {
    question: "What is your success rate with visa applications?",
    answer: "Our success rate with visa applications is...",
  },
];

export default function FAQSection() {
  return (
    <section className="bg-white space-y-8 max-w-[1140px] mx-auto">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <div>
            <h2 className="text-4xl font-medium text-[#0A2A2A] mb-4">
              Common questions <br />
              <span className="text-[#194852]">answered</span>
            </h2>
            <p className="text-lg text-[#64748B] mb-8">
              At the heart of our commitment to providing exceptional
              immigration solutions stands our trusted
            </p>
            <div className="relative w-full h-[300px] lg:h-[400px]">
              <Image
                src="/images/flags.png"
                alt="Hands holding various country flags"
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                style={{ objectFit: "contain" }}
                loading="lazy"
              />
            </div>
          </div>
          <Accordion
            type="single"
            collapsible
            className="w-full"
            defaultValue="item-0"
          >
            {faqs.map((faq, index) => (
              <AccordionItem value={`item-${index}`} key={index}>
                <AccordionTrigger className="text-lg font-medium text-[#1E293B]">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-[#64748B]">
                  {typeof faq.answer === "string" ? (
                    <p>{faq.answer}</p>
                  ) : (
                    faq.answer
                  )}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
