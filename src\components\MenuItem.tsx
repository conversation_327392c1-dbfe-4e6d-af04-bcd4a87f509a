"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Icon } from "@/components/shared/Icon";
import { useRbacPermission } from "@/hooks/use-rbac-permission";
import { MenuItemProps } from "@/types/components/MenuItemProps";

const MenuItem = ({ item, isCollapsed }: MenuItemProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const permissions = useRbacPermission();
  const pathname = usePathname();

  // Filter submenu items based on permissions
  const filteredSubmenu = item.submenu?.filter((subItem) => {
    if (!subItem.RbacConditions) return true;

    // If auth is still loading, hide items to prevent flashes
    if (permissions.isLoading) return false;

    // Check RBAC permission using consolidated hook
    return permissions.checkPermission(subItem.RbacConditions);
  });

  const hasSubmenu = filteredSubmenu && filteredSubmenu.length > 0;

  const isActive =
    (item.href && pathname === item.href) ||
    (filteredSubmenu &&
      filteredSubmenu.some((sub) => sub.href && pathname === sub.href));

  const content = (
    <>
      <Icon name={item.icon} className="w-5 h-5" />
      {!isCollapsed && (
        <>
          <span className="ml-3 flex-1 text-left">{item.label}</span>
          {hasSubmenu && (
            <span className="ml-auto">
              {isOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </span>
          )}
        </>
      )}
    </>
  );

  // If no submenu items are accessible, don't render the menu item
  if (item.submenu && !hasSubmenu) {
    return null;
  }

  return (
    <div className="relative">
      {item.href ? (
        <Link
          href={item.href}
          className={`w-full flex items-center px-4 py-2 rounded-lg transition-colors ${
            isActive
              ? "text-white border border-[#295D69] bg-[#194852]"
              : "text-gray-300 hover:bg-[#194852]"
          }`}
        >
          {content}
        </Link>
      ) : (
        <button
          onClick={() => hasSubmenu && setIsOpen(!isOpen)}
          className={`w-full flex items-center px-4 py-2 rounded-lg transition-colors ${
            isActive
              ? "bg-[#194852] text-white"
              : "text-gray-300 hover:bg-[#194852]/60"
          }`}
        >
          {content}
        </button>
      )}
      {hasSubmenu && isOpen && !isCollapsed && (
        <div className="ml-4 mt-1 space-y-1">
          {filteredSubmenu &&
            filteredSubmenu.map((subItem) => (
              <MenuItem
                key={subItem.label}
                item={subItem}
                isCollapsed={isCollapsed}
              />
            ))}
        </div>
      )}
    </div>
  );
};

export default MenuItem;
