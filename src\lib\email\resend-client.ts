import { Resend } from 'resend';

if (!process.env.RESEND_API_KEY) {
  throw new Error('RESEND_API_KEY environment variable is required');
}

if (!process.env.EMAIL_DOMAIN) {
  throw new Error('EMAIL_DOMAIN environment variable is required');
}

export const resend = new Resend(process.env.RESEND_API_KEY);

// Email sender addresses
export const EMAIL_SENDERS = {
  LOGIN: `AgencyForms <login@${process.env.EMAIL_DOMAIN}>`,
  INVITATION: `AgencyForms <invite@${process.env.EMAIL_DOMAIN}>`,
  SYSTEM: `AgencyForms <system@${process.env.EMAIL_DOMAIN}>`,
} as const;

// Email configuration
export const EMAIL_CONFIG = {
  DOMAIN: process.env.EMAIL_DOMAIN,
  BASE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
} as const;
