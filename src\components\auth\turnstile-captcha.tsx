"use client";

import React from 'react';
import { Turnstile, type TurnstileInstance } from '@marsidev/react-turnstile';

interface TurnstileCaptchaProps {
  onSuccess: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  className?: string;
}

export function TurnstileCaptcha({
  onSuccess,
  onError,
  onExpire,
  className
}: TurnstileCaptchaProps) {
  const siteKey = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY;
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const turnstileRef = React.useRef<TurnstileInstance | null>(null);

  if (!siteKey) {
    console.error('NEXT_PUBLIC_TURNSTILE_SITE_KEY is not configured');
    return null;
  }

  const handleSuccess = (token: string) => {
    setIsLoading(false);
    setHasError(false);
    onSuccess(token);
  };

  const handleError = (error: string) => {
    setIsLoading(false);
    setHasError(true);
    // Only log actual errors, not normal expiration
    if (error !== 'expired') {
      console.warn('Turnstile error:', error);
    }
    onError?.(error);
  };

  const handleExpire = () => {
    setIsLoading(false);
    setIsRefreshing(true);
    // Don't log expiration as it's normal behavior
    onExpire?.();

    // Auto-refresh after a short delay to allow Turnstile's built-in refresh
    setTimeout(() => {
      setIsRefreshing(false);
    }, 2000);
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className={className}>
      <Turnstile
        ref={turnstileRef}
        siteKey={siteKey}
        onSuccess={handleSuccess}
        onError={handleError}
        onExpire={handleExpire}
        onLoad={handleLoad}
        options={{
          theme: 'light',
          size: 'normal',
          action: 'magic-link-request',
          cData: 'auth-form',
          refreshExpired: 'auto', // Automatically refresh when token expires
          retry: 'auto', // Automatically retry on failure
          retryInterval: 3000, // Retry every 3 seconds instead of default 8 seconds
        }}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="text-xs text-gray-500 text-center mt-2">
          Loading security verification...
        </div>
      )}

      {/* Refreshing state */}
      {isRefreshing && (
        <div className="text-xs text-blue-500 text-center mt-2">
          Security verification expired. Refreshing automatically...
        </div>
      )}

      {/* Error state with retry option */}
      {hasError && !isRefreshing && (
        <div className="text-xs text-red-500 text-center mt-2">
          Security verification failed. It will retry automatically.
        </div>
      )}
    </div>
  );
}

// Hook for managing CAPTCHA state
export function useTurnstile() {
  const [captchaToken, setCaptchaToken] = React.useState<string | null>(null);
  const [captchaError, setCaptchaError] = React.useState<string | null>(null);
  const [captchaExpired, setCaptchaExpired] = React.useState<boolean>(false);
  const [isRetrying, setIsRetrying] = React.useState<boolean>(false);

  const handleSuccess = React.useCallback((token: string) => {
    setCaptchaToken(token);
    setCaptchaError(null);
    setCaptchaExpired(false);
    setIsRetrying(false);
  }, []);

  const handleError = React.useCallback((error: string) => {
    // Handle different error types gracefully
    if (error === 'expired') {
      setCaptchaExpired(true);
      setCaptchaToken(null);
      setCaptchaError(null);
    } else {
      setCaptchaError(error);
      setCaptchaToken(null);
      setCaptchaExpired(false);

      // Auto-retry for network errors
      if (error.includes('network') || error.includes('timeout')) {
        setIsRetrying(true);
        setTimeout(() => {
          setIsRetrying(false);
          setCaptchaError(null);
        }, 3000);
      }
    }
  }, []);

  const handleExpire = React.useCallback(() => {
    setCaptchaExpired(true);
    setCaptchaToken(null);
    setCaptchaError(null);
    setIsRetrying(false);
  }, []);

  const reset = React.useCallback(() => {
    setCaptchaToken(null);
    setCaptchaError(null);
    setCaptchaExpired(false);
    setIsRetrying(false);
  }, []);

  // Auto-reset expired state after a delay to allow for automatic refresh
  React.useEffect(() => {
    if (captchaExpired) {
      const timer = setTimeout(() => {
        setCaptchaExpired(false);
      }, 3000); // Reset after 3 seconds to allow Turnstile to auto-refresh

      return () => clearTimeout(timer);
    }
  }, [captchaExpired]);

  return {
    captchaToken,
    captchaError,
    captchaExpired,
    isRetrying,
    handleSuccess,
    handleError,
    handleExpire,
    reset,
    isValid: !!captchaToken && !captchaExpired,
  };
}
