import { render } from '@react-email/render';
import { resend, EMAIL_SENDERS, EMAIL_CONFIG } from './resend-client';
import { MagicLinkEmail } from './templates/magic-link-email';
import { createAdminClient, logAdminOperation, type AdminMagicLinkOptions, type AdminMagicLinkResult } from '@/lib/supabase/admin';

/**
 * Enhanced Magic Link Service using Supabase Admin API + Custom Resend Templates
 *
 * This service provides better email deliverability by using Resend's API directly
 * while maintaining Supabase's secure token generation and validation.
 *
 * Flow:
 * 1. Generate magic link via Supabase Admin API
 * 2. Send custom email via Resend API
 * 3. User clicks link → existing auth callback handles session
 */

interface SendAdminMagicLinkParams {
  email: string;
  redirectTo?: string;
  captchaToken?: string | undefined;
  clientIP?: string;
}

interface AdminMagicLinkEmailResult {
  success: boolean;
  messageId?: string | undefined;
  error?: string;
  userExists?: boolean;
}

/**
 * Generate magic link using Supabase Admin API
 *
 * @param options - Magic link generation options
 * @returns Promise<AdminMagicLinkResult>
 */
async function generateAdminMagicLink(options: AdminMagicLinkOptions): Promise<AdminMagicLinkResult> {
  try {
    const adminClient = createAdminClient();

    logAdminOperation('generateMagicLink', {
      email: options.email,
      hasRedirectTo: !!options.redirectTo,
      hasCaptcha: !!options.captchaToken,
    });

    const { data, error } = await adminClient.auth.admin.generateLink({
      type: 'magiclink',
      email: options.email,
      options: {
        redirectTo: options.redirectTo || `${EMAIL_CONFIG.BASE_URL}/auth/callback?redirectTo=/dashboard`,
        ...(options.captchaToken && { captchaToken: options.captchaToken }),
      },
    });

    if (error) {
      // Log error but don't expose details for security
      console.warn(`Magic link generation failed for ${options.email}:`, error.message);

      // Check if error indicates user doesn't exist
      const userNotFound = error.message?.toLowerCase().includes('user not found') ||
                          error.message?.toLowerCase().includes('invalid email') ||
                          error.message?.toLowerCase().includes('not found');

      return {
        success: false,
        error: userNotFound ? 'USER_NOT_FOUND' : 'GENERATION_FAILED',
      };
    }

    if (!data?.properties?.action_link) {
      console.error('Magic link generation succeeded but no action_link returned');
      return {
        success: false,
        error: 'NO_ACTION_LINK',
      };
    }

    logAdminOperation('generateMagicLinkSuccess', {
      email: options.email,
      hasActionLink: !!data.properties.action_link,
    });

    return {
      success: true,
      actionLink: data.properties.action_link,
      emailOtp: data.properties.email_otp,
      hashedToken: data.properties.hashed_token,
    };
  } catch (error) {
    console.error('Exception during magic link generation:', error);
    return {
      success: false,
      error: 'EXCEPTION',
    };
  }
}

/**
 * Send magic link email using custom Resend template
 *
 * @param email - Recipient email address
 * @param magicLink - The magic link URL
 * @param expiresAt - When the link expires
 * @returns Promise<MagicLinkEmailResult>
 */
async function sendCustomMagicLinkEmail(
  email: string,
  magicLink: string,
  expiresAt: Date = new Date(Date.now() + 60 * 60 * 1000)
): Promise<{ success: boolean; messageId?: string | undefined; error?: string }> {
  try {
    // Format expiry time for display
    const expiresAtFormatted = expiresAt.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });

    // Render the email template
    const emailHtml = await render(
      MagicLinkEmail({
        magicLink,
        userEmail: email,
        expiresAt: expiresAtFormatted,
      })
    );

    // Send the email via Resend with enhanced headers
    const { data, error } = await resend.emails.send({
      from: EMAIL_SENDERS.LOGIN,
      to: [email],
      subject: 'Sign in to AgencyForms',
      html: emailHtml,
      headers: {
        'X-Magic-Link-Version': 'hybrid-v1',
        'X-Email-Type': 'magic-link',
      },
      // Add text fallback
      text: `
Sign in to AgencyForms

Hello! Click the link below to sign in to your AgencyForms account:

${magicLink}

This link will expire at ${expiresAtFormatted}.

If you didn't request this email, you can safely ignore it.

Email sent to: ${email}
      `.trim(),
    });

    if (error) {
      console.error('Failed to send magic link email via Resend:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email',
      };
    }

    logAdminOperation('sendMagicLinkEmail', {
      email,
      messageId: data?.id,
      provider: 'resend',
    });

    return {
      success: true,
      messageId: data?.id || undefined,
    };
  } catch (error) {
    console.error('Exception during magic link email sending:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Main function: Generate and send magic link using admin API + custom email
 *
 * This function maintains user enumeration protection by always returning
 * the same response regardless of whether the user exists.
 *
 * @param params - Magic link parameters
 * @returns Promise<AdminMagicLinkEmailResult>
 */
export async function sendAdminMagicLinkEmail(params: SendAdminMagicLinkParams): Promise<AdminMagicLinkEmailResult> {
  const { email, redirectTo, captchaToken, clientIP } = params;

  try {
    // Generate magic link via admin API
    const linkResult = await generateAdminMagicLink({
      email,
      redirectTo,
      captchaToken,
    });

    // If generation failed, handle based on error type
    if (!linkResult.success) {
      // For user enumeration protection, we still return success
      // but log the actual reason for monitoring
      logAdminOperation('magicLinkGenerationFailed', {
        email,
        error: linkResult.error,
        clientIP,
      });

      // Return generic success to prevent user enumeration
      return {
        success: true, // Always return success for security
        userExists: false, // Internal tracking only
      };
    }

    // Send email using custom template
    const emailResult = await sendCustomMagicLinkEmail(
      email,
      linkResult.actionLink!,
      new Date(Date.now() + 60 * 60 * 1000) // 1 hour expiry
    );

    if (!emailResult.success) {
      console.error(`Failed to send magic link email to ${email}:`, emailResult.error);

      // Even if email sending fails, return success for user enumeration protection
      return {
        success: true,
        userExists: true,
      };
    }

    logAdminOperation('magicLinkSentSuccessfully', {
      email,
      messageId: emailResult.messageId,
      clientIP,
    });

    return {
      success: true,
      messageId: emailResult.messageId || undefined,
      userExists: true,
    };
  } catch (error) {
    console.error('Exception in sendAdminMagicLinkEmail:', error);

    // Always return success for user enumeration protection
    return {
      success: true,
      userExists: false,
    };
  }
}

/**
 * Utility function to validate magic link URL format
 *
 * @param url - The magic link URL to validate
 * @returns boolean - true if URL appears to be a valid magic link
 */
export function isValidMagicLinkUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);

    // Check if it's a Supabase auth URL with required parameters
    const hasTokenHash = parsedUrl.searchParams.has('token_hash');
    const hasType = parsedUrl.searchParams.get('type') === 'magiclink';
    const isSupabaseAuth = parsedUrl.pathname.includes('/auth/v1/verify');

    return hasTokenHash && hasType && isSupabaseAuth;
  } catch {
    return false;
  }
}
