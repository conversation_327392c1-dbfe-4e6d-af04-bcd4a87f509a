import { NextResponse, type NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { RbacConditions } from '@/types/lib/rbac'
import { evaluateRbac } from './rbac-utils'
import { resolveUserAuthContext } from '../auth-context'

// Define the middleware config type locally
interface PermissionMiddlewareConfig extends RbacConditions {
  redirectTo?: string;      // Path to redirect to if checks fail
  bypassPaths?: string[];   // Paths that bypass permission checks
}

export function createPermissionMiddleware(config: PermissionMiddlewareConfig) {
  return async function middleware(request: NextRequest) {
    // Check if path should be bypassed
    if (config.bypassPaths?.some((path: string) => request.nextUrl.pathname.startsWith(path))) {
      return NextResponse.next()
    }

    const response = NextResponse.next({
      request,
    })

    try {
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return request.cookies.getAll()
            },
            setAll(cookiesToSet) {
              cookiesToSet.forEach(({ name, value, options }) => {
                response.cookies.set(name, value, options)
              })
            }
          },
        }
      )

      // IMPORTANT: Do not run any code between createServerClient and auth.getUser()
      const {
        data: { user },
      } = await supabase.auth.getUser()

      if (!user) {
        return NextResponse.redirect(new URL('/auth/login', request.url))
      }

      // Get active organization from database using is_current_context
      // Use resolveUserAuthContext which handles finding the current context
      const authContext = await resolveUserAuthContext(user.id)
      
      if (!authContext) {
        console.error('RBAC middleware: Failed to resolve auth context', {
          userId: user.id,
          path: request.nextUrl.pathname
        })
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }

      // Extract active orgId from the context
      const activeOrgId = authContext.orgId
      
      // We already have the role from authContext, but this middleware expects
      // to specifically get it from the organization_members table
      // Get user's role in the active organization
      const { data: memberData } = await supabase
        .from('organization_members')
        .select('org_member_role')
        .eq('user_id', user.id)
        .eq('org_id', activeOrgId)
        .single()

      if (!memberData) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }

      // Extract RBAC conditions from config, removing non-RBAC props
      const { redirectTo, ...rbacConditions } = config

      // Set the organization context if not provided
      if (!rbacConditions.resourceOrgId && !rbacConditions.orgId) {
        rbacConditions.resourceOrgId = activeOrgId
      }

      // Evaluate access using RBAC
      const hasAccess = evaluateRbac(memberData.org_member_role, rbacConditions)

      if (!hasAccess) {
        return NextResponse.redirect(
          new URL(redirectTo || '/dashboard', request.url)
        )
      }

      return response
    } catch (error) {
      console.error('Permission middleware error:', error)
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }
}

// Example usage:
// export const config = {
//   matcher: ['/dashboard/admin/:path*']
// }
// 
// export default createPermissionMiddleware({
//   rMinRole: 'orgAdmin', // Require minimum role of org admin
//   redirectTo: '/dashboard',
//   bypassPaths: ['/dashboard/admin/public']
// }) 