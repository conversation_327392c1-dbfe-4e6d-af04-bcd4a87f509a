import { createClient } from '@/lib/supabase/server'
import { Organization } from '@/types/organization'

/**
 * Type representing the response from the database for organization membership queries
 */
interface OrganizationMembershipResponse {
  org_id: string;
  org_member_role: number; 
  org_member_is_active: boolean;
  is_default_org: boolean;
  organizations: {
    id: string;
    org_name: string;
    org_icon: string | null;
    created_at: string;
    updated_at: string;
    is_active: boolean;
  };
  roles: {
    role_id: number;
    role_name: string;
  };
}

/**
 * Utility to get the active organization for a user from database context
 * Replaces cookie-based approach with DB-driven context
 */
export async function getUserActiveOrganization(
  userId?: string
): Promise<{ organization: Organization | null; error: Error | null }> {
  try {
    if (!userId) {
      const supabase = await createClient()
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        return { 
          organization: null, 
          error: userError || new Error('No authenticated user found') 
        }
      }
      
      userId = user.id
    }
    
    return getOrganizationByContext(userId)
  } catch (err) {
    console.error('Error in getUserActiveOrganization:', err)
    return { 
      organization: null, 
      error: err instanceof Error ? err : new Error(String(err)) 
    }
  }
}

/**
 * Gets the organization with current context for a user
 * Includes fallback logic to initialize context if needed
 */
export async function getOrganizationByContext(
  userId: string
): Promise<{ organization: Organization | null; error: Error | null }> {
  try {
    const supabase = await createClient()
    
    // Get organization with current context
    const { data: membership, error: contextError } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        org_member_role,
        org_member_is_active,
        is_default_org,
        organizations!inner (
          id,
          org_name,
          org_icon,
          created_at,
          updated_at,
          is_active
        ),
        roles!inner (
          role_id,
          role_name
        )
      `)
      .eq('user_id', userId)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)
      .eq('organizations.is_active', true)
      .single()
    
    // If found, return the organization
    if (!contextError && membership) {
      const typedMembership = membership as unknown as OrganizationMembershipResponse
      
      return {
        organization: {
          id: typedMembership.organizations.id,
          name: typedMembership.organizations.org_name,
          org_icon: typedMembership.organizations.org_icon || '',
          role: typedMembership.roles.role_name,
          org_member_role: typedMembership.org_member_role,
          isActive: typedMembership.organizations.is_active,
          isDefault: typedMembership.is_default_org,
          createdAt: typedMembership.organizations.created_at,
          updatedAt: typedMembership.organizations.updated_at
        },
        error: null
      }
    }
    
    // No context found, initialize one
    const { data: orgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: userId
    })
    
    if (initError || !orgId) {
      console.log('No organizations found or failed to initialize context')
      return { organization: null, error: new Error('No organization found for user') }
    }
    
    // Try again to get the organization with the newly set context
    return getOrganizationByContext(userId)
  } catch (err) {
    console.error('Error in getOrganizationByContext:', err)
    return { 
      organization: null, 
      error: err instanceof Error ? err : new Error(String(err)) 
    }
  }
} 