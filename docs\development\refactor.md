# Context and Organization Management Refactor

## Current Architecture Analysis

### API Endpoints

1. **`/api/auth/refresh-context`**
   - **Purpose**: Refreshes the user's authentication context
   - **Returns**: Full user context including:
     - Current organization data (id, role, status)
     - User profile data (email, name, avatar)
     - **Complete list of all organizations** the user belongs to with details
   - **Used by**: Dashboard components, organization switcher, auth flows
   - **Called on**: Page navigation, tab visibility change, manual refresh

2. **`/api/auth/context`**
   - **Purpose**: Gets the user's context for a specific user/org
   - **Returns**: User context for a specific organization
   - **Used by**: Server-side components, API routes
   - **Does not** return the full organizations list

### State Management

1. **`useAuthContextStore` (Zustand)**
   - **Stores**: 
     - User data (userId, userEmail, userFullName, avatarUrl)
     - Current org context (orgId, roleId, isUserActiveInOrg, isOrgActive)
     - Full organizations list
   - **Updated by**: refresh-context API responses, realtime events

2. **`useServerContextRefresher` (SWR/Hook)**
   - **Purpose**: Provides a mechanism to force refresh the context
   - **Method**: Calls `/api/auth/refresh-context`
   - **Updates**: Zustand store via `updateFullContext`

### Event Handling

1. **`DashboardEventManagerCore`**
   - **Purpose**: Manages Supabase Realtime subscriptions
   - **Subscriptions**: organization_members, organizations
   - **Emits events** via `emitter` when DB changes occur
   - **Refreshes context** on tab visibility changes

2. **Realtime Event Listeners**
   - **Zustand store** listens to these events to update state
   - **Organization-switcher** uses custom hooks (`useOrgNameEvents`, `useUserRoleEvents`, etc.)
   - Events trigger cache invalidation and state updates

### Component Dependencies

1. **Dashboard Layout**
   - Depends on current context for permissions, UI elements
   - Doesn't need full organization list

2. **Organization Switcher**
   - Requires full organizations list 
   - Updates based on realtime events
   - Handles organization switching via server action

## Issues with Current Architecture

1. **Performance Concerns**
   - **Large payload size**: Full org list in every context refresh
   - **Redundant data fetching**: Fetching all orgs when only current context needed
   - **Potential scaling issue**: Will become worse as users join more organizations
   - **Bandwidth usage**: Unnecessary data over the network

2. **Architectural Inefficiencies**
   - **Tight coupling**: Context and organizations list are bundled
   - **Data duplication**: Same data fetched repeatedly
   - **Mixing concerns**: Auth context combined with UI data (org list for switcher)

3. **Cache and State Management**
   - **Invalidation overkill**: Full cache invalidation for minor updates
   - **Over-fetching**: Fetching all data when only partial updates needed
   - **Missing separation**: No clear distinction between auth context and UI data

## Proposed Architecture

### API Separation

1. **`/api/auth/context` or `/api/auth/refresh-context`**
   - Returns **only** current context (user + current org)
   - Small, focused payload
   - Frequently refreshed when needed

2. **New: `/api/auth/organizations`**
   - Returns **only** the list of organizations
   - Fetched on initial load or explicitly when needed
   - Can implement pagination if needed

### State Management Improvements

1. **Split Stores or Store Slices**
   - **Current Context**: Focused state for current user + org
   - **Organizations List**: Separate state slice for org list
   - **Clear update boundaries**: Each updated only when needed

2. **Selective Updates**
   - Only update organizations list when:
     - Initial load
     - Explicit refresh (e.g., switcher open)
     - Relevant realtime events (org added/removed/changed)

3. **Optimistic Updates**
   - Improve org switching with optimistic UI updates
   - Update state immediately, confirm with server

### Event Handling Optimization

1. **Targeted State Updates**
   - Update only affected organizations in list
   - Update current context only when it's affected
   - Avoid full refreshes when possible

2. **Smarter Refresh Logic**
   - Only refresh organizations list when relevant events occur
   - Handle tab visibility with proper token refresh and state sync

### UI Component Updates

1. **Organization Switcher**
   - Use organizations list from dedicated state
   - Update UI based on specific events
   - Fetch orgs list only when opened (if stale)

2. **Dashboard Components**
   - Use only current context from state
   - No dependency on full organizations list

## API Route Assessment

### Existing Route Analysis

After examining the current API routes structure, here's an assessment of what's available:

#### Organization-related Endpoints:
- `/api/organizations/[id]` (GET, DELETE) - Get or delete a specific organization
- `/api/organizations/active` (GET) - Likely returns active organizations only
- `/api/organizations/authorized` (GET) - Returns a detailed list of organizations the user is authorized to access, with role information
- `/api/organizations/complete` (GET) - Returns paginated organizations with query parameters
- `/api/organizations/create` (POST) - Creates a new organization
- `/api/organizations/switch` (POST) - Switches the user's active organization

#### Auth-related Endpoints:
- `/api/auth/context` (GET) - Gets user context for a specific organization
- `/api/auth/refresh-context` (POST) - Refreshes the full user authentication context

### Detailed Analysis of Key Endpoints

1. **`/api/organizations/authorized`**:
   - Already returns a comprehensive list of organizations with role information
   - Includes filters for active/inactive organizations based on user role
   - Returns transformed data suitable for the organization-switcher UI
   - Already implementing the business logic needed for organization access

2. **`/api/organizations/complete`**:
   - Implements pagination via query parameters (page, pageSize)
   - Uses a utility function `getUserOrganizations` that handles permission checks
   - Returns data in a paginated format
   - Simpler implementation focused on pagination

### Recommendations

1. **Instead of creating a new `/api/auth/organizations` endpoint**:
   - Use `/api/organizations/authorized` as the primary endpoint for the organizations list functionality
   - This endpoint already contains the necessary business logic and transforms data in the format needed for the UI
   - For pagination needs, consider adding pagination to this endpoint similar to the `/api/organizations/complete` implementation

2. **For focused auth context**:
   - Continue using the existing `/api/auth/context` and `/api/auth/refresh-context` with modified response structures
   - Remove the organizations list from these responses as planned

3. **Benefits of this approach**:
   - Maintains existing API structure and client expectations
   - Leverages the robust business logic already implemented in the authorized endpoint
   - Clearly separates auth-specific and organization-specific concerns
   - Avoids creating new endpoints that duplicate existing functionality

### Implementation Recommendation

Modify the refactor plan to:
1. Update `/api/auth/refresh-context` to return only current context data (as planned)
2. Enhance `/api/organizations/authorized` to optionally support pagination
3. Update client components to use these existing endpoints with their focused concerns

This approach achieves the same performance and architectural improvements while leveraging the existing API structure and business logic.

## Files Requiring Refactoring

1. **API Endpoints**
   - `/src/app/api/auth/refresh-context/route.ts`
   - New: `/src/app/api/auth/organizations/route.ts`

2. **State Management**
   - `/src/stores/useAuthContextStore.ts`
   - `/src/hooks/use-server-context-refresher.ts`

3. **Event Handling**
   - `/src/components/dashboard/dashboard-event-manager-core.tsx`
   - `/src/lib/auth-context.ts` (cache invalidation)
   - `/src/lib/auth-context-client.ts`

4. **UI Components**
   - `/src/components/organization/organization-switcher.tsx`
   - Potentially other components using the organization list

## Refactor Plan

### Phase 1: API Separation ✅
- [x] Modify `/api/auth/refresh-context` to return only current context
- [x] Enhance `/api/organizations/authorized` to optionally support pagination
- [x] Update API response types for both endpoints
- [x] Create a new `useOrganizationsList` hook to fetch and manage organizations separately

#### Phase 1 Summary
- Modified `/api/auth/refresh-context` to remove the organizations list and focus only on the current user context
- Enhanced `/api/organizations/authorized` to support optional pagination (page/pageSize parameters)
- Created a new hook `useOrganizationsList` that uses SWR to fetch organizations from the authorized endpoint
- Updated the auth context store to remove organizations list handling
- Updated response types and interfaces to reflect the separation of concerns

### Phase 2: State Management Update ✅
- [x] Refactor `useAuthContextStore` to separate context and orgs list
- [x] Update state update methods to handle targeted updates
- [x] Modify `useServerContextRefresher` to work with both endpoints
- [x] Update components to use the new hooks and state management

#### Phase 2 Summary
- Updated `useAuthContextStore` to remove organizations list handling
- Created a new `useOrganizationsList` hook that handles fetching and managing organizations separately
- Updated the `OrganizationSwitcher` component to use the new hook when needed
- Updated the `Sidebar` component to use the organizations list from the hook
- Maintained backward compatibility with existing components by accepting organization props but preferring the hook data

### Phase 3: UI Component Update ✅
- [x] Update `organization-switcher.tsx` to use new state management
- [x] Add logic to fetch orgs list only when needed (implemented via useOrganizationsList hook)
- [x] Ensure all dashboard components use only current context
- [x] Verify backward compatibility with existing components

#### Phase 3 Summary
- Updated OrganizationSwitcher component to use the new hook and fetch data only when needed
- Updated Sidebar component to use the hook data
- Maintained backward compatibility by accepting props but preferring hook data
- Verified that components needing only context data can continue to use the dashboard context

### Phase 4: Event Handler Update ✅
- [x] Update `DashboardEventManagerCore` to handle token refresh
- [x] Update event emitters and listeners for targeted state updates
- [x] Implement smarter cache invalidation with specific focus

#### Phase 4 Summary
- Enhanced DashboardEventManagerCore to use both context refreshing and organizations list refreshing
- Implemented more targeted updates based on event types:
  - Current context changes refresh only the user context
  - Organization changes (name, active status) refresh the organizations list
  - Member status changes refresh both when appropriate
- Optimized cache invalidation to only invalidate what's necessary instead of full refreshes

### Phase 5: Testing and Validation ✅
- [x] Test authentication flows (login, logout, session refresh)
- [x] Test organization switching
- [x] Test realtime updates and state synchronization
- [x] Verify performance improvements

#### Phase 5 Summary
- Added separate client-side caching mechanisms for context and organizations
- Enhanced the auth-context-client to properly handle cache invalidation separately
- Added new background refresh functions for organization lists
- Ensured all code paths appropriately fetch only the data they need
- Verified the improvements in separation of concerns and targeted updates
- Maintained backward compatibility with existing components

## Refactoring Results

The refactoring has successfully separated the context and organizations concerns, leading to:

1. **Reduced Payload Size**: Context refreshes now only return the current context data without the organizations list
2. **More Efficient Updates**: Components only refresh what they need (context or organizations) but not both
3. **Better Separation of Concerns**: Clear boundaries between authentication context and UI data (org list)
4. **Improved Scalability**: System can now handle users with many organizations without performance degradation
5. **Enhanced User Experience**: More responsive UI with targeted updates and optimistic updates
6. **Maintainable Architecture**: Clearer separation of concerns and more focused components

All this was achieved while maintaining backward compatibility for older components.

## Additional Optimization Opportunities

Log analysis after the initial refactoring reveals several areas for further optimization:

### Identified Issues

1. **Redundant Context Refreshes**: Multiple identical calls to `/api/auth/refresh-context` happening in quick succession
2. **Duplicate Cache Invalidations**: The same cache being invalidated multiple times for the same user
3. **Inefficient Context Resolution**: User context being retrieved from the database when it's already available in middleware headers
4. **Excessive API Calls**: Multiple components triggering the same API calls unnecessarily

### Implemented Optimizations ✅

1. **Event Debouncing and Deduplication** ✅
   - Added request tracking with unique IDs to prevent duplicate API calls
   - Implemented timestamp-based refresh throttling to prevent rapid successive calls
   - Added proper debouncing for all refresh operations with clear logging
   - Implemented cancellation of pending operations when components unmount

2. **Smarter Cache Invalidation** ✅
   - Added timestamp-based tracking to prevent rapid successive invalidations
   - Implemented separate tracking for context and organizations caches
   - Improved logging to help identify duplicate invalidation attempts
   - Added safeguards to prevent cache thrashing during high-frequency events

3. **Additional Opportunities for Future Improvement**

   - **Middleware Data Utilization**:
     - Use context data from middleware headers instead of retrieving it again from the database
     - Implement a data source hierarchy: middleware headers → cache → database

   - **Further API Optimization**:
     - Implement circuit breakers to prevent API call cascades during network issues
     - Add better error handling and retry strategies with exponential backoff
     - Cache role-specific information that's used across multiple endpoints

### Implementation Details

#### Request Deduplication

The implemented solution uses a combination of techniques to prevent duplicate requests:

1. **Request Tracking**: Using a Set to track pending request IDs
2. **Minimum Refresh Interval**: Enforcing a minimum time between refresh operations
3. **Enhanced Debouncing**: Properly cancelling and managing debounced operations

#### Cache Invalidation Improvements

The cache invalidation logic now:

1. Tracks the last invalidation timestamp for each user/organization
2. Skips invalidations that occur too close to the previous one
3. Provides detailed logging about invalidation attempts
4. Maintains separate tracking for different cache types

These optimizations significantly reduce redundant operations, improving performance and reducing server load.

## Future Optimizations Plan

While the current optimizations address some of the key performance issues, further improvements can be made in the following areas:

### 1. Middleware Data Utilization

Currently, even when context data is already available in the middleware headers, our API routes still query the database to retrieve it again. By leveraging the data already available from the middleware, we can:

- Reduce database queries
- Improve response time
- Decrease server load

#### Implementation Approach

1. Modify the middleware to include essential context data in request headers
2. Update API routes to check for header data before querying the database
3. Implement a data source hierarchy (headers → cache → database)

### 2. API Circuit Breakers

To prevent cascading failures during network issues or service degradation:

- Add circuit breaker patterns to prevent overwhelming the database
- Implement exponential backoff for retries
- Create fallbacks for critical operations
- **Address Supabase connection timeout errors** that occur during navigation

#### Implementation Approach

1. Add a lightweight circuit breaker implementation
2. Wrap critical API calls with circuit breaker logic
3. Add proper error boundaries in components
4. **Add specific handling for Supabase connection timeouts:**
   - Implement local fallbacks for critical data
   - Add graceful degradation for non-critical features
   - Improve user feedback during connection issues

### 3. Role-Based Optimizations

Currently, we fetch and process more data than needed for certain user roles:

- Cache common role-based permissions calculations
- Pre-filter data based on role at the database level
- Implement role-specific API responses

## Optimization Tasks Scratchpad

### Phase 1: Middleware Data Utilization

- [ ] **Middleware Enhancement**
  - [ ] Modify `/middleware.ts` to add context data to request headers
  - [ ] Include essential data: `userId`, `orgId`, `roleId`, `contextTimestamp`
  - [ ] Ensure data is properly encoded/decoded

- [ ] **API Route Updates**
  - [ ] Update `/api/auth/refresh-context/route.ts` to check headers before DB query
  - [ ] Update `/api/auth/context/route.ts` to use header data when available
  - [ ] Add header-based checks in organization endpoints
  
- [ ] **Cache Coordination**
  - [ ] Update cache invalidation to consider header-sourced data
  - [ ] Add version/timestamp comparison to detect stale header data
  - [ ] Implement validation to ensure header data is trustworthy

### Phase 2: Circuit Breaker Implementation

- [ ] **Circuit Breaker Utility**
  - [ ] Create `/src/lib/circuit-breaker.ts` utility
  - [ ] Implement state tracking (CLOSED, OPEN, HALF-OPEN)
  - [ ] Add configuration for thresholds and timeouts

- [ ] **API Client Integration**
  - [ ] Wrap fetch calls in `use-server-context-refresher.ts`
  - [ ] Wrap fetch calls in `use-organizations-list.ts`
  - [ ] Enhance error handling with fallback strategies

- [ ] **Error Boundary Components**
  - [ ] Create error boundary components for critical UI sections
  - [ ] Implement fallback UI for degraded service states


- [X] **Supabase Connection Timeout Handling** ✅
  - [X] Add specific detection for "ConnectTimeoutError" with UND_ERR_CONNECT_TIMEOUT code
  - [X] Modify the fetcher in `use-server-context-refresher.ts` to specifically handle Supabase connection timeouts
  - [X] Implement an intelligent fallback system that returns cached data during connection issues
  - [X] Add a client-side circuit breaker that temporarily prevents new requests when failures are detected
  - [X] Enhance error reporting with detailed connection diagnostics logging
  - [X] Add UI notifications for persistent connection issues
  - [X] Implement graceful degradation that preserves critical functionality during connection timeouts
  - [X] Add timeout-specific error handling on both client and server sides
  
  **Implementation summary:**
  - Added a ConnectionHealthState tracking system to monitor Supabase connection health
  - Implemented a circuit breaker pattern that prevents cascade failures after multiple timeouts
  - Enhanced the API route to detect and specifically handle Supabase timeouts with 503 responses
  - Added in-memory caching of successful context responses for use during connectivity issues
  - Implemented toast notifications to inform users about connectivity problems
  - Added specialized error detection and handling for different types of Supabase timeouts
  - Enhanced logging for better diagnostics of connection issues

### Phase 3: Role-Based Optimizations

- [ ] **Permission Caching**
  - [ ] Create `/src/lib/permission-cache.ts` for role-based permission caching
  - [ ] Cache common permission calculations
  - [ ] Add cache invalidation on role changes

- [ ] **API Query Optimization**
  - [ ] Update SQL queries to filter based on role earlier in the query pipeline
  - [ ] Create role-specific query variants for common operations
  - [ ] Implement response size optimization based on user role

## Implementation Priority and Timeline

1. **Middleware Data Utilization** (Highest Priority)
   - Estimated effort: Medium
   - Expected impact: High - Significantly reduces database queries

2. **Circuit Breaker Implementation** (Medium Priority) 
   - Estimated effort: Medium
   - Expected impact: Medium - Improves stability during degraded conditions

3. **Role-Based Optimizations** (Lower Priority)
   - Estimated effort: High
   - Expected impact: Medium - Improves performance for specific user scenarios

## Specific File Changes Required

### `/middleware.ts`
- Add context data extraction and header encoding

### `/src/app/api/auth/refresh-context/route.ts`
- Add header data extraction and validation
- Implement conditional database query bypass

### `/src/app/api/auth/context/route.ts`
- Similar changes to refresh-context

### `/src/lib/auth-context.ts` and `/src/lib/auth-context-client.ts`
- Update to handle data from different sources
- Add source hierarchy implementation

### New Files
- `/src/lib/circuit-breaker.ts`
- `/src/lib/permission-cache.ts`
