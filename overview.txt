# Project Structure Map

## Core Architecture
- Next.js 15 App Router application with server-first approach
- Supabase for authentication, database, and storage
- Shadcn UI components with Tailwind CSS styling
- Role-based access control (RBAC) system
- Development Operating System is Windows
- IDE is VSCode

## Directory Structure

```
/
├── src/
│   ├── app/                     # Next.js App Router routes
│   │   ├── actions/             # Server actions
│   │   ├── api/                 # API routes
│   │   ├── auth/                # Authentication routes
│   │   │   ├── callback/        # Auth callback handling
│   │   │   ├── login/           # Login page
│   │   │   └── signup/          # Signup page
│   │   ├── dashboard/           # Main application area
│   │   │   ├── admin/           # Admin routes
│   │   │   │   ├── organization/ # Organization management
│   │   │   │   │   ├── create-organization/ # Create org (bypassed by middleware)
│   │   │   │   │   └── [orgId]/  # Specific organization pages
│   │   │   │   ├── users/        # User management
│   │   │   │   ├── invites/      # User invitations
│   │   │   │   └── flows/        # Workflow management
│   │   │   ├── handbooks/        # Handbooks section
│   │   │   ├── profile/          # User profile section
│   │   │   ├── settings/         # Application settings
│   │   │   └── rbac-test/        # RBAC testing routes
│   │   ├── layout.tsx           # Root layout
│   │   └── page.tsx             # Landing page
│   ├── components/              # UI components
│   │   ├── ui/                  # Shadcn UI components
│   │   ├── auth/                # Authentication components
│   │   ├── dashboard/           # Dashboard components
│   │   ├── navigation/          # Navigation components
│   │   │   ├── Sidebar.tsx      # Main sidebar navigation
│   │   │   └── MobileSidebar.tsx # Mobile navigation
│   │   ├── organization/        # Organization components
│   │   └── shared/              # Shared components
│   ├── data/                    # Data fetching and models
│   ├── hooks/                   # Custom React hooks
│   ├── lib/                     # Core utilities
│   │   ├── rbac/                # Role-based access control
│   │   │   ├── roles.ts         # Role definitions and permissions
│   │   │   ├── permissions-server.ts # Server-side permissions
│   │   │   ├── middleware.ts    # RBAC middleware functions
│   │   │   └── server-action.ts # Secured server actions
│   │   ├── supabase/            # Supabase client utilities
│   │   ├── auth-utils.ts        # Authentication utilities
│   │   └── organization-utils-server.ts # Organization utilities
│   ├── middleware.ts            # Next.js middleware for auth and routing
│   ├── providers/               # React context providers
│   ├── types/                   # TypeScript type definitions
│   │   ├── handbook.ts          # Handbook-related types
│   │   ├── navigation.ts        # Navigation menu types
│   │   ├── organization.ts      # Organization-related types
│   │   └── flows.ts             # Workflow-related types
│   └── utils/                   # Utility functions
├── supabase/                    # Supabase configuration
│   └── migrations/              # Database migrations
├── public/                      # Static assets
└── handbook_data/               # Handbook content data
```

## Key Files

- @middleware.ts - Controls routing, authentication and permission checks
- @roles.ts - Defines role hierarchy and permissions
- @client.ts - Supabase client setup
- @layout.tsx - Root layout with providers
- @layout.tsx - Dashboard layout with navigation

## Authentication Flow

1. User navigates to `/auth/login`
2. Login handled by @src/components/login-form.tsx
3. Upon success, redirected to callback route
4. Auth callback sets cookies and redirects to dashboard
5. Middleware validates authentication on protected routes

## RBAC System

### Role Hierarchy (from highest to lowest)
1. Superadmin (ID: 1) - Full system access
2. Support Admin (ID: 2) - Can manage all organizations
3. Organization Admin (ID: 3) - Can manage their organization
4. Organization Member (ID: 4) - Basic member access
5. Organization Accounting (ID: 5) - Basic member with accounting permissions
6. Organization Client (ID: 6) - Most restricted access

### Core Components
- @roles.ts - Role and permission definitions
- @permissions-server.ts - Server-side permission checks
- @middleware.ts - Route protection and redirection
- @server-action.ts - Protected server actions

### Database Schema (Key Tables)
- organizations - Organization data
- organization_members - User membership in organizations
- profiles - User profile information
- roles - Role definitions

## UI Components

### Navigation Structure
- @Sidebar.tsx - Main sidebar navigation
  - Uses navigation data from @src/data/navigation.ts
  - Filters menu items based on user permissions
  - Includes organization switcher
  - Supports collapsed/expanded states
- @MobileSidebar.tsx - Mobile navigation menu
- @MenuItem.tsx - Individual menu item component
- @TopBar.tsx - Top navigation bar

### Shadcn UI Components (@src/components/ui/*)
- Button (@components/ui/button.tsx)
- Input (@components/ui/input.tsx)
- Card (@components/ui/card.tsx)
- Dialog (@components/ui/dialog.tsx)
- Select (@components/ui/select.tsx)
- Table (@components/ui/table.tsx)
- Tabs (@components/ui/tabs.tsx)
- Form (@components/ui/form.tsx)
- Toast (@components/ui/toast.tsx)
- Accordion (@components/ui/accordion.tsx)
- Alert (@components/ui/alert.tsx)
- Breadcrumb (@components/ui/breadcrumb.tsx)
- And many others following the same pattern

### Authentication
- @login-form.tsx - Login form component
- @src/components/auth/* - Auth-related components

### Dashboard
- @src/components/dashboard/* - Dashboard components
- @Charts.tsx - Data visualization

## Navigation System

### Structure
Navigation is defined in @navigation.ts and organized into sections:
```typescript
export const navigationData: NavigationSection[] = [
  {
    title: "Main",
    items: [
      {
        label: "Dashboard",
        icon: "home",
        href: "/dashboard",
      },
      // More items...
    ]
  },
  // More sections...
]
```

### Permission-Based Filtering
The Sidebar component filters navigation items based on user permissions:
```typescript
const filteredNavigation = React.useMemo(() => {
  return navigationData
    .map((section) => ({
      ...section,
      items: section.items.filter((item: NavigationItem) => {
        // If no required permissions are specified, allow access to all
        if (!item.requiredPermissions) return true;

        // Check if user has all required permissions
        return permissions.canAll(item.requiredPermissions);
      }),
    }))
    .filter((section) => section.items.length > 0);
}, [permissions]);
```

### Organization Context
- Navigation happens within the context of the active organization
- OrganizationSwitcher component allows changing context
- Active organization ID is stored in cookies and retrieved by middleware

## Server Actions

Server actions are defined in:
- @src/app/actions/* - General server actions
- Various component directories for component-specific actions

## Key Routes

- `/` - Landing page
- `/auth/login` - Login page
- `/auth/signup` - Signup page
- `/auth/callback` - Authentication callback
- `/dashboard` - Main dashboard
- `/dashboard/admin/*` - Admin routes (protected)
- `/dashboard/admin/organization/create-organization` - Create organization (special bypass)
- `/dashboard/profile` - User profile
- `/dashboard/settings` - Application settings
- `/dashboard/handbooks` - Handbooks section

## Middleware Behavior

The middleware (@src/middleware.ts) controls:
1. Authentication checks - Redirects to login if not authenticated
2. Role-based access control - Checks user roles against required permissions
3. Organization context - Checks active organization and membership
4. Special route bypasses for certain paths

### Bypass Paths
The middleware contains a list of paths that bypass RBAC checks:
```typescript
const BYPASS_PATHS = [
  '/dashboard/admin/organization/create-organization',
  '/dashboard/admin/organization/create-organization/',
  '/dashboard/admin/organization/create-organization-simple',
  '/dashboard/admin/organization/create-organization-simple/',
  '/dashboard/admin/organization/create-organization-test',
  '/dashboard/admin/organization/create-organization-test/',
  '/dashboard/admin/invites',
  '/dashboard/admin/invites/',
  '/auth/callback',
  '/auth/callback/',
  '/auth/v1/callback',
  '/auth/v1/callback/',
  '/auth/login',
  '/auth/login/',
]
```

### Middleware Flow
1. Check if request path is in BYPASS_PATHS list
2. If bypassed, allow access immediately
3. Otherwise, initialize Supabase client
4. Get user with retry logic
5. Check auth status and redirect if needed
6. For admin routes, check additional permissions
7. For organization routes, check membership

### Admin Routes Logic
Admin routes (`/dashboard/admin/*`) require:
1. Active organization cookie
2. User to be a member of that organization
3. User to have admin permissions
4. For organization-specific routes, check membership to target org

## Type Definitions

### Organization Types @organization.ts
```typescript
export interface Organization {
  id: string
  name: string
  role: string
  org_icon: string
  org_member_role: number
  isActive: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

export interface OrganizationMember {
  userId: string
  organizationId: string
  role: string
  joinedAt: string
}
```

### Navigation Types @navigation.ts
```typescript
export interface NavigationItem {
  label: string
  icon: string
  href?: string
  requiredPermissions?: Permission[]
  submenu?: NavigationItem[]
}

export interface NavigationSection {
  title: string
  items: NavigationItem[]
}
```

### Handbook Types @handbook.ts
- HandbookProps: Main props interface for the Handbook component
- Service: Service information interface
- Criteria: Eligibility criteria interface
- Procedures: Service procedures interface
- RequiredDocuments: Required documents interface
- Fee: Service fees interface
- ServiceChannels: Service locations interface
- ComplaintChannels: Complaint channels interface
- ApplicationForms: Application forms interface
- BottomRemarks: Additional information interface
- Source: Source information interface

### Flow Types @flows.ts
- Workflow-related interfaces for defining process flows

## Component Import Pattern

```typescript
// UI components
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

// Custom components
import { Sidebar } from "@/components/Sidebar"
import { LoginForm } from "@/components/login-form"

// Utilities
import { createClient } from "@/lib/supabase/client"
import { hasPermission } from "@/lib/rbac/roles"
```

## Forms and Data Management

### Organization Creation
- Path: `/dashboard/admin/organization/create-organization`
- Component: @src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx
- Process:
  1. Form collects organization name and settings
  2. On submit, creates organization record in Supabase
  3. Automatically adds superadmin and supportadmin users as members
  4. Redirects back to organization list

### Form Patterns
- Uses React Hook Form for form state management
- Shadcn UI components for inputs and controls
- Server-side validation with Zod schemas
- Client-side error handling with toast notifications

## Custom Hooks
Key custom hooks include:
- usePermissions - Access to permission checking functions
- useDashboard - Dashboard context and state management
- useOrganizations - Organization data and management 

## Import Relations

### RBAC Imports

#### import { Permission } from "@/lib/rbac/roles"
- src/types/navigation.ts
- src/components/MenuItem.tsx

#### import { Permissions } from "@/lib/rbac/roles"
- src/data/navigation.ts
- src/components/organization/organization-list.tsx
- src/app/dashboard/rbac-test/page.tsx
- src/app/dashboard/admin/users/user-table.tsx
- src/app/dashboard/admin/users/page.tsx

#### import { Permission, Permissions } from "@/lib/rbac/roles"
- src/data/navigation.ts

#### import { WithPermissions } from "@/lib/rbac"
- src/app/dashboard/admin/users/page.tsx

#### import { hasPermission } from "@/lib/rbac/roles"
- (Referenced in multiple components for permission checks)

### Supabase Client Imports

#### import { createClient } from "@/lib/supabase/client" (Client-side)
- src/providers/organization-check-provider.tsx
- src/components/user-avatar-menu.tsx
- src/components/organization/all-invites-list.tsx
- src/components/organization/create-invite-dialog.tsx
- src/components/organization/invites-list.tsx
- src/components/organization/create-invite-button.tsx
- src/components/dashboard/dashboard-header.tsx
- src/app/dashboard/admin/users/user-table.tsx
- src/app/dashboard/admin/organization/organization-table.tsx
- src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx
- src/components/auth/user-auth-form.tsx
- src/components/auth/google-auth-button.tsx

#### import { createClient } from "@/lib/supabase/server" (Server-side)
- src/app/dashboard/layout.tsx
- src/app/dashboard/profile/page.tsx
- src/app/dashboard/admin/users/page.tsx
- src/app/dashboard/admin/layout.tsx
- src/app/dashboard/admin/organization/[orgId]/invites/page.tsx
- src/app/dashboard/admin/invites/page.tsx

### Toast Message Imports

#### import { toastMessages } from "@/lib/toast-messages"
- src/components/organization/organization-switcher.tsx
- src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx
- src/components/profile/ProfileForm.tsx
- (Should be used in all components that previously used direct sonner imports or useToast hook)

### UI Component Imports

#### import { Button } from "@/components/ui/button"
- src/components/profile/ProfileForm.tsx
- src/components/no-access.tsx
- src/components/organization/create-invite-button.tsx
- src/components/organization/invite-code-display.tsx
- src/components/organization/organization-list.tsx
- src/components/organization/join-organization-modal.tsx
- src/components/organization/invites-list.tsx
- src/components/organization/create-invite-dialog.tsx
- src/components/organization/all-invites-list.tsx
- src/components/ModeToggle.tsx
- src/components/login-form.tsx
- src/components/flowsteps/FlowStepEditDialog.tsx
- src/components/labels/LabelEditDialog.tsx
- src/components/flowsteps/FlowStepCategoryEditDialog.tsx
- src/components/labels/LabelCategoryEditDialog.tsx
- src/components/flows/FlowCard.tsx
- src/components/dashboard/dashboard-header.tsx
- src/app/page.tsx
- src/components/auth/user-auth-form.tsx
- src/components/auth/google-auth-button.tsx
- src/app/dashboard/rbac-test/page.tsx
- src/app/dashboard/handbooks/handbook-search.tsx
- src/app/dashboard/admin/users/user-table.tsx
- src/app/dashboard/admin/labels/page.tsx
- src/app/dashboard/admin/organization/organization-table.tsx
- src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx
- src/app/dashboard/admin/flowsteps/page.tsx
- src/app/dashboard/admin/flows/page.tsx

#### import { Input } from "@/components/ui/input"
- src/components/profile/ProfileForm.tsx
- src/components/organization/organization-list.tsx
- src/components/organization/join-organization-modal.tsx
- src/components/login-form.tsx
- src/components/flowsteps/FlowStepEditDialog.tsx
- src/components/labels/LabelEditDialog.tsx
- src/components/flowsteps/FlowStepCategoryEditDialog.tsx
- src/components/labels/LabelCategoryEditDialog.tsx
- src/components/flows/FlowCard.tsx
- src/components/auth/user-auth-form.tsx
- src/app/dashboard/admin/users/user-table.tsx
- src/app/dashboard/admin/labels/page.tsx
- src/app/dashboard/admin/organization/organization-table.tsx
- src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx
- src/app/dashboard/admin/flowsteps/page.tsx
- src/app/dashboard/admin/flows/page.tsx

#### import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
- src/components/flows/FlowCard.tsx
- src/components/country-selector.tsx
- src/app/dashboard/rbac-test/page.tsx
- src/app/dashboard/handbooks/page.tsx
- src/app/dashboard/admin/labels/page.tsx
- src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx
- src/app/dashboard/admin/flowsteps/page.tsx

#### import { Label } from "@/components/ui/label"
- src/components/profile/ProfileForm.tsx
- src/components/organization/create-invite-button.tsx
- src/components/login-form.tsx
- src/components/flowsteps/FlowStepEditDialog.tsx
- src/components/labels/LabelEditDialog.tsx
- src/components/flowsteps/FlowStepCategoryEditDialog.tsx
- src/components/labels/LabelCategoryEditDialog.tsx
- src/components/auth/user-auth-form.tsx
- src/app/dashboard/admin/organization/create-organization/create-organization-form.tsx

### Custom Hooks Imports

#### import { usePermissions } from "@/hooks/use-permissions"
- src/components/Sidebar.tsx
- src/components/organization/organization-list.tsx
- src/components/MenuItem.tsx
- src/app/dashboard/rbac-test/page.tsx
- src/app/dashboard/admin/users/user-table.tsx
- src/app/dashboard/admin/organization/organization-table.tsx

#### import { useToast } from "@/hooks/use-toast"
- src/components/ui/toaster.tsx
- src/components/profile/ProfileForm.tsx
- src/components/auth/google-auth-button.tsx

#### import { useOrganizationStorage } from "@/hooks/use-organization-storage"
- src/components/organization/organization-switcher.tsx

### Provider Imports

#### import { useDashboard } from "@/components/providers/dashboard-provider"
- src/components/Sidebar.tsx

### Most Common Imports

The most frequently imported components/utilities across the project are:
1. Button from @components/ui/button
2. Input from @components/ui/input
3. createClient from @lib/supabase/client (client-side)
4. usePermissions from @hooks/use-permissions
5. Label from @components/ui/label
6. Card components from @components/ui/card
7. Permissions from @lib/rbac/roles

### Import Patterns

1. UI component imports follow the pattern:
   ```typescript
   import { ComponentName } from "@/components/ui/component-name"
   ```

2. Supabase client imports are split between:
   - Client-side: `import { createClient } from "@/lib/supabase/client"`
   - Server-side: `import { createClient } from "@/lib/supabase/server"`

3. RBAC imports follow:
   ```typescript
   import { Permission, Permissions } from "@/lib/rbac/roles"
   ```

4. Custom hooks follow:
   ```typescript
   import { useHookName } from "@/hooks/use-hook-name"
   ```
