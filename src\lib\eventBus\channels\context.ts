// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import { AUTH_CONTEXT_CHANGED } from '../constants'
import type { OrganizationMemberInsertedEvent, OrganizationMemberUpdatedEvent, AuthEvent } from '@/lib/eventTypes'

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organization_members:* events and emit organization:context:changed events as appropriate.
 */
export function registerContextInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    // console.log('[ContextInterpreter] Already registered, skipping duplicate registration')
    return
  }

  // REMOVE OR COMMENT OUT THE LINE BELOW:
  // console.log('[ContextInterpreter] Registered', typeof window !== 'undefined' ? 'in browser' : 'on server')
  isRegistered = true

  // Handle INSERT (new default org)
  emitter.on('db:organization_members:inserted', (event: OrganizationMemberInsertedEvent) => {
    const { orgId, userId, data, timestamp } = event
    if (!orgId || !userId || !data.is_default_org) return

    const authEvent: AuthEvent = {
      userId,
      orgId,
      reason: 'Organization context changed - new default organization',
      timestamp,
    }
    clientLog(`[EventBus] New default organization for user ${userId} is org ${orgId}`)
    console.log('[ContextInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
    emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
  })

  // Handle UPDATE (default org changed)
  emitter.on('db:organization_members:updated', (event: OrganizationMemberUpdatedEvent) => {
    const { orgId, userId, data, timestamp } = event



    // Handle is_default_org changes
    if (orgId && userId && typeof data.is_default_org === 'boolean' && typeof data.old_is_default_org === 'boolean') {
      if (data.is_default_org !== data.old_is_default_org && data.is_default_org) {
        const authEvent: AuthEvent = {
          userId,
          orgId,
          reason: 'Organization context changed - default organization updated',
          timestamp,
        }
        clientLog(`[EventBus] Default organization updated for user ${userId} to org ${orgId}`)
        console.log('[ContextInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
        emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
      }
    }

    // Handle is_current_context changes (NEW - for cross-window synchronization)
    if (orgId && userId && typeof data.is_current_context === 'boolean' && typeof data.old_is_current_context === 'boolean') {
      if (data.is_current_context !== data.old_is_current_context && data.is_current_context === true) {
        const authEvent: AuthEvent = {
          userId,
          orgId,
          reason: 'Organization context changed - current context updated',
          timestamp,
        }
        clientLog(`[EventBus] Current context changed for user ${userId} to org ${orgId} - syncing all windows`)
        console.log('[ContextInterpreter] Emitting AUTH_CONTEXT_CHANGED event:', authEvent)
        emitter.emit(AUTH_CONTEXT_CHANGED, authEvent)
      }
    }
  })
}