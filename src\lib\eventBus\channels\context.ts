// NOTE: This module-level registration must NOT use useBusEvent or any React hook!
//       This service is intended for one-time registration of DB event translation.

import { emitter, clientLog } from '../emitter'
import type { OrganizationMemberInsertedEvent, OrganizationMemberUpdatedEvent, OrganizationContextEvent } from '@/lib/eventTypes'

// Use a module-level variable to track registration
let isRegistered = false

/**
 * Listen for raw db:organization_members:* events and emit organization:context:changed events as appropriate.
 */
export function registerContextInterpreter() {
  // Prevent duplicate registration
  if (isRegistered) {
    // console.log('[ContextInterpreter] Already registered, skipping duplicate registration')
    return
  }

  // REMOVE OR COMMENT OUT THE LINE BELOW:
  // console.log('[ContextInterpreter] Registered', typeof window !== 'undefined' ? 'in browser' : 'on server')
  isRegistered = true

  // Handle INSERT (new default org)
  emitter.on('db:organization_members:inserted', (event: OrganizationMemberInsertedEvent) => {
    const { orgId, userId, data, timestamp } = event
    if (!orgId || !userId || !data.is_default_org) return
    const contextEvent: OrganizationContextEvent = {
      userId,
      orgId,
      timestamp,
    }
    clientLog(`[EventBus] New default organization for user ${userId} is org ${orgId}`)
    emitter.emit('organization:context:changed', contextEvent)
  })

  // Handle UPDATE (default org changed)
  emitter.on('db:organization_members:updated', (event: OrganizationMemberUpdatedEvent) => {
    const { orgId, userId, data, timestamp } = event



    // Handle is_default_org changes
    if (orgId && userId && typeof data.is_default_org === 'boolean' && typeof data.old_is_default_org === 'boolean') {
      if (data.is_default_org !== data.old_is_default_org && data.is_default_org) {
        const contextEvent: OrganizationContextEvent = {
          userId,
          orgId,
          timestamp,
        }
        clientLog(`[EventBus] Default organization updated for user ${userId} to org ${orgId}`)
        emitter.emit('organization:context:changed', contextEvent)
      }
    }

    // Handle is_current_context changes (NEW - for cross-window synchronization)
    if (orgId && userId && typeof data.is_current_context === 'boolean' && typeof data.old_is_current_context === 'boolean') {
      if (data.is_current_context !== data.old_is_current_context && data.is_current_context === true) {
        const contextEvent: OrganizationContextEvent = {
          userId,
          orgId,
          timestamp,
        }
        clientLog(`[EventBus] Current context changed for user ${userId} to org ${orgId} - syncing all windows`)
        emitter.emit('organization:context:changed', contextEvent)

        // Note: AUTH_CONTEXT_CHANGED will be emitted by useAuthEvents.ts when it handles organization:context:changed
        // No need to emit it here to avoid duplicate events
      }
    }
  })
}