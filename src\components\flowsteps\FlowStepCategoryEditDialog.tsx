import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { FlowStepCategory } from "@/types/app/dashboard/admin/flowsteps/FlowStepCategory";
import { FlowStepCategoryEditDialogProps } from "@/types/components/flowsteps/FlowStepCategoryEditDialogProps";

export function FlowStepCategoryEditDialog({
  flowStepCategory,
  isOpen,
  onClose,
  onSave,
}: FlowStepCategoryEditDialogProps) {
  const [formData, setFormData] = useState<Partial<FlowStepCategory>>({
    name: "",
  });

  useEffect(() => {
    if (flowStepCategory) {
      setFormData(flowStepCategory);
    } else {
      setFormData({ name: "" });
    }
  }, [flowStepCategory]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {flowStepCategory ? "Edit Category" : "Add Category"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              required
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
