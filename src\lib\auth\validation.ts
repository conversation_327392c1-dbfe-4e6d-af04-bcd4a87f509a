import { z } from 'zod';

// Email validation schema with comprehensive checks
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .max(254, 'Email is too long') // RFC 5321 limit
  .email('Please enter a valid email address')
  .refine((email) => {
    // Additional email validation rules
    const parts = email.split('@');
    if (parts.length !== 2) return false;

    const [localPart, domain] = parts;

    // Local part validation
    if (localPart.length > 64) return false; // RFC 5321 limit
    if (localPart.startsWith('.') || localPart.endsWith('.')) return false;
    if (localPart.includes('..')) return false;

    // Domain validation
    if (domain.length > 253) return false; // RFC 5321 limit
    if (domain.startsWith('-') || domain.endsWith('-')) return false;
    if (!domain.includes('.')) return false;

    // Check for valid characters
    const validLocalChars = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+$/;
    const validDomainChars = /^[a-zA-Z0-9.-]+$/;

    return validLocalChars.test(localPart) && validDomainChars.test(domain);
  }, 'Please enter a valid email address')
  .transform((email) => email.toLowerCase().trim()); // Normalize email

// Magic link request schema
export const magicLinkRequestSchema = z.object({
  email: emailSchema,
  // Honeypot field - should always be empty
  website: z.string().max(0, 'Invalid request').optional(),
  // CSRF token
  token: z.string().min(1, 'Invalid request').optional(),
  // CAPTCHA token from Turnstile (validated by Supabase)
  captchaToken: z.string().optional(),
  // Client info for security logging
  userAgent: z.string().optional(),
  timestamp: z.number().optional(),
});

export type MagicLinkRequest = z.infer<typeof magicLinkRequestSchema>;

// Rate limiting configuration
export const RATE_LIMITS = {
  // Per email address
  EMAIL_REQUESTS_PER_HOUR: 3,
  EMAIL_REQUESTS_PER_DAY: 10,

  // Per IP address
  IP_REQUESTS_PER_MINUTE: 5,
  IP_REQUESTS_PER_HOUR: 20,
  IP_REQUESTS_PER_DAY: 100,

  // Cooldown between requests
  COOLDOWN_SECONDS: 60,
} as const;

// Security validation helpers
export function validateEmailFormat(email: string): { isValid: boolean; error?: string } {
  try {
    emailSchema.parse(email);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0]?.message || 'Invalid email' };
    }
    return { isValid: false, error: 'Invalid email format' };
  }
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
}

// Common email domains for additional validation
export const COMMON_EMAIL_DOMAINS = [
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com',
  'protonmail.com', 'aol.com', 'live.com', 'msn.com', 'mail.com'
];

// Suspicious patterns to flag
export const SUSPICIOUS_PATTERNS = [
  /test@test\.com/i,
  /admin@admin\.com/i,
  /noreply@/i,
  /no-reply@/i,
  /donotreply@/i,
  /\+.*\+.*@/i, // Multiple plus signs
  /\.{2,}/, // Multiple consecutive dots
];

export function isSuspiciousEmail(email: string): boolean {
  return SUSPICIOUS_PATTERNS.some(pattern => pattern.test(email));
}
