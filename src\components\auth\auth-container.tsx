"use client";

import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { BookDashed } from "lucide-react";
import type { AuthContainerProps } from "@/types/components/auth/AuthContainerProps";

export function AuthContainer({ children }: AuthContainerProps) {
  const pathname = usePathname();
  const isLogin = pathname === "/auth/login";

  return (
    <div className="container relative min-h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative flex-col p-6 lg:flex items-center justify-center bg-gray-50">
        <div className="relative z-20 flex items-center text-lg font-medium mb-6">
          <BookDashed className="h-6 w-6 mr-2" />
          <span>Welcome back to Agency Forms</span>
        </div>
        <div className="absolute inset-0 bg-gray-50" />
        <div className="relative z-10 w-full">
          <div className="w-full max-w-[500px] mx-auto space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={pathname}
                initial={{ x: isLogin ? -100 : 100, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: isLogin ? 100 : -100, opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                {children}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
      <div className="relative hidden h-full flex-col bg-[#0A2C35] p-10 text-white dark:border-r lg:flex">
        <div className="absolute inset-0 bg-[#0A2C35]" />
        <div
          className="absolute inset-0 opacity-100"
          style={{
            backgroundImage: "url(/images/grid.png)",
            backgroundRepeat: "repeat",
          }}
        />

        <div className="relative z-20 flex flex-col justify-center flex-1">
          <h2 className="text-2xl font-bold mb-8 text-center">
            Trusted by Users worldwide
          </h2>
          <div className="space-y-6 max-w-[600px] mx-auto">
            <div className="bg-[#194852] rounded-lg p-6 shadow-[0_8px_30px_rgb(0,0,0,0.06)] backdrop-blur-sm border border-white/[0.03]">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 flex items-center justify-center bg-white text-[#0A2C35] rounded-full font-semibold mr-3 shadow-sm">
                  SC
                </div>
                <div>
                  <div className="font-medium">Sarah Chen</div>
                  <div className="text-sm opacity-80">CTO, TechStart</div>
                </div>
              </div>
              <blockquote>
                <p className="text-sm leading-relaxed">
                  &quot;The template helped us launch our SaaS product in just
                  two weeks. The authentication and multi-tenancy features are
                  rock solid.&quot;
                </p>
              </blockquote>
            </div>

            <div className="bg-[#194852] rounded-lg p-6 shadow-[0_8px_30px_rgb(0,0,0,0.06)] backdrop-blur-sm border border-white/[0.03]">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 flex items-center justify-center bg-white text-[#0A2C35] rounded-full font-semibold mr-3 shadow-sm">
                  MR
                </div>
                <div>
                  <div className="font-medium">Michael Roberts</div>
                  <div className="text-sm opacity-80">Founder, DataFlow</div>
                </div>
              </div>
              <blockquote>
                <p className="text-sm leading-relaxed">
                  &quot;The best part is how well thought out the organization
                  management is. It saved us months of development time.&quot;
                </p>
              </blockquote>
            </div>

            <div className="bg-[#194852] rounded-lg p-6 shadow-[0_8px_30px_rgb(0,0,0,0.06)] backdrop-blur-sm border border-white/[0.03]">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 flex items-center justify-center bg-white text-[#0A2C35] rounded-full font-semibold mr-3 shadow-sm">
                  JK
                </div>
                <div>
                  <div className="font-medium">Jessica Kim</div>
                  <div className="text-sm opacity-80">
                    Lead Developer, CloudScale
                  </div>
                </div>
              </div>
              <blockquote>
                <p className="text-sm leading-relaxed">
                  &quot;Clean code, great documentation, and excellent support.
                  Exactly what we needed to get our MVP off the ground.&quot;
                </p>
              </blockquote>
            </div>
          </div>
        </div>
        <div className="relative z-20 mt-auto">
          <p className="text-sm text-center font-medium">
            Join thousands of people traveling with Agency Forms
          </p>
        </div>
      </div>
    </div>
  );
}
