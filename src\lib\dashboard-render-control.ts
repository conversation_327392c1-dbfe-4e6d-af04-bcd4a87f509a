'use client';

/**
 * Simple helper functions for dashboard render control
 * Uses a more secure approach than sessionStorage
 */

// Use a module-level variable that resets on page load/refresh
let isDashboardRenderBlocked = false;
let blockStartTime = 0;

// Callbacks to notify components of state changes
const stateChangeCallbacks = new Set<() => void>();

export function blockDashboardRender(reason: string = 'Permission evaluation') {
  console.log(`[DashboardRenderControl] Blocking dashboard rendering: ${reason}`);
  isDashboardRenderBlocked = true;
  blockStartTime = Date.now();
  notifyStateChange();
}

export function unblockDashboardRender(reason: string = 'Evaluation complete') {
  console.log(`[DashboardRenderControl] Unblocking dashboard rendering: ${reason}`);
  isDashboardRenderBlocked = false;
  blockStartTime = 0;
  notifyStateChange();
}

export function isDashboardBlocked(): boolean {
  return isDashboardRenderBlocked;
}

export function getBlockDuration(): number {
  return blockStartTime > 0 ? Date.now() - blockStartTime : 0;
}

export function clearDashboardRenderBlock() {
  console.log('[DashboardRenderControl] Force clearing dashboard render block');
  unblockDashboardRender('Force cleared');
}

// Subscribe to state changes for reactive components
export function onRenderStateChange(callback: () => void): () => void {
  stateChangeCallbacks.add(callback);
  
  // Return unsubscribe function
  return () => {
    stateChangeCallbacks.delete(callback);
  };
}

function notifyStateChange() {
  stateChangeCallbacks.forEach(callback => {
    try {
      callback();
    } catch (error) {
      console.error('[DashboardRenderControl] Error in state change callback:', error);
    }
  });
}

// Automatic cleanup on page unload (additional safety)
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    clearDashboardRenderBlock();
  });
}
