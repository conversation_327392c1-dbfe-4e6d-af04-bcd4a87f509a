"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { useDashboard } from "./providers/dashboard-provider";
import Image from "next/image";
import type { UserAvatarMenuProps } from "@/types/components/UserAvatarMenuProps"; // Import the interface

// UserAvatarMenuProps interface removed, now imported

export function UserAvatarMenu({ user }: UserAvatarMenuProps) { // Use imported interface
  const router = useRouter();
  const { avatarUrl } = useDashboard();
  const supabase = createClient();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    // router.refresh(); // Comment out router.refresh()
    router.push('/auth/login'); // Explicitly redirect to login page
  };

  if (!user) return null;

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <button className="relative w-8 h-8 rounded-full overflow-hidden">
          {avatarUrl ? (
            <Image
              src={avatarUrl}
              alt="User avatar"
              fill
              className="object-cover"
              sizes="32px"
              onError={(e) => { (e.target as HTMLImageElement).src = '' }}
            />
          ) : (
            <span className="flex items-center justify-center w-full h-full text-xs font-bold bg-gray-200 text-gray-600">
              {user?.email?.[0]?.toUpperCase() || 'U'}
            </span>
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          <div className="flex flex-col">
            <span>{user.email}</span>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => router.push("/dashboard/profile")}>
          Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => router.push("/dashboard/settings")}>
          Settings
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut}>Sign out</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
