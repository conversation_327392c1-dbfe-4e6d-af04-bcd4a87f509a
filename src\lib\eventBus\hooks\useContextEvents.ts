'use client'

import type { OrganizationContextEvent } from '@/lib/eventTypes'
import { useBusEvent } from '@/lib/useBusEvent'

/**
 * Subscribe to organization context change events for a specific user
 */
export function useOrganizationContextEvents(
  userId: string | null,
  handler: (event: OrganizationContextEvent) => void
): void {
  useBusEvent(
    'organization:context:changed',
    (payload) => {
      if (!userId || payload.userId !== userId) return
      handler(payload)
    },
    [userId, handler]
  )
}
