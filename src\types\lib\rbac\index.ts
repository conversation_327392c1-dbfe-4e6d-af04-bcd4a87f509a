// =============================================================================
// Core RBAC Types
// =============================================================================

/**
 * User role information interface for RBAC evaluation.
 * Contains user ID, roles map, and active organization context.
 *
 * @example
 * const userRole: UserRole = {
 *   userId: 'user-123',
 *   roles: new Map([['org-456', 3]]), // Map of org IDs to role IDs
 *   orgId: 'org-456'
 * };
 */
export interface UserRole {
  userId: string;
  roles: Map<string, number>;  // Map of organization IDs to role IDs
  orgId: string;               // Current active organization ID
}

/**
 * Role key type used for string representation of roles.
 * Maps to role IDs in the database.
 *
 * @example
 * const adminRole: RoleKey = 'orgAdmin';
 */
export type RoleKey = 'superAdmin' | 'supportAdmin' | 'orgAdmin' | 'orgMember' | 'orgAccounting' | 'orgClient';

/**
 * Strict role ID type based on the actual role IDs in the database.
 * Ensures type safety when working with role IDs.
 */
export type RoleId = 1 | 2 | 3 | 4 | 5 | 6;

/**
 * Mapping from role keys to role IDs for type-safe conversions.
 */
export const ROLE_KEY_TO_ID: Record<RoleKey, RoleId> = {
  superAdmin: 1,
  supportAdmin: 2,
  orgAdmin: 3,
  orgMember: 4,
  orgAccounting: 5,
  orgClient: 6,
} as const;

/**
 * Mapping from role IDs to role keys for type-safe conversions.
 */
export const ROLE_ID_TO_KEY: Record<RoleId, RoleKey> = {
  1: 'superAdmin',
  2: 'supportAdmin',
  3: 'orgAdmin',
  4: 'orgMember',
  5: 'orgAccounting',
  6: 'orgClient',
} as const;

/**
 * Enhanced permission check result that provides context about why permission was granted or denied.
 * Useful for debugging and providing user feedback.
 */
export interface PermissionResult {
  /** Whether the permission check passed */
  allowed: boolean;
  /** The reason for the result */
  reason: string;
  /** The user's current role ID */
  userRoleId?: RoleId;
  /** The required role for the operation */
  requiredRole?: RoleKey;
  /** Additional context about the check */
  context?: {
    orgId?: string;
    operation?: string;
    resource?: string;
  };
}

/**
 * Type-safe helper functions for role operations.
 */
export interface RoleHelpers {
  /** Convert role key to role ID with type safety */
  keyToId: (key: RoleKey) => RoleId;
  /** Convert role ID to role key with type safety */
  idToKey: (id: RoleId) => RoleKey;
  /** Check if a role ID is valid */
  isValidRoleId: (id: number) => id is RoleId;
  /** Check if a role key is valid */
  isValidRoleKey: (key: string) => key is RoleKey;
  /** Get all available role keys */
  getAllRoleKeys: () => readonly RoleKey[];
  /** Get all available role IDs */
  getAllRoleIds: () => readonly RoleId[];
}



/**
 * CRUD operation combinations for permission checks.
 * Used in RBAC props to specify which operations are being authorized.
 *
 * @example
 *
 * All combinations are supported:
 * const operation: CrudKey = 'r'; // Read operations
 * const operation: CrudKey = 'cr'; // Create and read operations
 * const operation: CrudKey = 'ru'; // Read and update operations
 * const operation: CrudKey = 'rd'; // Read and delete operations
 * const operation: CrudKey = 'cru'; // Create, read, and update operations
 * const operation: CrudKey = 'crd'; // Create, read, and delete operations
 * const operation: CrudKey = 'rud'; // Read, update, and delete operations
 * const operation: CrudKey = 'crud'; // All operations (create, read, update, delete)
 */
export type CrudKey = 'r' | 'cr' | 'ru' | 'rd' | 'cru' | 'crd' | 'rud' | 'crud';

/**
 * Core RBAC Props interface used for permission checking.
 * Can specify minimum role requirements or specific roles for different CRUD operations.
 *
 * @example
 * // Require orgAdmin for read operations
 * const props: RbacConditions = { rMinRole: 'orgAdmin' };
 *
 * // Allow only superAdmin and supportAdmin for standard create and read operations
 * const props: RbacConditions = { crRoles: ['superAdmin', 'supportAdmin'] };
 *
 * // Require orgMember for both create and read in current organization
 * const props: RbacConditions = { crMinRole: 'orgMember', orgContext: 'current' };
 */
export interface RbacConditions {
  // MinRole props (hierarchy-based)
  rMinRole?: RoleKey; // Minimum role required for read operations
  crMinRole?: RoleKey; // Minimum role required for create operations
  ruMinRole?: RoleKey; // Minimum role required for update operations
  rdMinRole?: RoleKey; // Minimum role required for delete operations
  cruMinRole?: RoleKey; // Minimum role required for create, read, update operations
  crdMinRole?: RoleKey; // Minimum role required for create, read, delete operations
  rudMinRole?: RoleKey; // Minimum role required for read, update, delete operations
  crudMinRole?: RoleKey; // Minimum role required for all operations

  // Roles props (specific roles)
  rRoles?: RoleKey[]; // List of roles that can read the resource
  crRoles?: RoleKey[]; // List of roles that can create the resource
  ruRoles?: RoleKey[]; // List of roles that can update the resource
  rdRoles?: RoleKey[]; // List of roles that can delete the resource
  cruRoles?: RoleKey[]; // List of roles that can create, read, update the resource
  crdRoles?: RoleKey[]; // List of roles that can create, read, delete the resource
  rudRoles?: RoleKey[]; // List of roles that can read, update, delete the resource
  crudRoles?: RoleKey[]; // List of roles that can perform all operations on the resource

  // Organization context
  orgId?: string;                  // Specific org ID to check against
  orgContext?: 'current' | 'any';  // Default: 'current'
  resourceOrgId?: string;          // Organization ID of the resource being accessed
}

/**
 * Helper type for extracting RBAC props from component props.
 * Useful when you need to extract just the RBAC-related properties.
 *
 * @example
 * interface MyComponentProps extends RbacConditions {
 *   title: string;
 *   onClick: () => void;
 * }
 *
 * function myFunction(props: MyComponentProps) {
 *   const RbacConditions: ExtractRbacConditions<MyComponentProps> = {
 *     rMinRole: props.rMinRole,
 *     orgId: props.orgId
 *   };
 * }
 */
export type ExtractRbacConditions<T> = Pick<T, Extract<keyof T, keyof RbacConditions>>;

// =============================================================================
// Database Entity Types
// =============================================================================

/**
 * Represents a permission record in the database.
 * Used when fetching permissions from the database.
 *
 * @example
 * const permission: RolePermission = { permission_name: 'org:view' };
 */
export interface RolePermission {
  permission_name: string;
}

/**
 * Represents a role record in the database.
 * Used when fetching roles from the database.
 *
 * @example
 * const role: RoleRecord = { role_id: 3, role_name: 'orgAdmin' };
 */
export interface RoleRecord {
  role_id: number;
  role_name: string;
}

// =============================================================================
// Function Parameter Types
// =============================================================================

/**
 * Options for permission checking functions.
 * Controls behavior when permission checks fail.
 *
 * @example
 * // Redirect to login on failure
 * checkPermission(Permission.ADMIN_ACCESS, { redirectTo: '/login' });
 *
 * // Silently fail without redirecting
 * checkPermission(Permission.ADMIN_ACCESS, { silentFail: true });
 */
export interface PermissionCheckOptions {
  redirectTo?: string;  // Path to redirect to if permission check fails
  silentFail?: boolean; // If true, will not redirect or throw on failure
}

/**
 * Configuration for middleware that checks permissions.
 * Used to protect routes based on RBAC permissions.
 *
 * @example
 * const config: PermissionMiddlewareConfig = {
 *   rMinRole: 'orgAdmin',
 *   redirectTo: '/login',
 *   bypassPaths: ['/public']
 * };
 */
export interface PermissionMiddlewareConfig extends RbacConditions {
  redirectTo?: string;      // Path to redirect to if checks fail
  bypassPaths?: string[];   // Paths that bypass permission checks
}

// =============================================================================
// Server Action Configuration Types
// =============================================================================

/**
 * Configuration for server actions with RBAC.
 *
 * @example
 * const config: ActionConfig = {
 *   rMinRole: 'orgAdmin',
 *   onError: (error) => console.error(error)
 * };
 */
export interface ActionConfig extends RbacConditions {
  onError?: (error: Error) => void; // Error handler
}

/**
 * Configuration for organization-specific server actions.
 * Used in org-specific server action wrappers.
 *
 * @example
 * const config: OrgActionConfig = {
 *   rMinRole: 'orgMember',
 *   onError: (error) => console.error(error)
 * };
 */
export interface OrgActionConfig extends RbacConditions {
  onError?: (error: Error) => void;
}

/**
 * Configuration for server actions that check multiple permissions.
 * Used in server action wrappers that need to verify multiple permissions.
 *
 * @example
 * const config: MultiPermissionConfig = {
 *   crudMinRole: 'orgAdmin',
 *   onError: (error) => console.error(error)
 * };
 */
export interface MultiPermissionConfig extends RbacConditions {
  onError?: (error: Error) => void;
}