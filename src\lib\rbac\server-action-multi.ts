import { checkRbacPermission } from './permissions-server'
import { MultiPermissionConfig, ExtractRbacConditions } from '@/types/lib/rbac'

/**
 * Higher-order function to protect server actions with RBAC permissions that
 * might require multiple different types of access rights
 * @param action The server action function to protect
 * @param config RBAC configuration for authorization checks
 * @returns Protected action function
 */
export function withPermissions<TArgs extends unknown[], TReturn>(
  action: (...args: TArgs) => Promise<TReturn>,
  config: MultiPermissionConfig
) {
  return async (...args: TArgs): Promise<TReturn> => {
    try {
      // Extract RBAC conditions from config
      const rbacConditions = config as ExtractRbacConditions<MultiPermissionConfig>
      
      // Check permission using RBAC system
      const hasAccess = await checkRbacPermission(rbacConditions, { silentFail: true })

      if (!hasAccess) {
        throw new Error('Unauthorized: Insufficient permissions')
      }

      return await action(...args)
    } catch (error) {
      if (config.onError && error instanceof Error) {
        config.onError(error)
      }
      throw error
    }
  }
}

// Example usage:
// const manageUsers = withPermissions(
//   async (action: 'create' | 'update' | 'delete', userData: UserData) => {
//     // User management logic
//     return { success: true }
//   },
//   {
//     crudMinRole: "orgAdmin", // Require org admin role for all CRUD operations
//     // For more fine-grained control:
//     // crMinRole: "orgAdmin", // Create/read requires admin
//     // udMinRole: "superAdmin" // Update/delete requires superadmin
//   }
// ) 