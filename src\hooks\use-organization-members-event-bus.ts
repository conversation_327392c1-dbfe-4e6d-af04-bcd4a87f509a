'use client'

import { useState, useCallback, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase/client'; // Corrected import for createClient
// import type { RealtimeChannel } from '@supabase/supabase-js'; // Removed unused import
// import { SupabaseClient } from '@supabase/supabase-js'; 
// import { emitter } from '@/lib/eventBus'; 
import type { OrganizationMemberFull } from "@/types/organization/";
import type { AvailableRole } from '@/types/hooks/AvailableRole';
import type { Organization } from "@/types/organization";
import { MemberUpdatingState, UpdatingStates } from '@/types/hooks/MemberUpdatingState';
import { toastMessages } from '@/lib/toast-messages';
import { RoleKey } from '@/lib/rbac/rbac-utils';
import { useDashboard } from '@/components/providers/dashboard-provider';
import { useBusEvent } from '@/lib/useBusEvent';


// Removed unused AnyPostgresChangesPayload type
// type AnyPostgresChangesPayload = {
//   schema: string;
//   table: string;
//   commit_timestamp: string;
//   eventType: 'INSERT' | 'UPDATE' | 'DELETE';
//   new: Record<string, unknown>; 
//   old: Record<string, unknown>; 
//   errors: unknown[] | null; 
// };


export interface UseOrganizationMembersProps {
  organizationIdScope: 'CURRENT_CONTEXT' | 'ALL_WITH_SELECTOR' | string;
  initialRoleFilters?: RoleKey[];
  defaultPageSize?: number;
  initialSearchQuery?: string;
  initialSelectedOrgIdForScope?: string;
}

const DEFAULT_PAGE_SIZE = 10;

export function useOrganizationMembersEventBus({
  organizationIdScope,
  initialRoleFilters = [],
  defaultPageSize = DEFAULT_PAGE_SIZE,
  initialSearchQuery = "",
  initialSelectedOrgIdForScope
}: UseOrganizationMembersProps) {
  const { orgId: dashboardOrgId } = useDashboard();
  const supabase = createClient(); // supabase client instance. Now uses local lib, should not expect args.

  const [members, setMembers] = useState<OrganizationMemberFull[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [totalMembers, setTotalMembers] = useState(0);

  const [currentSearchQuery, setCurrentSearchQuery] = useState(initialSearchQuery);
  const [currentRoleFilters, setCurrentRoleFilters] = useState<RoleKey[]>(initialRoleFilters);
  
  const [effectiveOrgId, setEffectiveOrgId] = useState<string | null>(null);
  const [selectedOrgIdForSelector, setSelectedOrgIdForSelector] = useState<string>(
    initialSelectedOrgIdForScope || 'all'
  );

  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [availableRoles, setAvailableRoles] = useState<AvailableRole[]>([]);
  const [userRoles, setUserRoles] = useState<Map<string, number>>(new Map());
  const [updatingStates, setUpdatingStates] = useState<UpdatingStates>({});
  
  // const [userId, setUserId] = useState<string | null>(null); // Replaced by currentUserIdRef for checks
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(false);
  const currentUserIdRef = useRef<string | null>(null);
  
  // const lastStatusUpdateRef = useRef<{orgId: string, userId: string, timestamp: number} | null>(null); // Commented: unused
  // const lastRoleUpdateRef = useRef<{orgId: string, userId: string, timestamp: number} | null>(null); // Commented: unused

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    let newEffectiveOrgId: string | null;
    if (organizationIdScope === 'CURRENT_CONTEXT') {
      newEffectiveOrgId = dashboardOrgId;
    } else if (organizationIdScope === 'ALL_WITH_SELECTOR') {
      newEffectiveOrgId = selectedOrgIdForSelector;
    } else {
      newEffectiveOrgId = organizationIdScope;
    }
    // Only update if the value has actually changed to avoid unnecessary re-renders/effect runs
    if (newEffectiveOrgId !== effectiveOrgId) {
      setEffectiveOrgId(newEffectiveOrgId);
    }
  }, [organizationIdScope, dashboardOrgId, selectedOrgIdForSelector, effectiveOrgId]);

  const setMemberUpdatingState = useCallback((orgId: string, memberUserId: string, action: keyof MemberUpdatingState, value: boolean) => {
      const key = `${orgId}-${memberUserId}`;
      setUpdatingStates(prev => ({
          ...prev,
          [key]: { ...(prev[key] || {}), [action]: value, }
      }));
  }, []);

  const fetchSelectorOrganizations = useCallback(async () => {
    if (!isMountedRef.current) return null;
    try {
      console.log('[EventBus] Fetching organizations for selector');
      const response = await fetch("/api/organizations/authorized", { cache: 'no-store' });
      if (!response.ok) throw new Error(`Failed to fetch organizations: ${response.statusText}`);
      const data: Organization[] = await response.json();
      if (isMountedRef.current) {
        setOrganizations(data);
      }
      return data;
    } catch (err: unknown) {
      console.error("[EventBus] Error fetching organizations for selector:", err);
      if (isMountedRef.current) setError(err instanceof Error ? err : new Error("Failed to fetch organizations for selector"));
      return null;
    }
  }, []); // Removed supabase from deps, as it's stable from createClient() outside

  useEffect(() => {
    async function fetchUserContextAndStaticData() {
      if (!isMountedRef.current) return;
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser(); // Renamed to authUser
        if (!authUser) {
                currentUserIdRef.current = null;
            if (isMountedRef.current) setUserRoles(new Map());
        } else {
            currentUserIdRef.current = authUser.id;
            const { data: roleData, error: userRoleError } = await supabase
                .from("organization_members")
                .select("org_id, org_member_role")
                .eq("user_id", authUser.id)
                .eq("org_member_is_active", true);
            if (userRoleError) throw userRoleError;
            if (roleData && isMountedRef.current) {
                setUserRoles(new Map(roleData.map((role: { org_id: string; org_member_role: number }) => [role.org_id, role.org_member_role])));
            }
        }
      } catch (err: unknown) {
          if (isMountedRef.current) setError(err instanceof Error ? err : new Error("Failed to fetch user permissions"));
      }

      try {
        const { data: rolesData, error: rolesError } = await supabase.from("roles").select("role_id, role_name").order("role_id");
        if (rolesError) throw rolesError;
        if (rolesData && isMountedRef.current) setAvailableRoles(rolesData as AvailableRole[]);
      } catch (err: unknown) {
        if (isMountedRef.current) setError(err instanceof Error ? err : new Error("Failed to fetch roles"));
      }
      
      fetchSelectorOrganizations();
    }
    fetchUserContextAndStaticData();
  }, [supabase, fetchSelectorOrganizations]);


  const fetchMembers = useCallback(async (
    orgIdToFetch: string, 
    pageToFetch: number,
    sizeToFetch: number,
    searchToFetch: string,
    rolesToFetch: RoleKey[]
  ) => {
    if (!isMountedRef.current || !orgIdToFetch) return;
    
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams();
      params.append('orgId', orgIdToFetch);
      params.append('page', pageToFetch.toString());
      params.append('pageSize', sizeToFetch.toString());
      if (searchToFetch) params.append('search', searchToFetch);
      rolesToFetch.forEach(roleKey => params.append('roleKeys[]', roleKey));

      const url = `/api/organization-members?${params.toString()}`;
      const response = await fetch(url, { cache: 'no-store' });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || errorData.error || 'Failed to fetch members');
      }
      const data: { members: OrganizationMemberFull[]; totalCount: number } = await response.json();
      if (isMountedRef.current) {
      setMembers(data.members);
        setTotalMembers(data.totalCount);
      }
    } catch (err: unknown) {
      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error('Unknown error fetching members'));
      toastMessages.common.loadError('members');
      setMembers([]);
        setTotalMembers(0);
      }
    } finally {
       if (isMountedRef.current) setIsLoading(false);
    }
  }, []); // fetchMembers is stable, dependencies are passed as arguments

  useEffect(() => {
    // Skip if user has been redirected for current route
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      const redirectKey = `dashboardRedirectInProgress_${currentPath}`;
      const redirectFlag = sessionStorage.getItem(redirectKey);
      if (redirectFlag) {
        console.log('[EventBus] Fetch effect skipped - redirect in progress for route:', currentPath);
        return;
      }
    }

    if (effectiveOrgId) {
      console.log('[EventBus] Fetch effect triggered', { effectiveOrgId, currentPage, pageSize, currentSearchQuery, currentRoleFilters });
      if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = setTimeout(() => {
        fetchMembers(effectiveOrgId, currentPage, pageSize, currentSearchQuery, currentRoleFilters);
      }, 300);
      return () => { if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current); };
    } else {
      if (organizationIdScope !== 'CURRENT_CONTEXT') {
         setIsLoading(false);
        setMembers([]);
         setTotalMembers(0);
      }
    }
  }, [effectiveOrgId, currentPage, pageSize, currentSearchQuery, currentRoleFilters, fetchMembers, organizationIdScope]);

  const handleSetCurrentPage = useCallback((page: number) => setCurrentPage(page), []);
  const handleSetPageSize = useCallback((size: number) => { setPageSize(size); setCurrentPage(1); }, []);
  const handleSetSearchQuery = useCallback((query: string) => { setCurrentSearchQuery(query); setCurrentPage(1);}, []);
  const handleSetRoleFilters = useCallback((filters: RoleKey[]) => { setCurrentRoleFilters(filters); setCurrentPage(1); }, []);
  const handleSetSelectedOrgIdForSelector = useCallback((orgId: string) => {
    if (organizationIdScope === 'ALL_WITH_SELECTOR') setSelectedOrgIdForSelector(orgId); setCurrentPage(1);
  }, [organizationIdScope]);
  
  const refreshMembers = useCallback(() => {
    if (effectiveOrgId) {
      fetchMembers(effectiveOrgId, currentPage, pageSize, currentSearchQuery, currentRoleFilters);
    }
  }, [effectiveOrgId, currentPage, pageSize, currentSearchQuery, currentRoleFilters, fetchMembers]);

  const updateMemberRole = useCallback(async (orgId: string, targetUserId: string, newRoleIdStr: string) => {
    const originalMember = members.find(m => m.org_id === orgId && m.user_id === targetUserId);
    const originalRole = originalMember?.org_member_role;
    const originalRoleName = originalMember?.role_name;
    const newRoleId = parseInt(newRoleIdStr, 10); 
    const newRole = availableRoles.find(r => r.role_id === newRoleId);
    if (!newRole) { toastMessages.common.unexpectedError("Invalid role selected."); return; }
    setMemberUpdatingState(orgId, targetUserId, 'role', true);
    if (originalMember) setMembers(prev => prev.map(m => (m.org_id === orgId && m.user_id === targetUserId) ? { ...m, org_member_role: newRole.role_id, role_name: newRole.role_name, updated_at: new Date().toISOString() } : m));
    try {
      const response = await fetch('/api/organization-members/update-role', { method: 'PATCH', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ orgId, userId: targetUserId, newRoleId: newRole.role_id }), });
      if (!response.ok) { const d = await response.json().catch(() => ({})); throw new Error(d.message || d.error || 'Failed to update role.'); }
      toastMessages.user.roleUpdate(originalMember?.user_full_name || 'The member', newRole.role_name, originalMember?.organization.org_name || 'the organization');
      refreshMembers();
    } catch (err: unknown) {
      toastMessages.user.updateError(err instanceof Error ? err.message : 'Failed to update role.');
      if (originalMember && originalRole !== undefined && originalRoleName !== undefined) setMembers(prev => prev.map(m => (m.org_id === orgId && m.user_id === targetUserId) ? { ...m, org_member_role: originalRole, role_name: originalRoleName, updated_at: originalMember.updated_at || new Date().toISOString() } : m));
    } finally { setMemberUpdatingState(orgId, targetUserId, 'role', false); }
  }, [members, availableRoles, setMemberUpdatingState, refreshMembers]);

  const updateMemberStatus = useCallback(async (orgId: string, targetUserId: string, newStatus: boolean) => {
    const originalMember = members.find(m => m.org_id === orgId && m.user_id === targetUserId);
    const originalStatus = originalMember?.org_member_is_active;
    setMemberUpdatingState(orgId, targetUserId, 'status', true);
    if (originalMember) setMembers(prev => prev.map(m => (m.org_id === orgId && m.user_id === targetUserId) ? { ...m, org_member_is_active: newStatus, updated_at: new Date().toISOString() } : m));
    try {
      const response = await fetch('/api/organization-members/update-status', { method: 'PATCH', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ orgId, userId: targetUserId, newStatus }), });
      if (!response.ok) { const d = await response.json().catch(() => ({})); throw new Error(d.message || d.error || 'Failed to update status.'); }
      toastMessages.user.statusUpdate(originalMember?.user_full_name || 'The member', newStatus ? 'Active' : 'Inactive');
      refreshMembers();
    } catch (err: unknown) {
      toastMessages.user.updateError(err instanceof Error ? err.message : 'Failed to update status.');
      if (originalMember && originalStatus !== undefined) setMembers(prev => prev.map(m => (m.org_id === orgId && m.user_id === targetUserId) ? { ...m, org_member_is_active: originalStatus, updated_at: originalMember.updated_at || new Date().toISOString() } : m));
    } finally { setMemberUpdatingState(orgId, targetUserId, 'status', false); }
  }, [members, setMemberUpdatingState, refreshMembers]);

  const removeMember = useCallback(async (orgId: string, targetUserId: string) => {
    const memberToRemove = members.find(m => m.org_id === orgId && m.user_id === targetUserId);
    if (memberToRemove?.org_member_is_active) { toastMessages.common.unexpectedError("Cannot remove an active member. Please deactivate first."); return; }
    setMemberUpdatingState(orgId, targetUserId, 'remove', true);
    const membersBeforeRemoval = [...members];
    if (memberToRemove) setMembers(prev => prev.filter(m => !(m.org_id === orgId && m.user_id === targetUserId)));
    try {
      const response = await fetch('/api/organization-members/remove', { method: 'DELETE', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ orgId, userId: targetUserId }), });
      if (!response.ok) { const d = await response.json().catch(() => ({})); throw new Error(d.message || d.error || 'Failed to remove member.');}
      toastMessages.user.removed(memberToRemove?.user_full_name || 'The member', memberToRemove?.organization.org_name || 'the organization');
      refreshMembers();
    } catch (err: unknown) {
      toastMessages.user.removeError(err instanceof Error ? err.message : 'Failed to remove member.');
      if (memberToRemove) setMembers(membersBeforeRemoval);
    } finally { setMemberUpdatingState(orgId, targetUserId, 'remove', false); }
  }, [members, setMemberUpdatingState, refreshMembers]);

  // Listen for mitt events and refresh members on relevant changes
  useBusEvent('db:organization_members:inserted', (event) => {
    if (!effectiveOrgId) return
    if (event.orgId === effectiveOrgId || effectiveOrgId === 'all') refreshMembers()
  })
  useBusEvent('db:organization_members:updated', (event) => {
    if (!effectiveOrgId) return
    if (event.orgId === effectiveOrgId || effectiveOrgId === 'all') refreshMembers()
  })
  useBusEvent('db:organization_members:deleted', (event) => {
    if (!effectiveOrgId) return
    if (event.orgId === effectiveOrgId || effectiveOrgId === 'all') refreshMembers()
  })

  return {
    members, isLoading, error, organizations, availableRoles, userRoles, updatingStates,
    currentPage, pageSize, totalMembers, setCurrentPage: handleSetCurrentPage, setPageSize: handleSetPageSize,
    currentSearchQuery, currentRoleFilters, setSearchQuery: handleSetSearchQuery, setRoleFilters: handleSetRoleFilters,
    setSelectedOrgIdForSelector: handleSetSelectedOrgIdForSelector, effectiveSelectedOrgId: effectiveOrgId,
    updateMemberRole, updateMemberStatus, removeMember, refreshMembers, setMemberUpdatingState,
    organizationIdScopeUsed: organizationIdScope 
  };
}
