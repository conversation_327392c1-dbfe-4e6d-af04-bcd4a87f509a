import type { UserPersonalInfo } from '@/types/user';

export interface OrganizationMemberResponse {
  org_id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  org_member_role: number;
  org_member_is_active: boolean;
  is_default_org: boolean;
  org_member_updated_by: string | null;
  profiles: {
    full_name: string;
    avatar_url: string | null;
    user_personal_information: UserPersonalInfo | null;
  };
  organizations: {
    org_name: string;
    org_icon: string | null;
    is_active: boolean;
  };
  roles: {
    role_name: string;
  };
  updatedByProfile: {
    full_name: string | null;
  } | null;
} 