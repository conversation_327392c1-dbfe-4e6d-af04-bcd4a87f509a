'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useBusEvent } from '@/lib/useBusEvent'
import { AUTH_CONTEXT_CHANGED } from '@/lib/eventBus/constants'
import type { AuthEvent } from '@/lib/eventTypes'

// Simple toast-like notification function
function showNotification(message: string): void {
  console.log('Notification:', message)
  // In a real implementation, this would show a toast or notification
}

/**
 * A component for testing the event system
 * This should only be rendered for developers and for testing purposes
 */
export function TestEventSystem({ userId }: { userId: string }) {
  const [events, setEvents] = useState<Array<{ type: string, timestamp: number, details: string }>>([])
  const [lastEventTimestamp, setLastEventTimestamp] = useState<number | null>(null)

  // Handler for unified auth context events
  const handleAuthContextChange = useCallback((event: AuthEvent) => {
    // Only show events for the current user
    if (event.userId !== userId) return;

    setEvents(prev => [
      {
        type: 'Auth Context Change',
        timestamp: event.timestamp || Date.now(),
        details: `${event.reason} (User: ${event.userId}, Org: ${event.orgId || 'N/A'})`
      },
      ...prev.slice(0, 9)
    ])
    setLastEventTimestamp(Date.now())
  }, [userId])

  // Subscribe to unified auth context events
  useBusEvent(AUTH_CONTEXT_CHANGED, handleAuthContextChange, [handleAuthContextChange])

  // Test functions for administrators to trigger events
  const triggerContextChange = async () => {
    try {
      const { data, error } = await createClient()
        .from('organizations')
        .select('id')
        .limit(1)
      if (error) throw error
      if (data && data.length > 0) {
        const orgId = data[0].id
        // Call the server action to switch organization
        const result = await import('@/app/actions/switch-organization').then(mod => 
          mod.switchOrganization(orgId)
        )
        if (!result || result.success === false) {
          throw new Error(result?.error || "Failed to switch organization")
        }
        showNotification(`Switched to organization ${orgId}`)
      }
    } catch (error) {
      console.error("Error triggering context change:", error)
      showNotification("Failed to trigger context change")
    }
  }

  // Emit a dummy event to test the event system
  const triggerTestDbChange = async () => {
    try {
      // Example: Trigger a change by updating a test user's status
      // Replace 'YOUR_TEST_USER_ID' with an actual test user ID in your DB
      // Replace 'YOUR_TEST_ORG_ID' with an actual test organization ID
      // Ensure you have RLS policies that allow this update for your test user/role
      const testUserId = userId; // Use the current user for simplicity if permitted
      // You might need a specific test org or find the current user's org
      const { data: orgData, error: orgError } = await createClient()
        .from('organization_members')
        .select('org_id')
        .eq('user_id', testUserId)
        .limit(1);
      
      if (orgError) throw orgError;
      if (!orgData || orgData.length === 0) {
        throw new Error('Could not find an organization for the current user.');
      }
      const testOrgId = orgData[0].org_id;

      // Toggle the user's status in this organization
      const { data: currentMemberData, error: currentMemberError } = await createClient()
        .from('organization_members')
        .select('org_member_is_active')
        .eq('user_id', testUserId)
        .eq('org_id', testOrgId)
        .single();

      if (currentMemberError) throw currentMemberError;

      const newStatus = !currentMemberData.org_member_is_active;

      const { error: updateError } = await createClient()
        .from('organization_members')
        .update({ org_member_is_active: newStatus })
        .eq('user_id', testUserId)
        .eq('org_id', testOrgId);

      if (updateError) throw updateError;
      
      showNotification(`Triggered DB change: User status toggled in org ${testOrgId}. Check logs for events.`);
    } catch (error) {
      console.error("Error triggering test DB change:", error);
      showNotification("Failed to trigger test DB change");
    }
  };

  // Event system active if any event received in last 60 seconds
  const isEventSystemActive = lastEventTimestamp && (Date.now() - lastEventTimestamp < 60000)

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Event System Test</CardTitle>
        <CardDescription>Monitor realtime events in the system</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          {/* Event System Status */}
          <div className="border rounded-md p-4 bg-slate-50">
            <h3 className="text-sm font-medium mb-2">Event System Status</h3>
            <div className="flex items-center justify-between">
              <span className="text-sm">Event System:</span>
              <span className={`text-sm font-medium ${isEventSystemActive ? 'text-green-600' : 'text-red-600'}`}>
                {isEventSystemActive ? 'Active' : 'No recent events'}
              </span>
            </div>
            <div className="flex gap-2 mt-2">
              <Button onClick={triggerTestDbChange} variant="outline">
                Trigger Test DB Change
              </Button>
              <Button onClick={triggerContextChange}>
                Trigger Org Switch (Context Change)
              </Button>
            </div>
          </div>
          {/* Events Log */}
          <div className="border rounded-md p-4">
            <h3 className="text-sm font-medium mb-2">Recent Events</h3>
            {events.length === 0 ? (
              <p className="text-sm text-muted-foreground">No events received yet</p>
            ) : (
              <div className="space-y-2">
                {events.map((event, i) => (
                  <div key={i} className="text-sm border-b pb-2">
                    <div className="font-medium">{event.type}</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </div>
                    <div className="text-xs">{event.details}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 