## Test Case 1

**Test Configuration:**
- **Initial Role:** superadmin  
- **Initial Current Org Status:** active  
- **Initial Current User Status:** active  
- **Transition Axis:** Organization  
- **Change:** org status: active → inactive  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Add   
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** No  
- [x] Pass [ ] Fail **Redirect?:** No  
- **Comments:** Superadmin retains access to disabled organization if user account is active  

---

## Test Case 2

**Test Configuration:**
- **Initial Role:** Any  
- **Initial Current Org Status:** active  
- **Initial Current User Status:** inactive  
- **Transition Axis:** Organization  
- **Change:** org status: active → inactive  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Add  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Yes  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** No  
- [x] Pass [ ] Fail **Redirect?:** No  
- **Comments:** User account already inactive and user already on account disabled page  

---

## Test Case 3

**Test Configuration:**
- **Initial Role:** Any  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** inactive  
- **Transition Axis:** Organization  
- **Change:** org status: inactive → active  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Remove  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Yes  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** No  
- [x] Pass [ ] Fail **Redirect?:** No  
- **Comments:** User account already inactive and user already on account disabled page  

---

## Test Case 4

**Test Configuration:**
- **Initial Role:** superadmin  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** active  
- **Transition Axis:** Organization  
- **Change:** org status: inactive → active  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Remove 
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** No  
- **Comments:** Superadmin wasn't on disabled page so can remain  

---

## Test Case 5

**Test Configuration:**
- **Initial Role:** non-superadmin  
- **Initial Current Org Status:** active  
- **Initial Current User Status:** active  
- **Transition Axis:** Organization  
- **Change:** org status: active → inactive  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Add   
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Organization-disabled  
- **Comments:**  

---

## Test Case 6

**Test Configuration:**
- **Initial Role:** non-superadmin  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** active  
- **Transition Axis:** Organization  
- **Change:** org status: inactive → active  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Remove 
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Dashboard  
- **Comments:**  

---

## Test Case 7

**Test Configuration:**
- **Initial Role:** any  
- **Initial Current Org Status:** active  
- **Initial Current User Status:** active  
- **Transition Axis:** Role  
- **Change:** role: any → any  

**Expected Results:**
- [x] Pass [ ] Fail **Top bar notification organization inactive:** No  
- [x] Pass [ ] Fail **Sidebar notification organization disabled:** No  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [ ] Pass [x] Fail **Redirect?:** Dashboard  
- **Comments:**  

---

## Test Case 8

**Test Configuration:**
- **Initial Role:** any  
- **Initial Current Org Status:** any  
- **Initial Current User Status:** inactive  
- **Transition Axis:** Role  
- **Change:** role: any → any  

**Expected Results:**
- [x] Pass [ ] Fail **Top bar notification organization inactive:** No 
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Yes  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft refresh  
- [x] Pass [ ] Fail **Update navigation:** No  
- [x] Pass [ ] Fail **Redirect?:** No  
- **Comments:** User account already inactive and user already on account disabled page  

---

## Test Case 9

**Test Configuration:**
- **Initial Role:** superadmin  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** active  
- **Transition Axis:** Role  
- **Change:** role: superadmin → non-superadmin  

**Expected Results:**
- [ ] Pass [X] Fail **Top bar notification organization inactive:** Yes 
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Organization-disabled  
- **Comments:** Only superadmin has access to inactive organizations  


---

## Test Case 10

**Test Configuration:**
- **Initial Role:** non-superadmin  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** active  
- **Transition Axis:** Role  
- **Change:** role: non-superadmin → superadmin  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Yes  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** No  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Dashboard  
- **Comments:** Promoted to superadmin, so gets access to disabled organization  

---

## Test Case 11

**Test Configuration:**
- **Initial Role:** Any  
- **Initial Current Org Status:** active  
- **Initial Current User Status:** active  
- **Transition Axis:** User  
- **Change:** user status: active → inactive  

**Expected Results:**
- [x] Pass [ ] Fail **Top bar notification organization inactive:** No  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Add  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Account-disabled  
- **Comments:**  

---

## Test Case 12

**Test Configuration:**
- **Initial Role:** Any  
- **Initial Current Org Status:** active  
- **Initial Current User Status:** inactive  
- **Transition Axis:** User  
- **Change:** user status: inactive → active  

**Expected Results:**
- [x] Pass [ ] Fail **Top bar notification organization inactive:** No  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Remove  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Dashboard  
- **Comments:**  

---

## Test Case 13

**Test Configuration:**
- **Initial Role:** Any  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** active  
- **Transition Axis:** User  
- **Change:** user status: active → inactive  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Yes   
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Add  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Account-disabled  
- **Comments:**  

---

## Test Case 14

**Test Configuration:**
- **Initial Role:** superadmin  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** inactive  
- **Transition Axis:** User  
- **Change:** user status: inactive → active  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Yes  
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Remove  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** Yes - soft-refresh rbacEvaluate  
- [x] Pass [ ] Fail **Redirect?:** Dashboard  
- **Comments:** Superadmin account changed to active, so gains access to disabled org  

---

## Test Case 15

**Test Configuration:**
- **Initial Role:** non-superadmin  
- **Initial Current Org Status:** inactive  
- **Initial Current User Status:** inactive  
- **Transition Axis:** User  
- **Change:** user status: inactive → active  

**Expected Results:**
- [ ] Pass [x] Fail **Top bar notification organization inactive:** Yes   
- [x] Pass [ ] Fail **Sidebar notification account disabled:** Remove  
- [x] Pass [ ] Fail **Update organization-switcher:** Yes - soft-refresh  
- [x] Pass [ ] Fail **Update navigation:** No  
- [x] Pass [ ] Fail **Redirect?:** Organization-disabled  
- **Comments:**  


Additional notes:
- We need to add a catch/retry mechanism for any cases where the authentication cookie may have expired, such as when this occurs:
D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\organization\organization-switcher.tsx:294 
            
GET http://localhost:3000/api/organizations/authorized 401 (Unauthorized)

and:

D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:30 
            
            
POST http://localhost:3000/api/auth/refresh-context 401 (Unauthorized)
fetcher @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:30
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\swr@2.3.3_react@19.0.0\node_modules\swr\dist\_internal\index.mjs:56
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\swr@2.3.3_react@19.0.0\node_modules\swr\dist\index\index.mjs:342
onRevalidate @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\swr@2.3.3_react@19.0.0\node_modules\swr\dist\index\index.mjs:500
startRevalidate @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\swr@2.3.3_react@19.0.0\node_modules\swr\dist\_internal\config-context-client-v7VOFo66.mjs:269
mutateByKey @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\swr@2.3.3_react@19.0.0\node_modules\swr\dist\_internal\config-context-client-v7VOFo66.mjs:277
internalMutate @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\swr@2.3.3_react@19.0.0\node_modules\swr\dist\_internal\config-context-client-v7VOFo66.mjs:253
useServerContextRefresher.useCallback[forceRefreshContext] @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:180
await in useServerContextRefresher.useCallback[forceRefreshContext]
useServerContextRefresher.useCallback[debouncedForceRefreshContext] @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:199
setTimeout
useServerContextRefresher.useCallback[debouncedForceRefreshContext] @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:198
useServerContextRefresher.useCallback[debouncedForceRefreshContext] @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:197
handleEventAndRedirectIfNeeded @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\dashboard-scenario-manager.tsx:64
DashboardScenarioManager.useEffect.onMemberStatusChanged @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\dashboard-scenario-manager.tsx:183
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\mitt@3.0.1\node_modules\mitt\dist\mitt.mjs:1
emit @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\mitt@3.0.1\node_modules\mitt\dist\mitt.mjs:1
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\lib\eventBus\channels\status.ts:68
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\mitt@3.0.1\node_modules\mitt\dist\mitt.mjs:1
emit @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\mitt@3.0.1\node_modules\mitt\dist\mitt.mjs:1
DashboardEventManagerCore.useEffect.channel @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\components\dashboard\dashboard-event-manager-core.tsx:97
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\RealtimeChannel.js:381
_trigger @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\RealtimeChannel.js:366
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\RealtimeClient.js:394
eval @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\RealtimeClient.js:394
decode @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\lib\serializer.js:12
_onConnMessage @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\RealtimeClient.js:386
conn.onmessage @ D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\node_modules\.pnpm\@supabase+realtime-js@2.11.2\node_modules\@supabase\realtime-js\dist\module\RealtimeClient.js:380
D:\My Data\Projects\Private projects\Visa Application Automation\nextjs_supabaseSSR\src\hooks\use-server-context-refresher.ts:38 [useServerContextRefresher] Response status: 401