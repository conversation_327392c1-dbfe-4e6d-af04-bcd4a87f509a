import { MemberTable, MemberTableBusinessRules } from "@/components/shared/member-table";
import { withRbacPermission } from "@/lib/rbac/permissions-server";
import type { RoleKey } from "@/types/lib/rbac";
import { PageProps } from "@/types/app/PageProps";

// This page is for organization members to view clients in their current organization.
const ClientsPage = async (_props: PageProps) => {
  // Define business rules for client management:
  // 1. rMinRole orgMember can read clients (page access already handled by withRbacPermission)
  // 2. ruMinRole orgMember can read and update client profiles
  // 3. Client roles cannot be changed on this page by anyone
  // 4. ruMinRole orgMember can read/update client status
  // 5. rudMinRole orgAdmin can read/update/delete clients

  const clientBusinessRules: MemberTableBusinessRules = {
    profileEditMinRole: "orgMember", // Rule 2: orgMember+ can view/edit profiles
    canEditRole: false, // Rule 3: Client roles cannot be changed on this page by anyone
    statusChangeMinRole: "orgMember", // Rule 4: orgMember+ can update status
    deleteMinRole: "orgAdmin", // Rule 5: orgAdmin+ can delete clients
    inviteMinRole: null, // Will be added later as mentioned
  };

  // Define role filters for this page - only 'orgClient'
  const clientRoleFilters: RoleKey[] = ['orgClient'];

  return (
    <div className="p-6">
      {/* The MemberTable component will render its own breadcrumbs and title based on props */}
      <MemberTable
        organizationIdScope="CURRENT_CONTEXT"
        roleFilters={clientRoleFilters}
        permissionModel="business-rules"
        businessRules={clientBusinessRules}
        showOrganizationColumn={false} // Not needed when scope is current context
        tableTitle="Our Clients"
        // onInviteUser prop not provided as inviteMinRole is null
      />
    </div>
  );
};

export default withRbacPermission(
  { rMinRole: "orgMember" }, // Accessible by OrgMember and above
  { redirectTo: "/dashboard" }
)(ClientsPage);