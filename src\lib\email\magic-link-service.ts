import { render } from '@react-email/render';
import { resend, EMAIL_SENDERS, EMAIL_CONFIG } from './resend-client';
import { MagicLinkEmail } from './templates/magic-link-email';

interface SendMagicLinkEmailParams {
  email: string;
  magicLink: string;
  expiresAt?: Date;
}

interface MagicLinkEmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export async function sendMagicLinkEmail({
  email,
  magicLink,
  expiresAt = new Date(Date.now() + 60 * 60 * 1000), // 1 hour default
}: SendMagicLinkEmailParams): Promise<MagicLinkEmailResult> {
  try {
    // Format expiry time for display
    const expiresAtFormatted = expiresAt.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });

    // Render the email template
    const emailHtml = await render(
      MagicLinkEmail({
        magicLink,
        userEmail: email,
        expiresAt: expiresAtFormatted,
      })
    );

    // Send the email via Resend
    const { data, error } = await resend.emails.send({
      from: EMAIL_SENDERS.LOGIN,
      to: [email],
      subject: 'Sign in to AgencyForms',
      html: emailHtml,
      // Add text fallback
      text: `
Sign in to AgencyForms

Hello! Click the link below to sign in to your AgencyForms account:

${magicLink}

This link will expire at ${expiresAtFormatted}.

If you didn't request this email, you can safely ignore it.

Email sent to: ${email}
      `.trim(),
    });

    if (error) {
      console.error('Failed to send magic link email:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email',
      };
    }

    return {
      success: true,
      ...(data?.id && { messageId: data.id }),
    };
  } catch (error) {
    console.error('Error sending magic link email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

// Helper function to validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to check if magic link is expired
export function isMagicLinkExpired(expiresAt: Date): boolean {
  return new Date() > expiresAt;
}

// Helper function to generate magic link URL
export function generateMagicLinkUrl(token: string, redirectTo: string = '/dashboard'): string {
  const baseUrl = EMAIL_CONFIG.BASE_URL;
  const params = new URLSearchParams({
    token,
    type: 'magiclink',
    redirectTo,
  });
  
  return `${baseUrl}/auth/callback?${params.toString()}`;
}
