/**
 * mitt-debugger.ts
 * ------------------------------------------------------------------
 * Utility helpers to introspect mitt listeners at runtime.
 * Exposes a global  __mittDebug([...eventName])
 * ------------------------------------------------------------------
 */

import type { Emitter } from 'mitt';
import type { Events } from '@/lib/eventBus';

/** Internal helper: a mitt instance plus its `all` map. */
type MittInstance = Emitter<Events> & {
  all: Map<string | symbol, Array<(evt: unknown) => void>>;
};

let mittDebuggerAttached = false;

/**
 * Print listener information for one or every event.
 */
export function debugEmitterListeners(
  emitter: Emitter<Events>,
  eventName?: keyof Events | string
): void {
  const mittInstance = emitter as MittInstance;

  if (!mittInstance.all) {
    console.warn(
      '[mitt-debugger] This object does not expose the ".all" map - are you sure it is a mitt emitter?'
    );
    return;
  }

  if (!eventName) {
    console.group('[mitt-debugger] All event listeners');
    if (mittInstance.all.size === 0) {
      console.log('No event listeners registered.');
    } else {
      mittInstance.all.forEach((listeners, evt) => {
        console.log(`${String(evt)} : ${listeners.length} listener(s)`);
      });
    }
    console.groupEnd();
    return;
  }

  const listeners = mittInstance.all.get(eventName as string) ?? [];

  console.group(`[mitt-debugger] Listeners for "${String(eventName)}"`);
  if (listeners.length === 0) {
    console.log(`No listeners for "${String(eventName)}"`);
  } else {
    console.log(`${listeners.length} listener(s) found:`);
    listeners.forEach((fn, i) =>
      console.log(`[${i}] ${fn.toString().slice(0, 100)}…`)
    );
  }
  console.groupEnd();
}

/**
 * Attach `__mittDebug` to globalThis exactly once.
 */
export function attachMittDebugger(emitter: Emitter<Events>): void {
  if (mittDebuggerAttached) return;
  mittDebuggerAttached = true;

  const debugFn = (evtName?: string) =>
    debugEmitterListeners(emitter, evtName);

  // Works in browsers, Node, and web workers
  (globalThis as any).__mittDebug = debugFn;

  console.log(
    '[mitt-debugger] Ready!  Call:\n' +
      '  __mittDebug()          // show all events\n' +
      '  __mittDebug("myEvent") // show listeners for "myEvent"\n'
  );
}

/* ------------------------------------------------------------------ */
/*                           Global typings                           */
/* ------------------------------------------------------------------ */

declare global {
  // NB: `var` to match the runtime property we create on globalThis.
  //     Optional so projects can omit attaching it.
  // eslint-disable-next-line no-var
  var __mittDebug: ((eventName?: string) => void) | undefined;

  interface Window {
    /** Present only in browser environments after `attachMittDebugger` is called. */
    __mittDebug?: (eventName?: string) => void;
  }
}

export {}; // <-- ensures this file is treated as an ES module
