

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "drizzle";


ALTER SCHEMA "drizzle" OWNER TO "postgres";


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "citext" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."invitation_status" AS ENUM (
    'created',
    'sent',
    'delivered',
    'failed',
    'accepted',
    'declined',
    'expired',
    'added'
);


ALTER TYPE "public"."invitation_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_invitation"("p_token" "text", "p_user_id" "uuid", "p_full_name" "text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$DECLARE
  v_inv         email_invitations%ROWTYPE;
  v_has_default boolean;
  v_hash        text := encode(digest(p_token::bytea, 'sha256'), 'hex');
BEGIN
  /* 1. Lock + validate invitation via hash */
  SELECT *
  INTO   v_inv
  FROM   email_invitations
  WHERE  invitation_hash = v_hash          -- ← hash column
    AND  status          = 'delivered'
    AND  expires_at      >= now()
  FOR UPDATE;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invitation not found/expired';
  END IF;

  /* 2. Mark accepted */
  UPDATE email_invitations
     SET status      = 'accepted',
         accepted_by = p_user_id,
         accepted_at = now()
   WHERE id = v_inv.id;

  /* 3. Update profile */
  UPDATE profiles
     SET full_name = p_full_name
   WHERE id = p_user_id;

  /* 4. Upsert membership */
  INSERT INTO organization_members(
      org_id, user_id, org_member_role,
      org_member_is_active, is_default_org, is_current_context,
      org_member_updated_by
  )
  VALUES (
      v_inv.org_id, p_user_id, v_inv.role_id,
      true, false, false,
      v_inv.invited_by
  )
  ON CONFLICT (org_id, user_id) DO UPDATE
        SET org_member_is_active = true,
            org_member_role      = EXCLUDED.org_member_role,
            org_member_updated_by= EXCLUDED.org_member_updated_by;

  /* 5. Ensure default/current flags */
  SELECT EXISTS (
    SELECT 1
    FROM   organization_members
    WHERE  user_id = p_user_id
      AND  is_default_org
  ) INTO v_has_default;

  IF NOT v_has_default THEN
    UPDATE organization_members
       SET is_default_org    = true,
           is_current_context= true
     WHERE org_id = v_inv.org_id
       AND user_id = p_user_id;
  END IF;

  RETURN true;
END;$$;


ALTER FUNCTION "public"."accept_invitation"("p_token" "text", "p_user_id" "uuid", "p_full_name" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."accept_invitation"("p_token" "text", "p_user_id" "uuid", "p_full_name" "text") IS 'Atomically processes invitation acceptance with proper locking';



CREATE OR REPLACE FUNCTION "public"."accept_invitation_anonymous_complete"("p_hash" "text", "p_user_id" "uuid", "p_email" "text", "p_full_name" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public', 'pg_catalog'
    AS $$
DECLARE
  v_inv email_invitations%rowtype;
  v_has_default boolean;
BEGIN
  -- 1. Lock and validate invitation using hash directly
  SELECT * INTO v_inv
  FROM email_invitations
  WHERE invitation_hash = p_hash
    AND email = p_email
    AND status = 'delivered'
    AND expires_at >= now()
  FOR UPDATE;

  IF NOT FOUND THEN
      RAISE EXCEPTION 'Invitation not found/expired';
  END IF;

  -- 2. Ensure profile exists (upsert to handle trigger failures)
  INSERT INTO profiles (id, full_name, email)
  VALUES (p_user_id, p_full_name, p_email)
  ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    email = EXCLUDED.email;

  -- 3. Mark invitation accepted (FINAL STATUS - no 'added' needed)
  UPDATE email_invitations
    SET status = 'accepted',
        accepted_by = p_user_id,
        accepted_at = now()
  WHERE id = v_inv.id;

  -- 4. Insert / activate membership
  INSERT INTO organization_members(
      org_id, user_id, org_member_role,
      org_member_is_active, is_default_org, is_current_context,
      org_member_updated_by
  ) VALUES (
      v_inv.org_id, p_user_id, v_inv.role_id,
      true, false, false,
      v_inv.invited_by
  )
  ON CONFLICT (org_id, user_id) DO UPDATE
      SET org_member_is_active = true,
          org_member_role = EXCLUDED.org_member_role,
          org_member_updated_by = EXCLUDED.org_member_updated_by;

  -- 5. Set default org if needed
  SELECT EXISTS(
    SELECT 1
    FROM organization_members
    WHERE user_id = p_user_id
      AND is_default_org
  ) INTO v_has_default;

  IF NOT v_has_default THEN
     UPDATE organization_members
       SET is_default_org = true,
           is_current_context = true
     WHERE org_id = v_inv.org_id
       AND user_id = p_user_id;
  END IF;

END;
$$;


ALTER FUNCTION "public"."accept_invitation_anonymous_complete"("p_hash" "text", "p_user_id" "uuid", "p_email" "text", "p_full_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_superusers_to_org"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    superuser RECORD;
BEGIN
    FOR superuser IN SELECT id FROM public.superusers LOOP
        INSERT INTO public.organization_members (org_id, user_id, org_member_role, org_member_is_active, created_at, updated_at)
        VALUES (NEW.id, superuser.id, 1, true, NOW(), NOW()); -- Assuming role '1' for superusers
    END LOOP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."add_superusers_to_org"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_org_admin_access"("check_org_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  select exists (
    select 1
    from organization_members
    where user_id = auth.uid()
    and org_id = check_org_id
    and org_member_role <= 3
    and org_member_is_active = true
  );
$$;


ALTER FUNCTION "public"."check_org_admin_access"("check_org_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."current_user_has_organizations"() RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
  v_user_id UUID;
  v_has_orgs BOOLEAN;
  v_org_id UUID;
BEGIN
  -- Get current user
  v_user_id := auth.uid();
  
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;
  
  -- Check if user has any organizations
  SELECT EXISTS (
    SELECT 1
    FROM public.organization_members
    WHERE user_id = v_user_id
    AND org_member_is_active = true
  ) INTO v_has_orgs;
  
  -- If user has organizations, ensure one is set as current context
  IF v_has_orgs THEN
    v_org_id := public.initialize_user_organization_context(v_user_id);
  END IF;
  
  RETURN v_has_orgs;
END;
$$;


ALTER FUNCTION "public"."current_user_has_organizations"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."current_user_has_organizations"() IS 'Checks if the current user belongs to any active organization, for use in RLS policies';



CREATE OR REPLACE FUNCTION "public"."get_role_name"("role_id" integer) RETURNS "text"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $_$
    SELECT role_name
    FROM public.roles
    WHERE role_id = $1;
$_$;


ALTER FUNCTION "public"."get_role_name"("role_id" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_active_organization"("p_user_id" "uuid") RETURNS "uuid"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
    SELECT org_id
    FROM public.organization_members
    WHERE user_id = p_user_id
    AND org_member_is_active = true
    AND is_default_org = true
    LIMIT 1;
$$;


ALTER FUNCTION "public"."get_user_active_organization"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_org_role"("p_org_id" "uuid") RETURNS integer
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
  SELECT org_member_role
  FROM public.organization_members
  WHERE user_id = auth.uid()
  AND org_id = p_org_id
  LIMIT 1;
$$;


ALTER FUNCTION "public"."get_user_org_role"("p_org_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_organization_ids"("check_user_id" "uuid") RETURNS SETOF "uuid"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  select org_id
  from public.organization_members
  where user_id = check_user_id
  and org_member_is_active = true;
$$;


ALTER FUNCTION "public"."get_user_organization_ids"("check_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, avatar_url, email)
  VALUES (
    NEW.id, 
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'avatar_url',
    COALESCE(
      NEW.raw_user_meta_data->>'email',
      NEW.email  -- Fallback to the actual email field
    )
  );
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
begin
  new.updated_at = now();
  return new;
end;
$$;


ALTER FUNCTION "public"."handle_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_any_organizations"() RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  select exists (select 1 from organizations);
$$;


ALTER FUNCTION "public"."has_any_organizations"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_organization_role"("p_user_id" "uuid", "p_org_id" "uuid", "p_role_id" integer) RETURNS boolean
    LANGUAGE "plpgsql"
    SET "search_path" TO ''
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.organization_members 
    WHERE user_id = p_user_id 
    AND org_id = p_org_id 
    AND org_member_role = p_role_id
    AND org_member_is_active = true
  );
END;
$$;


ALTER FUNCTION "public"."has_organization_role"("p_user_id" "uuid", "p_org_id" "uuid", "p_role_id" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_user_role"("role_name" "text") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
  SELECT COALESCE(
    (auth.jwt() ->> 'role')::text = role_name,
    false
  );
$$;


ALTER FUNCTION "public"."has_user_role"("role_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."initialize_user_organization_context"("p_user_id" "uuid") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
  v_org_id UUID;
  v_default_org_id UUID;
  v_any_org_id UUID;
BEGIN
  -- Check if user already has an active context
  SELECT org_id INTO v_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  AND org_member_is_active = true
  LIMIT 1;
  
  -- Return existing context if found
  IF v_org_id IS NOT NULL THEN
    RETURN v_org_id;
  END IF;
  
  -- Try to find default organization
  SELECT org_id INTO v_default_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_default_org = true
  AND org_member_is_active = true
  LIMIT 1;
  
  -- If default org found, set it as context
  IF v_default_org_id IS NOT NULL THEN
    PERFORM public.set_user_organization_context(p_user_id, v_default_org_id);
    RETURN v_default_org_id;
  END IF;
  
  -- No default org, try any active org
  SELECT org_id INTO v_any_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND org_member_is_active = true
  LIMIT 1;
  
  -- If any org found, set it as context
  IF v_any_org_id IS NOT NULL THEN
    PERFORM public.set_user_organization_context(p_user_id, v_any_org_id);
    RETURN v_any_org_id;
  END IF;
  
  -- No organizations found
  RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."initialize_user_organization_context"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_in_organization"("org_id_param" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM organization_members
    WHERE user_id = auth.uid()
    AND org_id = org_id_param
    AND org_member_is_active = true
  );
END;
$$;


ALTER FUNCTION "public"."is_in_organization"("org_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_org_admin"("org_id_param" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $$ 
  SELECT EXISTS (
    SELECT 1
    FROM public.organization_members
    WHERE user_id = auth.uid()
    AND organization_members.org_id = org_id_param
    AND org_member_role = 2
  );
$$;


ALTER FUNCTION "public"."is_org_admin"("org_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_org_member"("org_id_param" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $$ 
  SELECT EXISTS (
    SELECT 1
    FROM public.organization_members
    WHERE user_id = auth.uid()
    AND organization_members.org_id = org_id_param
  );
$$;


ALTER FUNCTION "public"."is_org_member"("org_id_param" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_org_member"("user_uid" "uuid", "organization_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  select exists (
    select 1
    from organization_members
    where user_id = user_uid
    and org_id = organization_id
    and org_member_is_active = true
  );
$$;


ALTER FUNCTION "public"."is_org_member"("user_uid" "uuid", "organization_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_superadmin"("user_uid" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
  select exists (
    select 1
    from roles r
    where r.role_name = 'superadmin'
    and r.role_id in (
      select org_member_role
      from organization_members
      where user_id = user_uid
      and org_member_is_active = true
    )
  );
$$;


ALTER FUNCTION "public"."is_superadmin"("user_uid" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_superadmin_check"("check_user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
  select exists (
    select 1
    from public.organization_members om
    join public.roles r on r.role_id = om.org_member_role
    where om.user_id = check_user_id
    and r.role_name = 'superadmin'
    and om.org_member_is_active = true
  );
$$;


ALTER FUNCTION "public"."is_superadmin_check"("check_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_superadmin_v2"() RETURNS boolean
    LANGUAGE "sql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$ 
  SELECT EXISTS (
    SELECT 1
    FROM auth.users
    WHERE id = auth.uid()
    AND raw_app_meta_data->>'role' = 'superadmin'
  );
$$;


ALTER FUNCTION "public"."is_superadmin_v2"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_policy_check"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RAISE LOG 'Policy check: user_id=%, auth.uid()=%, org_id=%',
        NEW.user_id,
        auth.uid(),
        NEW.org_id;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."log_policy_check"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."refresh_user_roles"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.user_roles;
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."refresh_user_roles"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_current_organization_context"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    deleted_user_id uuid := OLD.user_id; -- Get the user ID from the deleted entry
    new_default_org uuid;
BEGIN
    -- Check if the user still exists in the organization_members table
    IF NOT EXISTS (
        SELECT 1
        FROM organization_members
        WHERE user_id = deleted_user_id
    ) THEN
        RETURN OLD; -- User does not exist anymore, stop evaluation
    END IF;

    -- Check if the user has another entry with is_current_context set to TRUE
    IF EXISTS (
        SELECT 1
        FROM organization_members
        WHERE user_id = deleted_user_id AND is_current_context = true
    ) THEN
        RETURN OLD; -- User has another active context, stop evaluation
    END IF;

    -- User has entries but none with is_current_context set to TRUE
    -- Check for a default organization
    SELECT org_id INTO new_default_org
    FROM organization_members
    WHERE user_id = deleted_user_id AND org_member_is_active = true AND is_default_org = true
    LIMIT 1;

    -- If no default organization, check for any other active organization
    IF new_default_org IS NULL THEN
        SELECT org_id INTO new_default_org
        FROM organization_members
        WHERE user_id = deleted_user_id AND org_member_is_active = true
        LIMIT 1;
    END IF;

    -- Update the is_current_context flag for the new organization
    IF new_default_org IS NOT NULL THEN
        UPDATE organization_members
        SET is_current_context = true
        WHERE user_id = deleted_user_id AND org_id = new_default_org;
    END IF;

    RETURN OLD;
END;
$$;


ALTER FUNCTION "public"."set_current_organization_context"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_org_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Retrieve the org_name from the organizations table
    SELECT org_name INTO NEW.org_name
    FROM organizations
    WHERE id = NEW.org_id;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_org_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_user_full_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RAISE NOTICE 'Processing user_id: %', NEW.user_id;

    -- Retrieve the full_name from the profiles table
    SELECT full_name INTO NEW.user_full_name
    FROM profiles
    WHERE id = NEW.user_id;

    IF NEW.user_full_name IS NULL THEN
        RAISE NOTICE 'No matching full_name found for user_id: %', NEW.user_id;
    ELSE
        RAISE NOTICE 'Found full_name: % for user_id: %', NEW.user_full_name, NEW.user_id;
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_user_full_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_user_organization_context"("p_user_id" "uuid", "p_org_id" "uuid") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
  v_current_context_org_id UUID;
BEGIN
  -- Check if the requested org is already the current context
  SELECT org_id INTO v_current_context_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  LIMIT 1;
  
  -- If the requested org is already the current context, do nothing
  IF v_current_context_org_id = p_org_id THEN
    RETURN;
  END IF;
  
  -- First, set the current context to false (only if there is one)
  IF v_current_context_org_id IS NOT NULL THEN
    UPDATE public.organization_members
    SET is_current_context = false
    WHERE user_id = p_user_id
    AND org_id = v_current_context_org_id;
  END IF;
  
  -- Then set the selected org to true
  UPDATE public.organization_members
  SET is_current_context = true
  WHERE user_id = p_user_id
  AND org_id = p_org_id;
END;
$$;


ALTER FUNCTION "public"."set_user_organization_context"("p_user_id" "uuid", "p_org_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_email_invitations_timestamp"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_email_invitations_timestamp"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_org_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    UPDATE organization_members
    SET org_name = NEW.org_name
    WHERE org_id = NEW.id;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_org_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
begin
    new.updated_at = now();
    return new;
end;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_user_full_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    UPDATE organization_members
    SET user_full_name = NEW.full_name
    WHERE user_id = NEW.id;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_user_full_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."user_has_organizations"("user_id_param" "uuid") RETURNS boolean
    LANGUAGE "plpgsql"
    SET "search_path" TO ''
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.organization_members
    WHERE user_id = user_id_param
    AND org_member_is_active = true
  );
END;
$$;


ALTER FUNCTION "public"."user_has_organizations"("user_id_param" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."user_has_organizations"("user_id_param" "uuid") IS 'Checks if a user belongs to any active organization';


SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "drizzle"."__drizzle_migrations" (
    "id" integer NOT NULL,
    "hash" "text" NOT NULL,
    "created_at" bigint
);


ALTER TABLE "drizzle"."__drizzle_migrations" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "drizzle"."__drizzle_migrations_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "drizzle"."__drizzle_migrations_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "drizzle"."__drizzle_migrations_id_seq" OWNED BY "drizzle"."__drizzle_migrations"."id";



CREATE TABLE IF NOT EXISTS "public"."email_invitations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "org_id" "uuid" NOT NULL,
    "role_id" integer NOT NULL,
    "email" "extensions"."citext" NOT NULL,
    "invited_by" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone NOT NULL,
    "status" "public"."invitation_status" DEFAULT 'created'::"public"."invitation_status",
    "accepted_by" "uuid",
    "accepted_at" timestamp with time zone,
    "resend_email_id" "text",
    "personal_message" "text",
    "invitation_hash" "text" NOT NULL,
    CONSTRAINT "valid_acceptance" CHECK (((("status" = 'accepted'::"public"."invitation_status") AND ("accepted_by" IS NOT NULL) AND ("accepted_at" IS NOT NULL)) OR (("status" <> 'accepted'::"public"."invitation_status") AND ("accepted_by" IS NULL) AND ("accepted_at" IS NULL)))),
    CONSTRAINT "valid_expiry" CHECK (("expires_at" > "created_at"))
);


ALTER TABLE "public"."email_invitations" OWNER TO "postgres";


COMMENT ON TABLE "public"."email_invitations" IS 'Email-based organization invitations with comprehensive status tracking';



COMMENT ON COLUMN "public"."email_invitations"."status" IS 'Tracks invitation lifecycle from creation to completion';



COMMENT ON COLUMN "public"."email_invitations"."resend_email_id" IS 'Resend API message ID for delivery tracking';



CREATE TABLE IF NOT EXISTS "public"."flows" (
    "id" bigint NOT NULL,
    "uid" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "position" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."flows" OWNER TO "postgres";


ALTER TABLE "public"."flows" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."flows_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."flowstepcategories" (
    "id" bigint NOT NULL,
    "uid" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "position" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."flowstepcategories" OWNER TO "postgres";


COMMENT ON TABLE "public"."flowstepcategories" IS 'Defines the categories for steps';



ALTER TABLE "public"."flowstepcategories" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."flowstepcategories_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."flowsteps" (
    "id" bigint NOT NULL,
    "uid" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "type" "text",
    "category_id" bigint,
    "position" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."flowsteps" OWNER TO "postgres";


COMMENT ON TABLE "public"."flowsteps" IS 'This table defines the steps which can be used in a flow';



ALTER TABLE "public"."flowsteps" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."flowsteps_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."labelcategories" (
    "id" bigint NOT NULL,
    "uid" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "position" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."labelcategories" OWNER TO "postgres";


ALTER TABLE "public"."labelcategories" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."labelcategories_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."labels" (
    "id" bigint NOT NULL,
    "uid" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "type" "text",
    "category_id" bigint,
    "position" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."labels" OWNER TO "postgres";


ALTER TABLE "public"."labels" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."labels_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."organization_members" (
    "org_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "org_member_role" smallint NOT NULL,
    "org_member_is_active" boolean NOT NULL,
    "org_member_updated_by" "uuid",
    "is_default_org" boolean DEFAULT false NOT NULL,
    "user_full_name" "text",
    "org_name" "text",
    "is_current_context" boolean DEFAULT false
);

ALTER TABLE ONLY "public"."organization_members" REPLICA IDENTITY FULL;

ALTER TABLE ONLY "public"."organization_members" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."organization_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "org_name" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "org_icon" "text",
    "is_active" boolean NOT NULL
);

ALTER TABLE ONLY "public"."organizations" REPLICA IDENTITY FULL;

ALTER TABLE ONLY "public"."organizations" FORCE ROW LEVEL SECURITY;


ALTER TABLE "public"."organizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "full_name" "text",
    "email" "text",
    "avatar_url" "text",
    "username" "text"
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rbac_test" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "color" "text",
    "name" "text",
    "switch" boolean
);


ALTER TABLE "public"."rbac_test" OWNER TO "postgres";


COMMENT ON TABLE "public"."rbac_test" IS 'for testing rbac';



ALTER TABLE "public"."rbac_test" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."rbac_test_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."roles" (
    "role_id" smallint NOT NULL,
    "role_name" "text" NOT NULL
);


ALTER TABLE "public"."roles" OWNER TO "postgres";


ALTER TABLE "public"."roles" ALTER COLUMN "role_id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."roles_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."steps" (
    "id" bigint NOT NULL,
    "uid" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "type" "text" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text",
    "position" integer NOT NULL,
    "flow_id" bigint,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."steps" OWNER TO "postgres";


ALTER TABLE "public"."steps" ALTER COLUMN "id" ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME "public"."steps_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."superusers" (
    "id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "role_id" smallint DEFAULT '1'::smallint NOT NULL
);


ALTER TABLE "public"."superusers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_personal_information" (
    "id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "first_name" "text",
    "middle_name" "text",
    "last_name" "text",
    "passport_name" "text",
    "birth_name" "text",
    "previous_name" "text",
    "nickname" "text",
    "non_latin_full_name" "text",
    "dob" "date",
    "nationality" "text",
    "nationality_birth" "text",
    "sex" "text",
    "place_of_birth" "text",
    "height_cm" smallint,
    "passport_doc_id" "text",
    "passport_date_issue" "date",
    "passport_date_expiry" "date",
    "passport_country" "text",
    "updated_at" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "updated_by" "text"
);


ALTER TABLE "public"."user_personal_information" OWNER TO "postgres";


COMMENT ON COLUMN "public"."user_personal_information"."previous_name" IS 'Maiden name';



COMMENT ON COLUMN "public"."user_personal_information"."non_latin_full_name" IS 'For non-Latin alphabets (e.g., Chinese, Arabic, Thai)';



COMMENT ON COLUMN "public"."user_personal_information"."dob" IS 'Day of birth';



COMMENT ON COLUMN "public"."user_personal_information"."nationality_birth" IS 'Nationality at birth';



CREATE MATERIALIZED VIEW "public"."user_roles" AS
 SELECT DISTINCT "organization_members"."user_id",
        CASE
            WHEN ("organization_members"."org_member_role" = 1) THEN 'superadmin'::"text"
            WHEN ("organization_members"."org_member_role" = 3) THEN 'orgadmin'::"text"
            ELSE 'member'::"text"
        END AS "role_type",
    "organization_members"."org_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."org_member_is_active" = true)
  WITH NO DATA;


ALTER TABLE "public"."user_roles" OWNER TO "postgres";


ALTER TABLE ONLY "drizzle"."__drizzle_migrations" ALTER COLUMN "id" SET DEFAULT "nextval"('"drizzle"."__drizzle_migrations_id_seq"'::"regclass");



ALTER TABLE ONLY "drizzle"."__drizzle_migrations"
    ADD CONSTRAINT "__drizzle_migrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."email_invitations"
    ADD CONSTRAINT "email_invitations_invitation_hash_key" UNIQUE ("invitation_hash");



ALTER TABLE ONLY "public"."email_invitations"
    ADD CONSTRAINT "email_invitations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."flows"
    ADD CONSTRAINT "flows_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."flows"
    ADD CONSTRAINT "flows_uid_key" UNIQUE ("uid");



ALTER TABLE ONLY "public"."flowstepcategories"
    ADD CONSTRAINT "flowstepcategories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."flowstepcategories"
    ADD CONSTRAINT "flowstepcategories_uid_key" UNIQUE ("uid");



ALTER TABLE ONLY "public"."flowsteps"
    ADD CONSTRAINT "flowsteps_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."flowsteps"
    ADD CONSTRAINT "flowsteps_uid_key" UNIQUE ("uid");



ALTER TABLE ONLY "public"."labelcategories"
    ADD CONSTRAINT "labelcategories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."labelcategories"
    ADD CONSTRAINT "labelcategories_uid_key" UNIQUE ("uid");



ALTER TABLE ONLY "public"."labels"
    ADD CONSTRAINT "labels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."labels"
    ADD CONSTRAINT "labels_uid_key" UNIQUE ("uid");



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_pkey" PRIMARY KEY ("org_id", "user_id");



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_user_org" UNIQUE ("user_id", "org_id");



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_username_unique" UNIQUE ("username");



ALTER TABLE ONLY "public"."rbac_test"
    ADD CONSTRAINT "rbac_test_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_pkey" PRIMARY KEY ("role_id");



ALTER TABLE ONLY "public"."steps"
    ADD CONSTRAINT "steps_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."steps"
    ADD CONSTRAINT "steps_uid_key" UNIQUE ("uid");



ALTER TABLE ONLY "public"."superusers"
    ADD CONSTRAINT "superadmins_id_key" UNIQUE ("id");



ALTER TABLE ONLY "public"."superusers"
    ADD CONSTRAINT "superadmins_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_personal_information"
    ADD CONSTRAINT "user_details_pkey" PRIMARY KEY ("id");



CREATE INDEX "flows_position_idx" ON "public"."flows" USING "btree" ("position");



CREATE INDEX "idx_email_invitations_email" ON "public"."email_invitations" USING "btree" ("email");



CREATE INDEX "idx_email_invitations_expires" ON "public"."email_invitations" USING "btree" ("expires_at");



CREATE INDEX "idx_email_invitations_org_id" ON "public"."email_invitations" USING "btree" ("org_id");



CREATE INDEX "idx_email_invitations_status" ON "public"."email_invitations" USING "btree" ("status");



CREATE UNIQUE INDEX "idx_one_active_context_per_user" ON "public"."organization_members" USING "btree" ("user_id", "is_current_context") WHERE ("is_current_context" = true);



CREATE INDEX "idx_org_members_active" ON "public"."organization_members" USING "btree" ("org_member_is_active");



CREATE INDEX "idx_org_members_default" ON "public"."organization_members" USING "btree" ("user_id") WHERE ("is_default_org" = true);



CREATE INDEX "idx_org_members_org_id" ON "public"."organization_members" USING "btree" ("org_id");



CREATE INDEX "idx_org_members_role" ON "public"."organization_members" USING "btree" ("org_member_role");



CREATE INDEX "idx_org_members_user_id" ON "public"."organization_members" USING "btree" ("user_id");



CREATE INDEX "idx_org_members_user_id_active" ON "public"."organization_members" USING "btree" ("user_id") WHERE ("org_member_is_active" = true);



CREATE INDEX "idx_roles_id" ON "public"."roles" USING "btree" ("role_id");



CREATE UNIQUE INDEX "idx_user_roles_composite" ON "public"."user_roles" USING "btree" ("user_id", "role_type", "org_id");



CREATE INDEX "profiles_username_idx" ON "public"."profiles" USING "btree" ("username");



CREATE INDEX "steps_flow_id_position_idx" ON "public"."steps" USING "btree" ("flow_id", "position");



CREATE UNIQUE INDEX "uniq_pending_invite_hash" ON "public"."email_invitations" USING "btree" ("invitation_hash") WHERE ("status" = ANY (ARRAY['created'::"public"."invitation_status", 'sent'::"public"."invitation_status", 'delivered'::"public"."invitation_status", 'failed'::"public"."invitation_status"]));



CREATE UNIQUE INDEX "uniq_pending_invite_per_org_email" ON "public"."email_invitations" USING "btree" ("email", "org_id") WHERE ("status" = ANY (ARRAY['created'::"public"."invitation_status", 'sent'::"public"."invitation_status", 'delivered'::"public"."invitation_status", 'failed'::"public"."invitation_status"]));



CREATE OR REPLACE TRIGGER "add_superusers_to_new_org" AFTER INSERT ON "public"."organizations" FOR EACH ROW EXECUTE FUNCTION "public"."add_superusers_to_org"();



CREATE OR REPLACE TRIGGER "after_organization_member_delete" AFTER DELETE ON "public"."organization_members" FOR EACH ROW EXECUTE FUNCTION "public"."set_current_organization_context"();



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."labelcategories" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."labels" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "handle_user_personal_information_updated_at" BEFORE UPDATE ON "public"."user_personal_information" FOR EACH ROW EXECUTE FUNCTION "public"."handle_updated_at"();



CREATE OR REPLACE TRIGGER "insert_org_name" BEFORE INSERT ON "public"."organization_members" FOR EACH ROW EXECUTE FUNCTION "public"."set_org_name"();



CREATE OR REPLACE TRIGGER "insert_user_full_name" BEFORE INSERT ON "public"."organization_members" FOR EACH ROW EXECUTE FUNCTION "public"."set_user_full_name"();



CREATE OR REPLACE TRIGGER "log_policy_check_trigger" BEFORE INSERT ON "public"."organization_members" FOR EACH ROW EXECUTE FUNCTION "public"."log_policy_check"();



CREATE OR REPLACE TRIGGER "refresh_user_roles_trigger" AFTER INSERT OR DELETE OR UPDATE ON "public"."organization_members" FOR EACH STATEMENT EXECUTE FUNCTION "public"."refresh_user_roles"();



CREATE OR REPLACE TRIGGER "trg_update_email_invitations_timestamp" BEFORE UPDATE ON "public"."email_invitations" FOR EACH ROW EXECUTE FUNCTION "public"."update_email_invitations_timestamp"();



CREATE OR REPLACE TRIGGER "trigger_update_org_name" AFTER UPDATE ON "public"."organizations" FOR EACH ROW WHEN (("old"."org_name" IS DISTINCT FROM "new"."org_name")) EXECUTE FUNCTION "public"."update_org_name"();



CREATE OR REPLACE TRIGGER "trigger_update_user_full_name" AFTER UPDATE ON "public"."profiles" FOR EACH ROW WHEN (("old"."full_name" IS DISTINCT FROM "new"."full_name")) EXECUTE FUNCTION "public"."update_user_full_name"();



CREATE OR REPLACE TRIGGER "update_flows_updated_at" BEFORE UPDATE ON "public"."flows" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_steps_updated_at" BEFORE UPDATE ON "public"."steps" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."email_invitations"
    ADD CONSTRAINT "email_invitations_accepted_by_fkey" FOREIGN KEY ("accepted_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."email_invitations"
    ADD CONSTRAINT "email_invitations_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."email_invitations"
    ADD CONSTRAINT "email_invitations_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."email_invitations"
    ADD CONSTRAINT "email_invitations_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("role_id");



ALTER TABLE ONLY "public"."flowsteps"
    ADD CONSTRAINT "flowsteps_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."flowstepcategories"("id") ON UPDATE CASCADE;



ALTER TABLE ONLY "public"."labels"
    ADD CONSTRAINT "labels_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."labelcategories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_org_member_role_fkey" FOREIGN KEY ("org_member_role") REFERENCES "public"."roles"("role_id") ON UPDATE CASCADE ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_org_member_updated_by_fkey" FOREIGN KEY ("org_member_updated_by") REFERENCES "public"."profiles"("id") ON UPDATE RESTRICT ON DELETE RESTRICT;



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_user_id_profiles_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."steps"
    ADD CONSTRAINT "steps_flow_id_fkey" FOREIGN KEY ("flow_id") REFERENCES "public"."flows"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."superusers"
    ADD CONSTRAINT "superadmins_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."superusers"
    ADD CONSTRAINT "superadmins_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("role_id") ON UPDATE CASCADE ON DELETE SET DEFAULT;



ALTER TABLE ONLY "public"."user_personal_information"
    ADD CONSTRAINT "user_personal_information_id_fkey1" FOREIGN KEY ("id") REFERENCES "public"."profiles"("id") ON UPDATE CASCADE ON DELETE CASCADE;



CREATE POLICY "Allow full access to authenticated users" ON "public"."labelcategories" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."labels" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow read access to all authenticated users" ON "public"."labelcategories" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow read access to all authenticated users" ON "public"."labels" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow superadmin to insert organizations" ON "public"."organizations" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."user_id" = "auth"."uid"()) AND ("om"."org_member_role" = 1) AND ("om"."org_member_is_active" = true)))));



CREATE POLICY "Allow superusers to view all data" ON "public"."profiles" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."superusers"
  WHERE (("superusers"."id" = "auth"."uid"()) AND ("superusers"."role_id" = 1)))));



CREATE POLICY "Enable all access for authenticated users" ON "public"."flows" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Enable all access for authenticated users" ON "public"."steps" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."organizations" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable read access for all users" ON "public"."organizations" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."roles" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."user_personal_information" FOR SELECT USING (true);



CREATE POLICY "Enable users to view their own data only" ON "public"."profiles" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "id"));



CREATE POLICY "Read access for all authenticated users" ON "public"."superusers" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Roles are viewable by authenticated users" ON "public"."roles" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Update personal info" ON "public"."user_personal_information" FOR UPDATE TO "authenticated" USING (("id" = "auth"."uid"())) WITH CHECK (("id" = "auth"."uid"()));



CREATE POLICY "Users can insert their own personal information." ON "public"."user_personal_information" FOR INSERT WITH CHECK (("auth"."uid"() = "id"));



CREATE POLICY "Users can insert their own profile." ON "public"."profiles" FOR INSERT TO "authenticated" WITH CHECK (("auth"."uid"() = "id"));



CREATE POLICY "Users can update own profile." ON "public"."profiles" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can update their own personal information." ON "public"."user_personal_information" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view their own personal information." ON "public"."user_personal_information" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "all_users_select_roles" ON "public"."roles" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "authenticated_insert_own_personal_information" ON "public"."user_personal_information" FOR INSERT TO "authenticated" WITH CHECK (("id" = "auth"."uid"()));



CREATE POLICY "authenticated_select_own_personal_information" ON "public"."user_personal_information" FOR SELECT TO "authenticated" USING (("id" = "auth"."uid"()));



CREATE POLICY "authenticated_select_own_profile" ON "public"."profiles" FOR SELECT TO "authenticated" USING (("id" = "auth"."uid"()));



CREATE POLICY "authenticated_update_own_personal_information" ON "public"."user_personal_information" FOR UPDATE TO "authenticated" USING (("id" = "auth"."uid"())) WITH CHECK (("id" = "auth"."uid"()));



CREATE POLICY "authenticated_update_own_profile" ON "public"."profiles" FOR UPDATE TO "authenticated" USING (("id" = "auth"."uid"())) WITH CHECK (("id" = "auth"."uid"()));



CREATE POLICY "email_invitations_delete_policy" ON "public"."email_invitations" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."user_id" = "auth"."uid"()) AND ("om"."org_id" = "email_invitations"."org_id") AND ("om"."org_member_is_active" = true) AND ("om"."org_member_role" <= 3)))));



CREATE POLICY "email_invitations_insert_policy" ON "public"."email_invitations" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."user_id" = "auth"."uid"()) AND ("om"."org_id" = "email_invitations"."org_id") AND ("om"."org_member_is_active" = true) AND ("om"."org_member_role" <= 4)))));



CREATE POLICY "email_invitations_select_policy" ON "public"."email_invitations" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."user_id" = "auth"."uid"()) AND ("om"."org_id" = "email_invitations"."org_id") AND ("om"."org_member_is_active" = true)))) OR (("email")::"text" = ( SELECT "profiles"."email"
   FROM "public"."profiles"
  WHERE ("profiles"."id" = "auth"."uid"()))) OR (EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."user_id" = "auth"."uid"()) AND ("om"."org_member_is_active" = true) AND ("om"."org_member_role" <= 2))))));



CREATE POLICY "email_invitations_update_policy" ON "public"."email_invitations" FOR UPDATE USING (((("email")::"text" = ( SELECT "profiles"."email"
   FROM "public"."profiles"
  WHERE ("profiles"."id" = "auth"."uid"()))) OR (EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."user_id" = "auth"."uid"()) AND ("om"."org_id" = "email_invitations"."org_id") AND ("om"."org_member_is_active" = true) AND ("om"."org_member_role" <= 3))))));



ALTER TABLE "public"."flows" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."labelcategories" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "org_users_select_organizations" ON "public"."organizations" FOR SELECT TO "authenticated" USING (("id" IN ( SELECT "organization_members"."org_id"
   FROM "public"."organization_members"
  WHERE (("organization_members"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members"."org_member_is_active" = true)))));



CREATE POLICY "orgaccounting_select_user_personal_information" ON "public"."user_personal_information" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'orgaccounting'::"text") AND ("id" IN ( SELECT "organization_members"."user_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."org_id" IN ( SELECT "organization_members_1"."org_id"
           FROM "public"."organization_members" "organization_members_1"
          WHERE (("organization_members_1"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members_1"."org_member_role" = 5))))))));



CREATE POLICY "orgadmin_select_organizations" ON "public"."organizations" FOR SELECT TO "authenticated" USING (("id" IN ( SELECT "organization_members"."org_id"
   FROM "public"."organization_members"
  WHERE (("organization_members"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members"."org_member_role" = 3)))));



CREATE POLICY "orgadmin_select_profiles" ON "public"."profiles" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'orgadmin'::"text") AND ("id" IN ( SELECT "organization_members"."user_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."org_id" IN ( SELECT "organization_members_1"."org_id"
           FROM "public"."organization_members" "organization_members_1"
          WHERE (("organization_members_1"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members_1"."org_member_role" = 3))))))));



CREATE POLICY "orgadmin_select_user_personal_information" ON "public"."user_personal_information" FOR SELECT TO "authenticated" USING (("id" IN ( SELECT "organization_members"."user_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."org_id" IN ( SELECT "organization_members_1"."org_id"
           FROM "public"."organization_members" "organization_members_1"
          WHERE (("organization_members_1"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members_1"."org_member_role" = 3)))))));



CREATE POLICY "orgadmin_update_organizations" ON "public"."organizations" FOR UPDATE TO "authenticated" USING (("id" IN ( SELECT "organization_members"."org_id"
   FROM "public"."organization_members"
  WHERE (("organization_members"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members"."org_member_role" = 3))))) WITH CHECK (("id" IN ( SELECT "organization_members"."org_id"
   FROM "public"."organization_members"
  WHERE (("organization_members"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members"."org_member_role" = 3)))));



CREATE POLICY "orgmember_select_profiles" ON "public"."profiles" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'orgmember'::"text") AND ("id" IN ( SELECT "organization_members"."user_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."org_id" IN ( SELECT "organization_members_1"."org_id"
           FROM "public"."organization_members" "organization_members_1"
          WHERE (("organization_members_1"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("organization_members_1"."org_member_role" = 4))))))));



ALTER TABLE "public"."roles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."steps" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "superadmin_delete_organizations" ON "public"."organizations" FOR DELETE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_delete_profiles" ON "public"."profiles" FOR DELETE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_delete_roles" ON "public"."roles" FOR DELETE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_delete_user_personal_information" ON "public"."user_personal_information" FOR DELETE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_insert_organizations" ON "public"."organizations" FOR INSERT TO "authenticated" WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_insert_profiles" ON "public"."profiles" FOR INSERT TO "authenticated" WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_insert_roles" ON "public"."roles" FOR INSERT TO "authenticated" WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_insert_user_personal_information" ON "public"."user_personal_information" FOR INSERT TO "authenticated" WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_select_organizations" ON "public"."organizations" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_select_profiles" ON "public"."profiles" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_select_user_personal_information" ON "public"."user_personal_information" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_update_organizations" ON "public"."organizations" FOR UPDATE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text"))) WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_update_profiles" ON "public"."profiles" FOR UPDATE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text"))) WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_update_roles" ON "public"."roles" FOR UPDATE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text"))) WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



CREATE POLICY "superadmin_update_user_personal_information" ON "public"."user_personal_information" FOR UPDATE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text"))) WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'superadmin'::"text")));



ALTER TABLE "public"."superusers" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "supportadmin_select_organizations" ON "public"."organizations" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'supportadmin'::"text")));



CREATE POLICY "supportadmin_select_profiles" ON "public"."profiles" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'supportadmin'::"text")));



CREATE POLICY "supportadmin_select_user_personal_information" ON "public"."user_personal_information" FOR SELECT TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'supportadmin'::"text")));



CREATE POLICY "supportadmin_update_profiles" ON "public"."profiles" FOR UPDATE TO "authenticated" USING (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'supportadmin'::"text"))) WITH CHECK (((( SELECT ("auth"."jwt"() -> 'app_metadata'::"text")) ? 'role'::"text") AND (( SELECT (("auth"."jwt"() -> 'app_metadata'::"text") ->> 'role'::"text")) = 'supportadmin'::"text")));



CREATE POLICY "users_select_organizations_as_member" ON "public"."organizations" FOR SELECT TO "authenticated" USING (("id" IN ( SELECT "organization_members"."org_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."user_id" = "auth"."uid"()))));



CREATE POLICY "users_select_own_personal_information" ON "public"."user_personal_information" FOR SELECT TO "authenticated" USING (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "users_select_own_profile" ON "public"."profiles" FOR SELECT TO "authenticated" USING (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "users_select_roles" ON "public"."roles" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "users_update_own_personal_information" ON "public"."user_personal_information" FOR UPDATE TO "authenticated" USING (("id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "users_update_own_profile" ON "public"."profiles" FOR UPDATE TO "authenticated" USING (("id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("id" = ( SELECT "auth"."uid"() AS "uid")));





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";






ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."organization_members";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."organizations";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."rbac_test";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



























































































































































































































































































































GRANT ALL ON FUNCTION "public"."accept_invitation"("p_token" "text", "p_user_id" "uuid", "p_full_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_invitation"("p_token" "text", "p_user_id" "uuid", "p_full_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_invitation"("p_token" "text", "p_user_id" "uuid", "p_full_name" "text") TO "service_role";



REVOKE ALL ON FUNCTION "public"."accept_invitation_anonymous_complete"("p_hash" "text", "p_user_id" "uuid", "p_email" "text", "p_full_name" "text") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."accept_invitation_anonymous_complete"("p_hash" "text", "p_user_id" "uuid", "p_email" "text", "p_full_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_invitation_anonymous_complete"("p_hash" "text", "p_user_id" "uuid", "p_email" "text", "p_full_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_invitation_anonymous_complete"("p_hash" "text", "p_user_id" "uuid", "p_email" "text", "p_full_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_superusers_to_org"() TO "anon";
GRANT ALL ON FUNCTION "public"."add_superusers_to_org"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_superusers_to_org"() TO "service_role";



GRANT ALL ON FUNCTION "public"."check_org_admin_access"("check_org_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."check_org_admin_access"("check_org_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_org_admin_access"("check_org_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."current_user_has_organizations"() TO "anon";
GRANT ALL ON FUNCTION "public"."current_user_has_organizations"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."current_user_has_organizations"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_role_name"("role_id" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."get_role_name"("role_id" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_role_name"("role_id" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_active_organization"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_active_organization"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_active_organization"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_org_role"("p_org_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_org_role"("p_org_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_org_role"("p_org_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_organization_ids"("check_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_organization_ids"("check_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_organization_ids"("check_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."has_any_organizations"() TO "anon";
GRANT ALL ON FUNCTION "public"."has_any_organizations"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_any_organizations"() TO "service_role";



GRANT ALL ON FUNCTION "public"."has_organization_role"("p_user_id" "uuid", "p_org_id" "uuid", "p_role_id" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."has_organization_role"("p_user_id" "uuid", "p_org_id" "uuid", "p_role_id" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_organization_role"("p_user_id" "uuid", "p_org_id" "uuid", "p_role_id" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."has_user_role"("role_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."has_user_role"("role_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_user_role"("role_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."initialize_user_organization_context"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."initialize_user_organization_context"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."initialize_user_organization_context"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_in_organization"("org_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_in_organization"("org_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_in_organization"("org_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_org_admin"("org_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_org_admin"("org_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_org_admin"("org_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_org_member"("org_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_org_member"("org_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_org_member"("org_id_param" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_org_member"("user_uid" "uuid", "organization_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_org_member"("user_uid" "uuid", "organization_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_org_member"("user_uid" "uuid", "organization_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_superadmin"("user_uid" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_superadmin"("user_uid" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_superadmin"("user_uid" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_superadmin_check"("check_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_superadmin_check"("check_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_superadmin_check"("check_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_superadmin_v2"() TO "anon";
GRANT ALL ON FUNCTION "public"."is_superadmin_v2"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_superadmin_v2"() TO "service_role";



GRANT ALL ON FUNCTION "public"."log_policy_check"() TO "anon";
GRANT ALL ON FUNCTION "public"."log_policy_check"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_policy_check"() TO "service_role";



GRANT ALL ON FUNCTION "public"."refresh_user_roles"() TO "anon";
GRANT ALL ON FUNCTION "public"."refresh_user_roles"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."refresh_user_roles"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_current_organization_context"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_current_organization_context"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_current_organization_context"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_org_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_org_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_org_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_user_full_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_user_full_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_user_full_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_user_organization_context"("p_user_id" "uuid", "p_org_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."set_user_organization_context"("p_user_id" "uuid", "p_org_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_user_organization_context"("p_user_id" "uuid", "p_org_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_email_invitations_timestamp"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_email_invitations_timestamp"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_email_invitations_timestamp"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_org_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_org_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_org_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_full_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_full_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_full_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."user_has_organizations"("user_id_param" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."user_has_organizations"("user_id_param" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."user_has_organizations"("user_id_param" "uuid") TO "service_role";
























GRANT ALL ON TABLE "public"."email_invitations" TO "anon";
GRANT ALL ON TABLE "public"."email_invitations" TO "authenticated";
GRANT ALL ON TABLE "public"."email_invitations" TO "service_role";



GRANT ALL ON TABLE "public"."flows" TO "anon";
GRANT ALL ON TABLE "public"."flows" TO "authenticated";
GRANT ALL ON TABLE "public"."flows" TO "service_role";



GRANT ALL ON SEQUENCE "public"."flows_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."flows_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."flows_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."flowstepcategories" TO "anon";
GRANT ALL ON TABLE "public"."flowstepcategories" TO "authenticated";
GRANT ALL ON TABLE "public"."flowstepcategories" TO "service_role";



GRANT ALL ON SEQUENCE "public"."flowstepcategories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."flowstepcategories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."flowstepcategories_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."flowsteps" TO "anon";
GRANT ALL ON TABLE "public"."flowsteps" TO "authenticated";
GRANT ALL ON TABLE "public"."flowsteps" TO "service_role";



GRANT ALL ON SEQUENCE "public"."flowsteps_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."flowsteps_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."flowsteps_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."labelcategories" TO "anon";
GRANT ALL ON TABLE "public"."labelcategories" TO "authenticated";
GRANT ALL ON TABLE "public"."labelcategories" TO "service_role";



GRANT ALL ON SEQUENCE "public"."labelcategories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."labelcategories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."labelcategories_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."labels" TO "anon";
GRANT ALL ON TABLE "public"."labels" TO "authenticated";
GRANT ALL ON TABLE "public"."labels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."labels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."labels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."labels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."organization_members" TO "anon";
GRANT ALL ON TABLE "public"."organization_members" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_members" TO "service_role";



GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."rbac_test" TO "anon";
GRANT ALL ON TABLE "public"."rbac_test" TO "authenticated";
GRANT ALL ON TABLE "public"."rbac_test" TO "service_role";



GRANT ALL ON SEQUENCE "public"."rbac_test_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."rbac_test_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."rbac_test_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."roles" TO "anon";
GRANT ALL ON TABLE "public"."roles" TO "authenticated";
GRANT ALL ON TABLE "public"."roles" TO "service_role";



GRANT ALL ON SEQUENCE "public"."roles_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."roles_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."roles_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."steps" TO "anon";
GRANT ALL ON TABLE "public"."steps" TO "authenticated";
GRANT ALL ON TABLE "public"."steps" TO "service_role";



GRANT ALL ON SEQUENCE "public"."steps_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."steps_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."steps_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."superusers" TO "anon";
GRANT ALL ON TABLE "public"."superusers" TO "authenticated";
GRANT ALL ON TABLE "public"."superusers" TO "service_role";



GRANT ALL ON TABLE "public"."user_personal_information" TO "anon";
GRANT ALL ON TABLE "public"."user_personal_information" TO "authenticated";
GRANT ALL ON TABLE "public"."user_personal_information" TO "service_role";



GRANT ALL ON TABLE "public"."user_roles" TO "anon";
GRANT ALL ON TABLE "public"."user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_roles" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
