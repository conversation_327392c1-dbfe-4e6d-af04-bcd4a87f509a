"use client";

import { useState } from "react";
import { format } from "date-fns";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { OrganizationListProps } from "@/types/components/organization/OrganizationListProps";
import { Restricted } from "@/components/rbac/restricted";

export function OrganizationList({ organizations }: OrganizationListProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredOrganizations = organizations.filter((org) =>
    org.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (date: string | undefined) => {
    if (!date) return "N/A";
    try {
      return format(new Date(date), "MMM d, yyyy");
    } catch {
      return "Invalid date";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex-1 max-w-sm">
          <Input
            placeholder="Search organizations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Restricted rMinRole="orgAdmin" crMinRole="orgAdmin">
          <Link href="/dashboard/developer/create-organization">
            <Button>Create Organization</Button>
          </Link>
        </Restricted>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrganizations.map((org) => (
              <TableRow key={org.id}>
                <TableCell>{org.name}</TableCell>
                <TableCell>{formatDate(org.createdAt)}</TableCell>
                <TableCell>{formatDate(org.updatedAt)}</TableCell>
                <TableCell>{org.isActive ? "Active" : "Inactive"}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Link href={`/dashboard/admin/organization/${org.id}/settings`}>
                      <Button variant="outline" size="sm">
                        Settings
                      </Button>
                    </Link>
                    <Link href={`/dashboard/admin/organization/${org.id}/invites`}>
                      <Button variant="outline" size="sm">
                        Invites
                      </Button>
                    </Link>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 