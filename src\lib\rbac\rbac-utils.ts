import { RoleId } from './roles';
import type { RbacConditions } from '@/types/lib/rbac';

// Define RoleKey here if not available from a central type, based on usage
export type RoleKey = 'superAdmin' | 'supportAdmin' | 'orgAdmin' | 'orgMember' | 'orgAccounting' | 'orgClient';

// Define UserRole here if not available from a central type, based on usage
export interface UserRole {
  orgId: string; // Current or target organization ID
  roles: Map<string, number>; // Map of orgId to roleId for the user
}

// Map role keys to role IDs
export const roleKeyToId: Record<RoleKey, number> = {
  superAdmin: RoleId.SUPERADMIN,
  supportAdmin: RoleId.SUPPORTADMIN,
  orgAdmin: RoleId.ORGADMIN,
  orgMember: RoleId.ORGMEMBER,
  orgAccounting: RoleId.ORGACCOUNTING,
  orgClient: RoleId.ORGCLIENT,
};

// Map role IDs to role keys
export const roleIdToKey: Record<number, RoleKey> = {
  [RoleId.SUPERADMIN]: 'superAdmin',
  [RoleId.SUPPORTADMIN]: 'supportAdmin',
  [RoleId.ORGADMIN]: 'orgAdmin',
  [RoleId.ORGMEMBER]: 'orgMember',
  [RoleId.ORGACCOUNTING]: 'orgAccounting',
  [RoleId.ORGCLIENT]: 'orgClient',
};

// Helper to check minimum role
export function checkMinRole(userRoleId: number, minRoleKey?: RoleKey): boolean {
  if (!minRoleKey) return true; // No restriction
  const minRoleId = roleKeyToId[minRoleKey];
  
  // Lower role ID = higher privilege level (e.g., SUPERADMIN=1, ORGMEMBER=4)
  // User should have a role ID that is <= the minimum required role ID
  return userRoleId <= minRoleId;
}

// Helper to check specific roles
export function checkRoles(userRoleId: number, roles?: RoleKey[]): boolean {
  if (!roles || roles.length === 0) return true; // No restriction
  return roles.some(role => userRoleId === roleKeyToId[role]);
}

// Parse CRUD operation from key
export function parseCrudFromKey(key: string): ('c' | 'r' | 'u' | 'd')[] {
  const operations: ('c' | 'r' | 'u' | 'd')[] = [];
  if (key.includes('c')) operations.push('c');
  if (key.includes('r')) operations.push('r');
  if (key.includes('u')) operations.push('u');
  if (key.includes('d')) operations.push('d');
  return operations;
}

// Map CRUD operations to permissions
export function mapCrudToPermissions(
  resourceType: string,
  operations: ('c' | 'r' | 'u' | 'd')[]
): string[] {
  return operations.map(op => {
    switch (op) {
      case 'c': return `${resourceType}:create`;
      case 'r': return `${resourceType}:view`;
      case 'u': return `${resourceType}:edit`;
      case 'd': return `${resourceType}:delete`;
      default: return '';
    }
  }).filter(Boolean);
}

// Evaluate RBAC permissions for a single operation
export function evaluateRbacForCrud(
  userRoleId: number,
  RbacConditions: RbacConditions,
  operation: 'c' | 'r' | 'u' | 'd' | string
): boolean {
  // Get all properties that apply to this operation
  const relevantMinRoleProps = Object.entries(RbacConditions)
    .filter(([key]) => key.endsWith('MinRole') && key.includes(operation))
    .map(([, value]) => value as RoleKey | undefined);
    
  const relevantRolesProps = Object.entries(RbacConditions)
    .filter(([key]) => key.endsWith('Roles') && key.includes(operation))
    .map(([, value]) => value as RoleKey[] | undefined);
  
  // If no restrictions defined for this operation, return false
  if (relevantMinRoleProps.length === 0 && relevantRolesProps.length === 0) {
    return false;
  }
  
  // Check if user meets any minimum role requirement
  const meetsMinRole = relevantMinRoleProps.length === 0 || 
    relevantMinRoleProps.some(minRole => checkMinRole(userRoleId, minRole));
    
  // Check if user has any specific role
  const hasSpecificRole = relevantRolesProps.length === 0 ||
    relevantRolesProps.some(roles => checkRoles(userRoleId, roles));
    
  // User must satisfy both conditions
  return meetsMinRole && hasSpecificRole;
}

// Evaluate all RBAC permissions
export function evaluateRbac(
  userRoleParam: number | UserRole, 
  RbacConditions: RbacConditions
): boolean {
  // Handle different types of userRole parameter
  let userRoleId: number;
  
  // If userRoleParam is a UserRole object
  if (typeof userRoleParam === 'object' && userRoleParam !== null) {
    const userRole = userRoleParam as UserRole;
    // Use the orgId from RbacConditions if provided, otherwise from the UserRole object.
    // This allows checking permissions for a specific org context if needed.
    const targetOrgId = RbacConditions.orgId || userRole.orgId;
    userRoleId = userRole.roles.get(targetOrgId) ?? Number.MAX_SAFE_INTEGER; // Use ?? for nullish coalescing
  } else {
    // If userRoleParam is a direct role ID (number)
    userRoleId = userRoleParam as number;
  }
  
  // Check if any RBAC props are defined
  const hasRbacConditions = Object.keys(RbacConditions).some(key => 
    (key.endsWith('MinRole') || key.endsWith('Roles')) && 
    RbacConditions[key as keyof RbacConditions] !== undefined
  );
  
  // If no RBAC props defined, don't restrict
  if (!hasRbacConditions) return true;
  
  // Navigation items special case - if only rMinRole is defined
  if ('rMinRole' in RbacConditions && Object.keys(RbacConditions).filter(k => k !== 'orgId').length === 1) {
    const minRoleKey = RbacConditions.rMinRole as RoleKey; // rMinRole is known to be RoleKey from type
    return checkMinRole(userRoleId, minRoleKey);
  }
  
  // Similar check for rRoles
  if ('rRoles' in RbacConditions && Object.keys(RbacConditions).filter(k => k !== 'orgId').length === 1) {
    const roles = RbacConditions.rRoles as RoleKey[]; // rRoles is known to be RoleKey[]
    return checkRoles(userRoleId, roles);
  }
  
  // For all other RBAC checks, go through each supported operation type
  
  // Read operation checks
  if (RbacConditions.rMinRole) {
    if (!checkMinRole(userRoleId, RbacConditions.rMinRole)) return false;
  }
  
  // Combined operation checks
  if (RbacConditions.crMinRole) {
    if (!checkMinRole(userRoleId, RbacConditions.crMinRole)) return false;
  }
  if (RbacConditions.ruMinRole) {
    if (!checkMinRole(userRoleId, RbacConditions.ruMinRole)) return false;
  }
  if (RbacConditions.rdMinRole) {
    if (!checkMinRole(userRoleId, RbacConditions.rdMinRole)) return false;
  }
  
  // Triple operation checks
  if (RbacConditions.cruMinRole) {
    if (!checkMinRole(userRoleId, RbacConditions.cruMinRole)) return false;
  }
  // Properties 'crdMinRole' and 'rudMinRole' do not exist on type 'RbacConditions'.
  // Removing these checks. If C+R+D or R+U+D specific logic is needed, 
  // RbacConditions type and this logic needs to be updated.
  
  // Full CRUD check
  if (RbacConditions.crudMinRole) {
    if (!checkMinRole(userRoleId, RbacConditions.crudMinRole)) return false;
  }
  
  // Role specific checks
  if (RbacConditions.rRoles && !checkRoles(userRoleId, RbacConditions.rRoles as RoleKey[])) return false;
  
  // Combined roles
  if (RbacConditions.crRoles && !checkRoles(userRoleId, RbacConditions.crRoles as RoleKey[])) return false;
  if (RbacConditions.ruRoles && !checkRoles(userRoleId, RbacConditions.ruRoles as RoleKey[])) return false;
  if (RbacConditions.rdRoles && !checkRoles(userRoleId, RbacConditions.rdRoles as RoleKey[])) return false;
  if (RbacConditions.cruRoles && !checkRoles(userRoleId, RbacConditions.cruRoles as RoleKey[])) return false;
  // Property 'crdRoles' and 'rudRoles' do not exist on type 'RbacConditions'.
  // Removing these checks.
  if (RbacConditions.crudRoles && !checkRoles(userRoleId, RbacConditions.crudRoles as RoleKey[])) return false;
  
  // If we've made it this far, all checks passed
  return true;
} 