# Database Functions and Triggers Documentation

This document provides an overview of all functions and triggers in the Supabase project (slippy_rhyno - jdhltybpmwuijrxqhzjj).

## Custom Functions

### Organization and User Management

#### `is_superadmin(user_uid uuid) → boolean`
- **Description**: Checks if a user is a superadmin.
- **Usage**: Used for access control to verify if a user has superadmin privileges.
- **Returns**: `true` if the user is a superadmin, `false` otherwise.
- **Tables Used**: `organization_members`, `roles`
- **Used In**: RLS policies for tables requiring superadmin checks

#### `is_superadmin_v2() → boolean`
- **Description**: Checks if the current user is a superadmin.
- **Usage**: Used for RLS policies and server-side access control.
- **Returns**: `true` if the current user is a superadmin, `false` otherwise.
- **Tables Used**: `auth.users` (checks raw_app_meta_data)
- **Used In**: RLS policies for organization management and profile access

#### `is_superadmin_check(check_user_id uuid) → boolean`
- **Description**: Validates if a specified user has superadmin privileges.
- **Usage**: Used for checking superadmin status of any user.
- **Returns**: `true` if the specified user is a superadmin, `false` otherwise.
- **Tables Used**: `organization_members`, `roles`
- **Used In**: User permissions verification

#### `is_org_member(user_uid uuid, organization_id uuid) → boolean`
- **Description**: Checks if a user is a member of a specific organization.
- **Usage**: Used for access control in organization-specific operations.
- **Returns**: `true` if the user is a member of the organization, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: Access control for organization-specific data

#### `is_org_member(org_id_param uuid) → boolean`
- **Description**: Checks if the current user is a member of a specific organization.
- **Usage**: Used in RLS policies for organization-based access control.
- **Returns**: `true` if the current user is a member of the organization, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: RLS policies on `organization_invites` and other organization-related tables

#### `is_org_admin(org_id_param uuid) → boolean`
- **Description**: Checks if the current user is an admin of a specific organization.
- **Usage**: Used for admin-level access controls within an organization.
- **Returns**: `true` if the current user is an organization admin, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: Administrative operations for organizations

#### `check_org_admin_access(check_org_id uuid) → boolean`
- **Description**: Validates if the current user has admin access to a specific organization.
- **Usage**: Used to verify admin permissions before allowing administrative operations.
- **Returns**: `true` if the user has admin access to the organization, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: Organization administration features

#### `has_organization_role(p_user_id uuid, p_org_id uuid, p_role_id integer) → boolean`
- **Description**: Checks if a user has a specific role in an organization.
- **Usage**: Used for role-based access control within organizations.
- **Returns**: `true` if the user has the specified role, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: Role-based authentication and authorization

#### `has_user_role(role_name text) → boolean`
- **Description**: Checks if the current user has a specific role.
- **Usage**: Used for role-based access control throughout the application.
- **Returns**: `true` if the user has the role, `false` otherwise.
- **Tables Used**: Uses `auth.jwt()` to check role
- **Used In**: RLS policies and authorization checks

#### `get_user_org_role(p_org_id uuid) → integer`
- **Description**: Gets the role ID of the current user in a specific organization.
- **Usage**: Used to determine permission levels for the current user.
- **Returns**: The role ID (integer) of the user in the organization.
- **Tables Used**: `organization_members`
- **Used In**: Permission-based UI rendering and access control

#### `get_role_name(role_id integer) → text`
- **Description**: Gets the name of a role based on its ID.
- **Usage**: Used for displaying role names in the UI.
- **Returns**: The name of the role.
- **Tables Used**: `roles`
- **Used In**: UI components displaying role information

#### `is_in_organization(org_id_param uuid) → boolean`
- **Description**: Checks if the current user is in a specific organization.
- **Usage**: Used for basic organization membership verification.
- **Returns**: `true` if the user is in the organization, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: Organization-specific access control

#### `has_any_organizations() → boolean`
- **Description**: Checks if the current user belongs to any organization.
- **Usage**: Used to determine if a user should be directed to create an organization.
- **Returns**: `true` if the user belongs to at least one organization, `false` otherwise.
- **Tables Used**: `organizations`
- **Used In**: Initial user onboarding flow

#### `current_user_has_organizations() → boolean`
- **Description**: Alternative function to check if the current user belongs to any organization.
- **Usage**: Used in UI logic to determine user flow.
- **Returns**: `true` if the user belongs to at least one organization, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: User navigation and UI state management

#### `user_has_organizations(user_id_param uuid) → boolean`
- **Description**: Checks if a specific user belongs to any organization.
- **Usage**: Used to verify organization membership for any user.
- **Returns**: `true` if the user belongs to at least one organization, `false` otherwise.
- **Tables Used**: `organization_members`
- **Used In**: Admin operations on user accounts

#### `get_user_organization_ids(check_user_id uuid) → SETOF uuid`
- **Description**: Gets all organization IDs that a user belongs to.
- **Usage**: Used to list all organizations for a user.
- **Returns**: A set of organization UUIDs.
- **Tables Used**: `organization_members`
- **Used In**: Organization selection UI and multi-org features

#### `get_user_active_organization(p_user_id uuid) → uuid`
- **Description**: Gets the active organization for a user.
- **Usage**: Used to determine which organization context a user is currently in.
- **Returns**: The UUID of the user's current active organization.
- **Tables Used**: `organization_members`
- **Used In**: Context-switching between organizations

### Invitation Management

#### ~~`generate_invite_code() → text`~~ **REMOVED**
- **Status**: Removed with old invitation system

#### ~~`has_valid_invite(org_id_param uuid) → boolean`~~ **REMOVED**
- **Status**: Removed with old invitation system

#### ~~`process_invite_code(p_invite_code text) → jsonb`~~ **REMOVED**
- **Status**: Removed with old invitation system

## Triggers

### Organization and User Management Triggers


#### `handle_new_user()`
- **Required**: Yes
- **Description**: Triggered after a new user is created in the auth schema.
- **Usage**: Creates a profile record for the new user.
- **Trigger**: `ON INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION handle_new_user()`
- **Tables Used**: `auth.users`, `profiles`

#### `add_superusers_to_org()`
- **Required**: Yes
- **Description**: Triggered after a new organization is created.
- **Usage**: Automatically adds superadmin users to the newly created organization.
- **Trigger**: `AFTER INSERT ON organizations FOR EACH ROW EXECUTE FUNCTION add_superusers_to_org()`
- **Tables Used**: `organizations`, `superusers`, `organization_members`

#### `handle_updated_at()`
- **Required**: Yes
- **Description**: Updates the `updated_at` timestamp before updating various tables.
- **Usage**: Keeps track of when records were last modified.
- **Trigger**: Applied to multiple tables: `labelcategories`, `labels`, `user_personal_information`
- **Tables Used**: `labelcategories`, `labels`, `user_personal_information`

#### `update_updated_at_column()`
- **Required**: No > Duplicate of handle_updated_at()
- **Description**: Updates the `updated_at` timestamp before updating records.
- **Usage**: Keeps track of when records were last modified.
- **Trigger**: Applied to `flows` and `steps` tables
- **Tables Used**: `flows`, `steps`

#### ~~`set_invite_code()`~~ **REMOVED**
- **Status**: Removed with old invitation system

#### `set_user_full_name()`
- **Required**: Yes
- **Description**: Sets the user's full name in the organization_members table.
- **Usage**: Keeps the user's name in sync with the profiles table.
- **Trigger**: `BEFORE INSERT ON organization_members FOR EACH ROW EXECUTE FUNCTION set_user_full_name()`
- **Tables Used**: `organization_members`, `profiles`

#### `update_user_full_name()`
- **Description**: Updates user names in the organization_members table when profile names change.
- **Usage**: Keeps denormalized data in sync.
- **Trigger**: `AFTER UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_user_full_name()`
- **Tables Used**: `profiles`, `organization_members`

#### `set_org_name()`
- **Required**: Yes
- **Description**: Sets the organization name in the organization_members table.
- **Usage**: Keeps the organization name in sync with the organizations table.
- **Trigger**: `BEFORE INSERT ON organization_members FOR EACH ROW EXECUTE FUNCTION set_org_name()`
- **Tables Used**: `organization_members`, `organizations`

#### `update_org_name()`
- **Required**: Yes
- **Description**: Updates organization names in the organization_members table when the organization name changes.
- **Usage**: Keeps denormalized data in sync.
- **Trigger**: `AFTER UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_org_name()`
- **Tables Used**: `organizations`, `organization_members`

#### `log_policy_check()`
- **Required**: Unknown
- **Description**: Logs policy checks before inserting into organization_members.
- **Usage**: Used for debugging RLS policies.
- **Trigger**: `BEFORE INSERT ON organization_members FOR EACH ROW EXECUTE FUNCTION log_policy_check()`
- **Tables Used**: `organization_members`

#### `refresh_user_roles()`
- **Required**: Unknown
- **Description**: Refreshes user role information after changes to organization_members.
- **Usage**: Ensures role data is current after membership changes.
- **Trigger**: `AFTER INSERT OR UPDATE OR DELETE ON organization_members FOR EACH ROW EXECUTE FUNCTION refresh_user_roles()`
- **Tables Used**: `organization_members`, `user_roles` (materialized view)

#### `set_current_organization_context()`
- **Required**: Yes
- **Description**: Updates the current organization context when a member is deleted.
- **Usage**: Ensures users have a valid context after membership changes.
- **Trigger**: `AFTER DELETE ON organization_members FOR EACH ROW EXECUTE FUNCTION set_current_organization_context()`
- **Tables Used**: `organization_members`

## Recent Migrations

### `20250430090556_add_is_current_context_to_organization_members`
- **Description**: Added the `is_current_context` boolean column to organization_members table.
- **Purpose**: Tracks which organization is currently active for each user.
- **Tables Modified**: `organization_members`

### `20250430090627_initialize_is_current_context_data`
- **Description**: Initialized the is_current_context data for existing organization members.
- **Purpose**: Set up the initial state for the new feature tracking current organization contexts.
- **Tables Modified**: `organization_members`

## Row-Level Security (RLS) Policies

The database uses Row-Level Security extensively to control access to data. Here are the key policy patterns:

### Organizations Table
- **Read access**:
  - All users can view organizations they belong to
  - Organization admins can view their organizations
  - Superadmins can view all organizations
- **Write access**:
  - Organization admins can update their own organizations
  - Superadmins can update any organization
- **Delete access**: Only superadmins can delete organizations

### Profiles Table
- **Read access**:
  - Users can view their own profiles
  - Organization admins can view profiles of members in their organizations
  - Superusers can view all profiles
- **Write access**:
  - Users can update their own profiles
  - Superadmins can update any profile

### Organization Invites Table
- **Read access**:
  - Anyone can view invites by code
  - Organization members can view invites for their organizations
- **Write access**: Organization admins can create and update invites
- **Full access**: Superadmins have full access to all invites

### Personal Information Table
- **Read access**:
  - Users can view their own information
  - Organization admins can view information of members in their organizations
  - Organization accounting role can view select information
- **Write access**: Users can update their own information

## Schema Overview

The database schema is designed around organizations, users, and role-based access control:

1. **Organizations**: The core entity that represents a company or team.
2. **Users**: Represented by profiles and auth.users tables.
3. **Membership**: The organization_members table defines the relationship between users and organizations.
4. **Roles**: Defines permission levels within organizations (superadmin, admin, member, etc.).
5. **Invitations**: Allows adding new members to organizations via invite codes.

Additionally, the system includes support for:
- Workflow management (flows, steps)
- Categorization (labelcategories, labels, flowstepcategories)
- Personal information storage (user_personal_information)
