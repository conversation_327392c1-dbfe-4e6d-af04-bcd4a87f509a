'use client';

import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { isDashboardBlocked, onRenderStateChange, clearDashboardRenderBlock } from '@/lib/dashboard-render-control';

interface DashboardRenderGateProps {
  children: React.ReactNode;
}

/**
 * DashboardRenderGate prevents dashboard content from rendering during tab reactivation
 * until route permission evaluation completes. Uses secure module-level state instead
 * of client-side sessionStorage.
 */
export function DashboardRenderGate({ children }: DashboardRenderGateProps) {
  const [isRenderBlocked, setIsRenderBlocked] = useState(() => isDashboardBlocked());

  useEffect(() => {
    // Subscribe to render state changes
    const unsubscribe = onRenderStateChange(() => {
      setIsRenderBlocked(isDashboardBlocked());
    });

    // Cleanup subscription on unmount
    return unsubscribe;
  }, []);

  // Emergency safeguard: Clear block on page load if it exists
  useEffect(() => {
    // This only runs once on component mount (page load/refresh)
    // If there's a block active, it means something went wrong previously
    if (isDashboardBlocked()) {
      console.log('[DashboardRenderGate] Clearing render block on page load - likely from previous session');
      clearDashboardRenderBlock();
    }
  }, []); // Empty dependency array = runs once on mount

  // Show loading skeleton while render is blocked
  if (isRenderBlocked) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Verifying permissions...
          </p>
        </div>
      </div>
    );
  }

  // Render normal content when not blocked
  return <>{children}</>;
}
