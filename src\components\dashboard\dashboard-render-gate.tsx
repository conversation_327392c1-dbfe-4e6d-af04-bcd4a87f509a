'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Loader2 } from 'lucide-react';

interface DashboardRenderGateProps {
  children: React.ReactNode;
}

/**
 * DashboardRenderGate prevents dashboard content from rendering during tab reactivation
 * until route permission evaluation completes. This eliminates timing issues where
 * components render with stale permissions before route evaluation can redirect unauthorized users.
 */
export function DashboardRenderGate({ children }: DashboardRenderGateProps) {
  const [isRenderBlocked, setIsRenderBlocked] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // SAFEGUARD 1: Clear any stale render block flag on component mount (page load/refresh)
    const hasStaleFlag = !!sessionStorage.getItem('dashboardRenderBlocked');
    if (hasStaleFlag) {
      console.log('[DashboardRenderGate] Clearing stale render block flag on page load');
      sessionStorage.removeItem('dashboardRenderBlocked');
    }

    const checkRenderBlock = () => {
      const isBlocked = !!sessionStorage.getItem('dashboardRenderBlocked');
      setIsRenderBlocked(isBlocked);

      // SAFEGUARD 2: Set timeout to automatically clear flag if it gets stuck
      if (isBlocked && !timeoutRef.current) {
        console.log('[DashboardRenderGate] Setting 5-second timeout safeguard for render block');
        timeoutRef.current = setTimeout(() => {
          console.log('[DashboardRenderGate] SAFEGUARD: Automatically clearing stuck render block flag after 5 seconds');
          sessionStorage.removeItem('dashboardRenderBlocked');
          setIsRenderBlocked(false);
          timeoutRef.current = null;
        }, 5000); // 5 second timeout
      } else if (!isBlocked && timeoutRef.current) {
        // Clear timeout if flag was cleared normally
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };

    // Check immediately
    checkRenderBlock();

    // Poll for changes since sessionStorage doesn't have events
    const interval = setInterval(checkRenderBlock, 50); // Check every 50ms for responsiveness

    return () => {
      clearInterval(interval);
      // SAFEGUARD 3: Clear timeout on component unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  // Show loading skeleton while render is blocked
  if (isRenderBlocked) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Verifying permissions...
          </p>
        </div>
      </div>
    );
  }

  // Render normal content when not blocked
  return <>{children}</>;
}
