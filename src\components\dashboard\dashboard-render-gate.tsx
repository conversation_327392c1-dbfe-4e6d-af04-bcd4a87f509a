'use client';

import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

interface DashboardRenderGateProps {
  children: React.ReactNode;
}

/**
 * DashboardRenderGate prevents dashboard content from rendering during tab reactivation
 * until route permission evaluation completes. This eliminates timing issues where
 * components render with stale permissions before route evaluation can redirect unauthorized users.
 */
export function DashboardRenderGate({ children }: DashboardRenderGateProps) {
  const [isRenderBlocked, setIsRenderBlocked] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkRenderBlock = () => {
      const isBlocked = !!sessionStorage.getItem('dashboardRenderBlocked');
      setIsRenderBlocked(isBlocked);
    };

    // Check immediately
    checkRenderBlock();

    // Poll for changes since sessionStorage doesn't have events
    const interval = setInterval(checkRenderBlock, 50); // Check every 50ms for responsiveness

    return () => clearInterval(interval);
  }, []);

  // Show loading skeleton while render is blocked
  if (isRenderBlocked) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Verifying permissions...
          </p>
        </div>
      </div>
    );
  }

  // Render normal content when not blocked
  return <>{children}</>;
}
