import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { evaluateRbac, <PERSON><PERSON><PERSON> } from '@/lib/rbac/rbac-utils'
import { RoleId } from '@/lib/rbac/roles'; // Import RoleId for mapping
import type { OrganizationMemberFull } from '@/types/organization/'
import { getCurrentUserActiveOrganization } from '@/lib/organization-utils-server'

// Local interface for the shape of data from Supabase query
interface OrganizationMemberDB {
  org_id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  org_member_role: number;
  org_member_is_active: boolean;
  is_default_org: boolean;
  org_member_updated_by: string | null;
  organizations?: { // Optional, in case the join doesn't find a match (though direct SQL used LEFT)
    org_name: string;
    org_icon: string | null;
    is_active: boolean;
  };
  roles?: { // Optional
    role_name: string;
  };
  profiles?: { // Optional
    full_name: string;
    avatar_url: string | null;
    email?: string; // Ensure email is here for the new select
  };
}

// Mapping from RoleKey (camelCase string) to RoleId (number)
const roleKeyToIdMap: { [key in RoleKey]?: number } = {
  superAdmin: RoleId.SUPERADMIN,
  supportAdmin: RoleId.SUPPORTADMIN,
  orgAdmin: RoleId.ORGADMIN,
  orgMember: RoleId.ORGMEMBER,
  orgAccounting: RoleId.ORGACCOUNTING,
  orgClient: RoleId.ORGCLIENT,
};

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const orgIdQueryParam = searchParams.get('orgId')
    const page = parseInt(searchParams.get('page') || '1', 10)
    const pageSize = parseInt(searchParams.get('pageSize') || '50', 10)
    const search = searchParams.get('search')
    const roleKeysParams = searchParams.getAll('roleKeys[]') as RoleKey[]
    const offset = (page - 1) * pageSize

    // DEBUGGING - Role keys and IDs (before length check)
    console.log("DEBUGGING - Role keys and IDs (before length check):", { 
      roleKeysParams, 
      roleKeyToIdMap
    });

    const { data: userRolesData, error: rolesFetchError } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role')
      .eq('user_id', user.id)
      .eq('org_member_is_active', true)

    if (rolesFetchError) {
      return NextResponse.json({ error: 'Failed to verify access' }, { status: 500 })
    }
    const orgRolesMap = new Map(userRolesData.map(role => [role.org_id, role.org_member_role]))
    const isCurrentUserSuperAdminGlobally = Array.from(orgRolesMap.values()).some(roleId => evaluateRbac(roleId, {rRoles: ["superAdmin"]}));

    let effectiveOrgIdForQuery: string | null = null;
    let fetchAllOrgsForSuperAdmin = false;

    if (orgIdQueryParam && orgIdQueryParam !== 'all') {
      effectiveOrgIdForQuery = orgIdQueryParam;
      if (!isCurrentUserSuperAdminGlobally && !orgRolesMap.has(effectiveOrgIdForQuery)) {
        return NextResponse.json({ error: 'Forbidden: Access to specified organization denied.' }, { status: 403 });
      }
    } else if (orgIdQueryParam === 'all' && isCurrentUserSuperAdminGlobally) {
      fetchAllOrgsForSuperAdmin = true;
    } else if (!orgIdQueryParam && !isCurrentUserSuperAdminGlobally) {
      const { organization: activeOrg } = await getCurrentUserActiveOrganization(user.id);
      if (activeOrg) {
        effectiveOrgIdForQuery = activeOrg.id;
      } else {
        return NextResponse.json({ members: [], totalCount: 0 });
      }
    } else if (!orgIdQueryParam && isCurrentUserSuperAdminGlobally) {
        fetchAllOrgsForSuperAdmin = true;
    } else {
        const { organization: activeOrg } = await getCurrentUserActiveOrganization(user.id);
        if (activeOrg) effectiveOrgIdForQuery = activeOrg.id;
        else return NextResponse.json({ members: [], totalCount: 0 });
    }

    // Use INNER JOIN on profiles when searching, else LEFT JOIN
    const selectString = search
      ? `
      org_id,
      user_id,
      created_at,
      updated_at,
      org_member_role,
      org_member_is_active,
      is_default_org,
      org_member_updated_by,
      profiles!organization_members_user_id_profiles_id_fk!inner (
        full_name,
        avatar_url,
        email
      ),
      organizations (
        org_name,
        org_icon,
        is_active
      ),
      roles (
        role_name
      )
    `
      : `
      org_id,
      user_id,
      created_at,
      updated_at,
      org_member_role,
      org_member_is_active,
      is_default_org,
      org_member_updated_by,
      profiles!organization_members_user_id_profiles_id_fk (
        full_name,
        avatar_url,
        email
      ),
      organizations (
        org_name,
        org_icon,
        is_active
      ),
      roles (
        role_name
      )
    `;

    let query = supabase
      .from('organization_members')
      .select(selectString, { count: 'exact' });

    // Apply filters
    if (effectiveOrgIdForQuery) {
      query = query.eq('org_id', effectiveOrgIdForQuery);
    } else if (!fetchAllOrgsForSuperAdmin) {
      const userMemberOrgIds = Array.from(orgRolesMap.keys());
      if (userMemberOrgIds.length > 0) {
        query = query.in('org_id', userMemberOrgIds);
      } else {
        return NextResponse.json({ members: [], totalCount: 0 }); // No accessible orgs
      }
    }

    if (search) {
      query = query.ilike('profiles.full_name', `%${search}%`);
    }

    if (roleKeysParams.length > 0) {
      const roleIdValues = roleKeysParams.map(key => roleKeyToIdMap[key]).filter(id => id !== undefined) as number[];
      if (roleIdValues.length > 0) {
        query = query.in('org_member_role', roleIdValues);
      }
    }
    
    // Apply ordering and pagination
    query = query.order('created_at', { ascending: false })
                 .range(offset, offset + pageSize - 1);

    const { data: members, count, error: queryError } = await query;

    if (queryError) {
      console.error('Error fetching members or count:', queryError);
      return NextResponse.json({ error: 'Failed to fetch members' }, { status: 500 });
    }

    console.log("DEBUGGING - Raw members data from DB:", JSON.stringify(members, null, 2));

    const dbMembers = members as unknown as OrganizationMemberDB[] || [];
    const transformedMembers: OrganizationMemberFull[] = dbMembers.map(member => {
        console.log("DEBUGGING - Processing member:", {
            id: member.user_id,
            org_id: member.org_id,
            org_member_role: member.org_member_role,
            user_full_name: member.profiles?.full_name,
            org_name: member.organizations?.org_name,
            org_member_is_active: member.org_member_is_active,
            organizations: member.organizations,
            profiles: member.profiles,
            roles: member.roles
        });
      
        return {
            user_id: member.user_id,
            org_id: member.org_id,
            created_at: member.created_at,
            updated_at: member.updated_at,
            org_member_role: member.org_member_role,
            org_member_is_active: member.org_member_is_active,
            is_default_org: member.is_default_org,
            org_member_updated_by: member.org_member_updated_by,
            organization: { 
                id: member.org_id,
                org_name: member.organizations?.org_name || 'Unknown',
                org_icon: member.organizations?.org_icon || null,
                is_active: member.organizations?.is_active ?? false,
            },
            role_name: member.roles?.role_name || 'Unknown',
            user_full_name: member.profiles?.full_name || 'N/A',
            avatar_url: member.profiles?.avatar_url || null,
            user_email: member.profiles?.email || '',
            updated_by_name: null, 
            personal_info: null, 
        }
    });

    console.log("DEBUGGING - Transformed members:", JSON.stringify(transformedMembers, null, 2));
    
    return NextResponse.json({
      members: transformedMembers,
      totalCount: count ?? 0
    });
  } catch (error) {
    console.error('Error in GET /api/organization-members:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}