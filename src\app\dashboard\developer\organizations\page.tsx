import { OrganizationTable } from "./organization-table";
import { createClient } from "@/lib/supabase/server";
import { withRbacPermission } from "@/lib/rbac/permissions-server";
import { getUserOrganizations, PaginatedOrganizationsResponse } from "@/lib/organization-utils-server";
import { Organization } from "@/types/organization";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { PageProps } from "@/types/app/PageProps";

// This page is for SuperAdmins and SupportAdmins to view all organizations.
const DeveloperOrganizationsPage = async (_props: PageProps) => {
  const supabase = await createClient();
  // Get user - withRbacPermission already ensures user is authenticated and authorized
  const { data: { user } } = await supabase.auth.getUser();

  let organizations: Organization[] = [];
  let fetchError: string | null = null;

  try {
    const paginatedResult: PaginatedOrganizationsResponse = await getUserOrganizations(user!.id, true);
    if (paginatedResult && paginatedResult.organizations) {
      organizations = paginatedResult.organizations;
    } else {
      organizations = [];
      if (paginatedResult) {
         console.warn("Fetched organization data but 'organizations' array is missing or null.", paginatedResult);
      }
    }
  } catch (error) {
    console.error("Error fetching organizations for developer page:", error);
    fetchError = error instanceof Error ? error.message : "An unknown error occurred while fetching organizations.";
  }

  if (fetchError) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Error Fetching Organizations</AlertTitle>
          <AlertDescription>{fetchError}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">All System Organizations</h1>
      <OrganizationTable initialOrganizations={organizations} />
    </div>
  );
};

export default withRbacPermission(
  { rRoles: ["superAdmin", "supportAdmin"] },
  { redirectTo: "/dashboard" }
)(DeveloperOrganizationsPage);