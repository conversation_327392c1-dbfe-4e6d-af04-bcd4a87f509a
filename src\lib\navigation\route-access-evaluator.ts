import { navigationData } from '@/data/navigation';
import { evaluateRbac } from '@/lib/rbac/rbac-utils';
import { evaluateAccountDisabledPageAccess, evaluateOrganizationDisabledPageAccess } from '@/lib/permission-utils';
import type { RbacConditions } from '@/types/lib/rbac';

/**
 * Route Access Evaluator
 *
 * This module evaluates route access permissions using the navigation data structure.
 * It derives all route classifications from the existing navigation sections:
 * - Main Section: Organization-scoped routes
 * - Admin Section: Organization-scoped routes (with orgAdmin+ permissions)
 * - Developer Section: Global routes (with superAdmin/supportAdmin permissions)
 *
 * No hardcoded route lists - everything is derived from navigation data!
 */

/**
 * Result of route access evaluation
 */
export interface RouteAccessResult {
  /** Whether the user can access the route */
  canAccess: boolean;
  /** The RBAC conditions that apply to this route */
  rbacConditions: RbacConditions | undefined;
  /** Whether this route requires organization-scoped data */
  requiresOrgData: boolean;
  /** The data scope type for this route */
  dataScope: 'organization' | 'user' | 'global';
  /** Reason for access denial (if applicable) */
  reason: string | undefined;
}

/**
 * Context for evaluating route access
 */
export interface RouteAccessContext {
  /** Current route path */
  currentPath: string;
  /** User's role ID in the target organization */
  userRoleId: number;
  /** Whether the user is active in the target organization */
  isUserActiveInOrg: boolean;
  /** Whether the target organization is active */
  isOrgActive: boolean;
  /** Whether the user is a super admin */
  isSuperAdmin?: boolean;
}

/**
 * Unified access evaluation result with redirect priority
 */
export interface UnifiedAccessResult {
  /** Whether the user can access the current route */
  canAccess: boolean;
  /** Where the user should be redirected to */
  redirectTo: string;
  /** Priority level of the redirect reason */
  priority: 'account-disabled' | 'organization-disabled' | 'route-permission' | 'allowed';
  /** Human-readable reason for the decision */
  reason: string;
}

// No hardcoded routes - derive everything from navigation data structure

/**
 * Extract RBAC conditions for a specific route from navigation data
 */
function extractRouteRbacConditions(routePath: string): RbacConditions | undefined {
  // Check each navigation section
  for (const section of navigationData) {
    // Check section-level RBAC conditions first
    const sectionRbac = section.RbacConditions;

    // Check each item in the section
    for (const item of section.items) {
      if (item.href === routePath) {
        // Item-specific RBAC takes precedence over section RBAC
        const result = item.RbacConditions || sectionRbac;
        console.log('[RouteAccessEvaluator] Found route:', routePath, 'RBAC:', result, 'Section:', section.title);
        return result;
      }
    }
  }

  console.log('[RouteAccessEvaluator] No RBAC conditions found for route:', routePath);
  return undefined;
}

/**
 * Determine the data scope type for a route based on navigation data structure
 */
function getRouteDataScope(routePath: string): 'organization' | 'user' | 'global' {
  // Check each navigation section to find the route
  for (const section of navigationData) {
    for (const item of section.items) {
      if (item.href === routePath) {
        // Determine scope based on section
        switch (section.title) {
          case 'Developer':
            return 'global'; // Developer section is global
          case 'Main':
          case 'Admin':
          default:
            return 'organization'; // Main and Admin sections are organization-scoped
        }
      }
    }
  }

  // For routes not in navigation data, use path-based heuristics
  if (routePath.startsWith('/dashboard/developer')) {
    return 'global';
  }

  if (routePath.startsWith('/dashboard/my-') || routePath.startsWith('/dashboard/create-')) {
    return 'user'; // User-specific routes
  }

  // Default to organization scope for dashboard routes
  if (routePath.startsWith('/dashboard')) {
    return 'organization';
  }

  return 'global';
}

/**
 * Check if a route requires organization-scoped data refresh
 */
function requiresOrganizationData(routePath: string): boolean {
  return getRouteDataScope(routePath) === 'organization';
}

/**
 * Evaluate whether a user can access a specific route
 */
export function evaluateRouteAccess(context: RouteAccessContext): RouteAccessResult {
  const { currentPath, userRoleId, isUserActiveInOrg, isOrgActive, isSuperAdmin = false } = context;

  console.log('[RouteAccessEvaluator] Evaluating access for:', {
    currentPath,
    userRoleId,
    isUserActiveInOrg,
    isOrgActive,
    isSuperAdmin
  });

  // Extract RBAC conditions for the route
  const rbacConditions = extractRouteRbacConditions(currentPath);
  const dataScope = getRouteDataScope(currentPath);
  const requiresOrgData = requiresOrganizationData(currentPath);
  
  // Special handling for organization/user status
  if (!isOrgActive && !isSuperAdmin) {
    return {
      canAccess: false,
      rbacConditions,
      requiresOrgData,
      dataScope,
      reason: 'Organization is inactive and user is not super admin'
    };
  }
  
  if (isOrgActive && !isUserActiveInOrg) {
    return {
      canAccess: false,
      rbacConditions,
      requiresOrgData,
      dataScope,
      reason: 'User is inactive in the organization'
    };
  }
  
  // If no RBAC conditions are defined, route is accessible
  if (!rbacConditions) {
    return {
      canAccess: true,
      rbacConditions,
      requiresOrgData,
      dataScope,
      reason: undefined
    };
  }
  
  // Evaluate RBAC conditions
  const hasAccess = evaluateRbac(userRoleId, rbacConditions);

  console.log('[RouteAccessEvaluator] RBAC evaluation result:', {
    userRoleId,
    rbacConditions,
    hasAccess
  });

  return {
    canAccess: hasAccess,
    rbacConditions,
    requiresOrgData,
    dataScope,
    reason: hasAccess ? undefined : 'Insufficient role permissions'
  };
}

/**
 * Get the default redirect path for a user based on their role and organization status
 */
export function getDefaultRedirectPath(context: Omit<RouteAccessContext, 'currentPath'>): string {
  const { userRoleId, isUserActiveInOrg, isOrgActive, isSuperAdmin = false } = context;
  
  // Handle inactive states
  if (!isOrgActive && !isSuperAdmin) {
    return '/dashboard/organization-disabled';
  }
  
  if (isOrgActive && !isUserActiveInOrg) {
    return '/dashboard/account-disabled';
  }
  
  // Check if user can access developer section
  const canAccessDeveloper = evaluateRbac(userRoleId, { rRoles: ['superAdmin', 'supportAdmin'] });
  if (canAccessDeveloper) {
    return '/dashboard/developer/organizations';
  }
  
  // Check if user can access admin section
  const canAccessAdmin = evaluateRbac(userRoleId, { rMinRole: 'orgAdmin' });
  if (canAccessAdmin) {
    return '/dashboard/admin/members';
  }
  
  // Default to main dashboard
  return '/dashboard';
}

/**
 * Unified access evaluation that handles all scenarios in priority order
 *
 * Evaluation order:
 * 1. Account disabled? → /dashboard/account-disabled
 * 2. Organization disabled? → /dashboard/organization-disabled
 * 3. Route permissions? → /dashboard (or appropriate default)
 * 4. Recovery: On disabled page but status restored? → /dashboard
 * 5. Otherwise → Stay on current page
 */
export function evaluateUnifiedAccess(context: RouteAccessContext): UnifiedAccessResult {
  const { currentPath, userRoleId, isUserActiveInOrg, isOrgActive, isSuperAdmin = false } = context;

  console.log('[RouteAccessEvaluator] Unified access evaluation for:', {
    currentPath,
    userRoleId,
    isUserActiveInOrg,
    isOrgActive,
    isSuperAdmin
  });

  // Step 1: Account disabled (in current organization)
  // This takes precedence over organization disabled to prevent conflicts
  if (isOrgActive && !isUserActiveInOrg) {
    // Allow access only to account-disabled page
    if (currentPath === '/dashboard/account-disabled') {
      return {
        canAccess: true,
        redirectTo: '/dashboard/account-disabled',
        priority: 'account-disabled',
        reason: 'User is inactive in organization and on correct disabled page'
      };
    }

    return {
      canAccess: false,
      redirectTo: '/dashboard/account-disabled',
      priority: 'account-disabled',
      reason: 'User is inactive in the current organization'
    };
  }

  // Step 2: Organization disabled
  // Only applies if user is active but organization is disabled
  if (!isOrgActive && !isSuperAdmin) {
    // Allow access only to organization-disabled page
    if (currentPath === '/dashboard/organization-disabled') {
      return {
        canAccess: true,
        redirectTo: '/dashboard/organization-disabled',
        priority: 'organization-disabled',
        reason: 'Organization is inactive and user is on correct disabled page'
      };
    }

    return {
      canAccess: false,
      redirectTo: '/dashboard/organization-disabled',
      priority: 'organization-disabled',
      reason: 'Organization is inactive and user is not super admin'
    };
  }

  // Step 3: Route-specific permissions
  // User is active and organization is active, check route permissions
  const routeAccess = evaluateRouteAccess(context);

  if (!routeAccess.canAccess) {
    // User doesn't have access to current route, redirect to appropriate default
    const defaultPath = getDefaultRedirectPath({
      userRoleId,
      isUserActiveInOrg,
      isOrgActive,
      isSuperAdmin
    });

    return {
      canAccess: false,
      redirectTo: defaultPath,
      priority: 'route-permission',
      reason: routeAccess.reason || 'Insufficient permissions for current route'
    };
  }

  // Step 4: Recovery rules - handle transitions away from disabled pages using business logic
  if (currentPath === '/dashboard/account-disabled') {
    return evaluateAccountDisabledPageAccess(userRoleId, isUserActiveInOrg, isOrgActive, isSuperAdmin);
  }

  if (currentPath === '/dashboard/organization-disabled') {
    return evaluateOrganizationDisabledPageAccess(userRoleId, isUserActiveInOrg, isOrgActive, isSuperAdmin);
  }

  // Step 5: User has access and is on appropriate page
  return {
    canAccess: true,
    redirectTo: currentPath, // Stay on current page
    priority: 'allowed',
    reason: 'User has access to the current route'
  };
}
