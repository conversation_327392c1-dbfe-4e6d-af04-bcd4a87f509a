import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { User, SupabaseClient } from '@supabase/supabase-js' // Re-enable import for type usage
import { resolveUserAuthContext } from './lib/auth-context'
import { RoleId } from './lib/rbac/roles'

// Bypass paths that don't need any middleware checks (e.g., static assets)
const ASSET_BYPASS_PATHS = [
  '/_next/static',
  '/_next/image',
  '/favicon.ico',
  // specific browser/OS files often requested
  '/.well-known',
  '/manifest.json',
  '/robots.txt',
  '/site.webmanifest',
  // common image/font extensions
  '.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.ico', // images
  '.woff', '.woff2', '.ttf', '.otf', '.eot', // fonts
];

// API routes are handled separately by their own auth
const API_ROUTE_PREFIX = '/api/';

// Public routes that don't need authentication (exact matches or prefixes)
const PUBLIC_PATHS = [
  '/', // Homepage
  '/auth/login', // Login page itself
  '/auth/callback', // Supabase auth callback
  '/auth/v1/callback', // Supabase auth callback (alternative)
  '/onboarding', // Public invitation acceptance page
  // Add other truly public marketing/info pages here
];

// Paths that bypass organization/user status redirect checks
// These pages are typically where users land if their org/account is disabled.
// They still require authentication, and auth context headers should be set.
const BYPASS_STATUS_REDIRECT_PATHS = [
  '/dashboard/organization-disabled',
  '/dashboard/account-disabled',
  '/auth/signout', // Signout path
];

const MAX_RETRIES = 3;
const RETRY_DELAY = 100; // ms

const ACTIVE_ORG_ID_COOKIE_NAME = 'active-org-id'; // Define cookie name

async function getSupabaseUser(supabase: SupabaseClient, requestPath: string, isPathPublic: boolean, retries = 0): Promise<User | null> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      // If AuthSessionMissingError occurs on a public path, it's expected. Return null without error/retry.
      if (error.message === 'Auth session missing!' && isPathPublic) {
        // console.log(`[Middleware] Auth session missing on public path "${requestPath}" (expected).`);
        return null;
      }

      // For other errors, or AuthSessionMissingError on a non-public path, attempt retries.
      if (retries < MAX_RETRIES) {
        console.warn(`[Middleware] supabase.auth.getUser() error on path "${requestPath}" (attempt ${retries + 1}/${MAX_RETRIES}): ${error.message}. Retrying...`);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retries + 1)));
        return getSupabaseUser(supabase, requestPath, isPathPublic, retries + 1);
      } else {
        // Max retries reached for a genuine error or session missing on a protected path.
        console.error(`[Middleware] Failed to get Supabase user for path "${requestPath}" after ${MAX_RETRIES} retries:`, error);
        return null; // Return null after max retries to allow downstream handling.
      }
    }
    return user;
  } catch (e) { // Catch any unexpected exception during the process itself
    console.error(`[Middleware] Unexpected exception in getSupabaseUser for path "${requestPath}":`, e);
    return null;
  }
}

export async function middleware(request: NextRequest) {
  const requestPath = request.nextUrl.pathname;
  const requestBaseForRedirects = new URL(request.nextUrl.origin); // e.g. http://localhost:3000

  // First, check for asset bypass paths
  if (ASSET_BYPASS_PATHS.some(path => requestPath.startsWith(path) || requestPath.endsWith(path))) {
    // console.log(`[Middleware] Path ${requestPath} is an asset. Bypassing all other logic.`);
    return NextResponse.next({ request });
  }

  // console.log(`[Middleware] Processing: ${requestPath}`);

  let supabaseResponse = NextResponse.next({ request });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet: Array<{ name: string; value: string; options: CookieOptions }>) {
          cookiesToSet.forEach(({ name, value, options }) => {
            // For request.cookies, only name and value. Options are for the response.
            request.cookies.set(name, value);
            supabaseResponse.cookies.set(name, value, options);
          });
        },
      },
    }
  );

  // IMPORTANT: DO NOT REMOVE auth.getUser() - it refreshes the session and handles cookies via the client config above.
  // We call this primarily for Supabase to manage session cookies.
  // The return values (_initialUser, _initialUserError) are not directly used in subsequent logic paths for non-API routes,
  // as the getSupabaseUser() helper is called later for that specific purpose.
  await supabase.auth.getUser(); // Call for side-effects (session refresh, cookie handling)

  // AFTER supabase.auth.getUser() (which might have set cookies on supabaseResponse)
  // check if it's an API route and return early if so.
  // This ensures API routes get their original body and any session cookies set by Supabase,
  // bypassing subsequent x-auth-* header modifications and response reconstructions.
  if (requestPath.startsWith(API_ROUTE_PREFIX)) {
    // Special handling for refresh-context endpoint is now done after setting auth headers
    if (requestPath === '/api/auth/refresh-context') {
      // Allow this to continue to our header mutation approach below
      console.log(`[Middleware] Allowing ${requestPath} to continue for header mutation.`);
    } else {
      // All other API routes bypass middleware
      return supabaseResponse;
    }
  }

  try {
    // The user object for logic below will come from the getSupabaseUser() helper call.
    // No need to check _initialUserError here as getSupabaseUser() has its own error handling.

    const isPathCurrentlyPublic = PUBLIC_PATHS.some(p =>
      p === '/' ? requestPath === p : requestPath.startsWith(p)
    );
    const user = await getSupabaseUser(supabase, requestPath, isPathCurrentlyPublic);

    if (!user) {
      if (isPathCurrentlyPublic) {
        return supabaseResponse; // Allow access to public paths
      }
      // If path is not public and no user, redirect to login
      const redirectUrl = new URL('/auth/login', requestBaseForRedirects);
      redirectUrl.searchParams.set('redirectedFrom', requestPath);
      return NextResponse.redirect(redirectUrl, { headers: supabaseResponse.headers });
    }

    // If user exists, continue with logic for authenticated users
    if (requestPath.startsWith('/auth/login')) {
      return NextResponse.redirect(new URL('/dashboard', requestBaseForRedirects), { headers: supabaseResponse.headers });
    }

    let authContext = null;
    if (user) {
      console.log(`[Middleware] Resolving auth context for user ${user.id}`);
      authContext = await resolveUserAuthContext(user.id);

      if (authContext) {
        // Start with headers from the current 'response', which includes cookies set by Supabase.
        const combinedHeaders = new Headers(supabaseResponse.headers);

        // Add custom x-auth-* headers.
        combinedHeaders.set('x-auth-user-id', authContext.userId);
        combinedHeaders.set('x-auth-org-id', authContext.orgId);
        const encodedOrgName = encodeURIComponent(authContext.orgName || `Org ${authContext.orgId.substring(0, 6)}`);
        combinedHeaders.set('x-auth-org-name', encodedOrgName);
        combinedHeaders.set('x-auth-org-role-id', authContext.orgRoleId.toString());
        combinedHeaders.set('x-auth-is-superadmin', authContext.isSuperAdmin.toString());
        combinedHeaders.set('x-auth-is-org-active', authContext.isOrgActive.toString());
        combinedHeaders.set('x-auth-is-user-active', authContext.isUserActive.toString());

        // For refresh-context API route, don't reconstruct the response to preserve the body
        if (requestPath === '/api/auth/refresh-context') {
          console.log(`[Middleware] Processing ${requestPath} with header mutation approach to preserve body.`);

          // Directly mutate the existing headers instead of reconstructing the response
          combinedHeaders.forEach((value, key) => {
            supabaseResponse.headers.set(key, value);
          });

          // Log the headers that were set
          console.log(`[Middleware] Set auth headers on original response for ${requestPath}`);
          return supabaseResponse; // Return the original response with modified headers
        }

        // For all other paths, continue with the current approach
        // Reconstruct the response with the combined headers, preserving original body, status, etc.
        supabaseResponse = new NextResponse(supabaseResponse.body, {
            status: supabaseResponse.status,
            statusText: supabaseResponse.statusText,
            headers: combinedHeaders,
        });
      } else {
        console.error(`[Middleware] Critical: Failed to resolve auth context for user ${user.id} on ${requestPath}.`);
      }
    }

    // Now 'response.headers' contains both Supabase cookies and x-auth-* headers (if authContext was resolved).
    if (authContext) {
      const isStatusRedirectBypass = BYPASS_STATUS_REDIRECT_PATHS.some(p => requestPath.startsWith(p));
      if (!isStatusRedirectBypass) {
        if (!authContext.isOrgActive && !authContext.isSuperAdmin) {
          return NextResponse.redirect(new URL('/dashboard/organization-disabled', requestBaseForRedirects), { headers: supabaseResponse.headers });
        }
        if (authContext.isOrgActive && !authContext.isUserActive) {
          return NextResponse.redirect(new URL('/dashboard/account-disabled', requestBaseForRedirects), { headers: supabaseResponse.headers });
        }
      }
    }

    if (PUBLIC_PATHS.some(path => requestPath.startsWith(path))) {
      return supabaseResponse;
    }
    if (user && requestPath.startsWith('/dashboard')) {
      const activeOrgId = request.cookies.get(ACTIVE_ORG_ID_COOKIE_NAME)?.value;

      if (activeOrgId) {
        try {
          const { data: memberData, error: memberError } = await supabase
            .from('organization_members')
            .select('org_member_is_active, org_member_role')
            .eq('user_id', user.id)
            .eq('org_id', activeOrgId)
            .single();

          if (memberError) {
            console.error(`[Middleware] DB error fetching member data: ${memberError.message}`);
            // Decide on error handling: throw, or allow request to proceed with potential issues downstream
            // For now, logging and allowing to proceed to avoid blocking user for intermittent DB errors.
          } else if (!memberData) {
            console.warn(`[Middleware] No member data found for user ${user.id} in org ${activeOrgId}.`);
            // This could mean user is not part of the org, or cookie is stale.
          } else {
            const { org_member_is_active, org_member_role } = memberData;

            if (org_member_is_active === false && requestPath !== '/dashboard/account-disabled') {
              const url = request.nextUrl.clone();
              url.pathname = '/dashboard/account-disabled';
              console.log(`[Middleware] User ${user.id} inactive in org ${activeOrgId}. Redirecting.`);
              return NextResponse.redirect(url); // Terminal response
            }

            if (org_member_is_active === true) {
              const { data: orgData, error: orgError } = await supabase
                .from('organizations')
                .select('is_active')
                .eq('id', activeOrgId)
                .single();

              if (orgError) {
                console.error(`[Middleware] DB error fetching organization data: ${orgError.message}`);
              } else if (!orgData) {
                console.warn(`[Middleware] No organization data found for org ${activeOrgId}.`);
              } else {
                const { is_active: orgIsActive } = orgData;
                if (orgIsActive === false && org_member_role !== RoleId.SUPERADMIN && requestPath !== '/dashboard/organization-disabled') {
                  const url = request.nextUrl.clone();
                  url.pathname = '/dashboard/organization-disabled';
                  console.log(`[Middleware] Org ${activeOrgId} inactive, user ${user.id} (role ${org_member_role}) not superadmin. Redirecting.`);
                  return NextResponse.redirect(url); // Terminal response
                }
              }
            }
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          console.error('[Middleware] Exception during proactive checks:', message);
          // Fall through to return supabaseResponse by default on unexpected exceptions.
        }
      }
      // If no activeOrgId cookie, user might land on dashboard and pick an org.
      // Or, if all dashboard pages require an active org context, this could be a redirect to a selection page.
      // Current logic only acts if activeOrgId is present.
    }

    console.warn(`[Middleware] Fallback: Reached end of middleware for ${requestPath}.`);
    return supabaseResponse;

  } catch (error) {
    console.error('[Middleware] Generic error:', error, { path: requestPath });
    if (!requestPath.startsWith('/auth/login')) {
      const redirectUrl = new URL('/auth/login', requestBaseForRedirects);
      redirectUrl.searchParams.set('error', 'middleware_error');
      redirectUrl.searchParams.set('redirectedFrom', requestPath);
      return NextResponse.redirect(redirectUrl, { headers: request.headers });
    }
    return supabaseResponse;
  }
}

export const config = {
  matcher: [
     '/((?!api/|_next/static|_next/image|.*\.(?:png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|otf|eot)$).*)'
  ],
};