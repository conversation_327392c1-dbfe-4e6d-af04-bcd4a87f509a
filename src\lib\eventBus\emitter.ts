/**
 * emitter.ts
 * -----------------------------------------------------------------------------
 * The **only** mitt singleton used throughout the app. Import it from here or
 * from "@/lib/eventBus".  In development it is exposed on `globalThis.__appEmitter`
 * and (optionally) wired to the mitt debugger + console logging helpers.
 * -----------------------------------------------------------------------------
 */

import mitt, { type Emitter } from 'mitt';

import type {
  OrganizationContextEvent,
  UserRoleChangedEvent,
  MemberStatusChangedEvent,
  OrgNameChangedEvent,
  AnyMemberChangeEvent,
  OrganizationMemberInsertedEvent,
  OrganizationMemberUpdatedEvent,
  OrganizationMemberDeletedEvent,
  OrganizationDataChangedEvent,
  OrganizationActiveStatusChangedEvent,
  AuthEvent,
} from '@/lib/eventTypes';

import { AUTH_CONTEXT_CHANGED } from './constants';
import { attachMittDebugger } from '@/tools/mitt-debugger';

/* -------------------------------------------------------------------------- */
/*                                Event types                                 */
/* -------------------------------------------------------------------------- */

export type Events = {
  [AUTH_CONTEXT_CHANGED]: AuthEvent;

  /* Legacy events – still in use, will be migrated to unified AUTH_CONTEXT_CHANGED */
  'organization:context:changed': OrganizationContextEvent;
  'user:role:changed': UserRoleChangedEvent;
  'member:status:changed': MemberStatusChangedEvent;
  'organization:name:changed': OrgNameChangedEvent;
  'organization:member:changed': AnyMemberChangeEvent;
  'organization:statusChanged': OrganizationActiveStatusChangedEvent;
  'client:authContextUpdated': {
    isUserActive?: boolean;
    isOrgActive?: boolean;
    newRoleId?: number;
  };

  /* New granular events */
  'db:organization_members:inserted': OrganizationMemberInsertedEvent;
  'db:organization_members:updated': OrganizationMemberUpdatedEvent;
  'db:organization_members:deleted': OrganizationMemberDeletedEvent;
  'db:organizations:updated': OrganizationDataChangedEvent;

  /* Optimistic context-switch helpers */
  'organization:context:optimistic': {
    userId: string | null;
    orgId: string;
    orgName: string;
    role: number;
  };
  'organization:context:optimistic:done': {
    orgId: string;
    success?: boolean;
    refreshed?: boolean;
  };
  'organization:context:optimistic:error': { orgId: string };
};

/* -------------------------------------------------------------------------- */
/*                            Singleton mitt emitter                          */
/* -------------------------------------------------------------------------- */

export const emitter = mitt<Events>();

/* -------------------------------------------------------------------------- */
/*                       Development-only diagnostics hooks                   */
/* -------------------------------------------------------------------------- */

const __DEV__ = process.env.NODE_ENV === 'development';

if (__DEV__ && typeof globalThis !== 'undefined') {
  // Make it poke-able from DevTools
  (globalThis as any).__appEmitter = emitter;

  if (process.env.NEXT_PUBLIC_ENABLE_MITT_DEBUGGER === 'true') {
    attachMittDebugger(emitter);
  }

  if (process.env.NEXT_PUBLIC_ENABLE_EVENT_LOGGING === 'true') {
    emitter.on('*', (type, payload) => {
      // Filter out noisy events during organization switches
      if (type === 'db:organization_members:updated' &&
          sessionStorage.getItem('currentOrgSwitchId')) {
        return; // Skip logging during user-initiated switches
      }
      // eslint-disable-next-line no-console
      console.debug(`[mitt:event] ${type}`, payload);
    });
  }
}

/* -------------------------------------------------------------------------- */
/*                 isServer / clientLog (needed by other modules)             */
/* -------------------------------------------------------------------------- */

export const isServer = typeof window === 'undefined';

/**
 * Lightweight console helper used by some channel interpreters.
 * Logs only on the **client** and only in **development** mode.
 */
export const clientLog = (msg: string, ...args: unknown[]) => {
  if (!isServer && __DEV__) {
    // eslint-disable-next-line no-console
    console.debug(msg, ...args);
  }
};

/* -------------------------------------------------------------------------- */
/*                        Global typings for `__appEmitter`                   */
/* -------------------------------------------------------------------------- */

declare global {
  // eslint-disable-next-line no-var
  var __appEmitter: Emitter<Events> | undefined;

  interface Window {
    __appEmitter?: Emitter<Events>;
  }
}

export {}; // ensures this file is treated as a module
