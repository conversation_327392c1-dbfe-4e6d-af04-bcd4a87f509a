---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
# Project Structure Map

## Core Architecture
- Next.js 15 App Router application wrst approach
- Supabase for authentication, database, and storage
- Shadcn UI components with Tailwind CSS styling
- Role-based access control (RBAC) system
- Development Operating System is Windows
- IDE is VSCode

## Dynamic APIs in Next.js 15+

### Overview
Dynamic APIs in Next.js 15+ are asynchronous and must be properly awaited:
- searchParams in pages and layouts
- cookies() from next/headers
- headers() from next/headers

### Server Components Pattern
```typescript
interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default async function Page({ searchParams }: PageProps) {
  const params = await searchParams;
  // Now you can safely use params.id
}
```

### Client Components Pattern
```typescript
'use client'

import { use } from 'react'

interface PageProps {
  searchParams: Promise<{
    id?: string;
  }>;
}

export default function Page({ searchParams }: PageProps) {
  const params = use(searchParams);
  // Now you can safely use params.id
}
```

### Best Practices
1. Always type searchParams as a Promise
2. Always await searchParams before accessing properties
3. Use React.use() in Client Components
4. Use await in Server Components
5. Never access dynamic API properties directly
6. Handle all dynamic APIs asynchronously

[Rest of the existing content remains unchanged...] 