export interface SubItem {
  text: string;
}

export interface CriteriaItem {
  number: string | number;
  text: string;
  sub_items?: SubItem[];
}

export interface CriteriaItems {
  header?: string;
  sub_criteria: CriteriaItem[];
}

export interface Criteria {
  criteria_notes?: string;
  criteria_items: CriteriaItems;
  criteria_remark?: string;
}

export interface ServicePlace {
  place_of_service: string;
  place_of_service_text?: string;
  service_time_days?: string;
  service_time_times?: string;
  place_of_service_remark?: string;
}

export interface ServiceChannels {
  places_of_service: ServicePlace[];
}

export interface ProcedureStep {
  number: string | number;
  text: string;
}

export interface Procedure {
  step: string | number;
  title: string;
  procedure_steps: ProcedureStep[];
  time?: string;
  responsible_section?: string;
  procedure_remarks?: string;
}

export interface Procedures {
  total_time?: string;
  rows: Procedure[];
}

export interface RequiredDocument {
  number: string | number;
  document_name: string;
  remarks?: string;
  authority?: string;
  original_documents: string | number;
  copies: string | number;
}

export interface RequiredDocuments {
  rows: RequiredDocument[];
}

export interface FeeItem {
  number: string | number;
  fee_details: string;
  fee_amount: string | number;
}

export interface Fee {
  rows: FeeItem[];
}

export interface ComplaintChannel {
  number: string | number;
  channel_name: string;
  address?: string;
  hotline?: string;
  email?: string;
  website?: string;
  pobox?: string;
}

export interface ComplaintChannels {
  rows: ComplaintChannel[];
}

export interface ApplicationForm {
  number?: string | number;
  form_name: string;
  remarks?: string;
}

export interface ApplicationForms {
  rows: ApplicationForm[];
}

export interface SubRemark {
  text: string;
}

export interface BottomRemarks {
  header?: string;
  remarks?: string;
  sub_remarks: SubRemark[];
}

export interface Source {
  "source-url"?: string;
  "source-publication-date"?: string;
}

export interface Service {
  service_name: string;
  service_case_id?: string;
}

export interface Handbook {
  service: Service;
  criteria: Criteria;
  procedures: Procedures;
  required_documents: RequiredDocuments;
  fee: Fee;
  service_channels: ServiceChannels;
  complaint_channels: ComplaintChannels;
  application_forms: ApplicationForms;
  bottom_remarks: BottomRemarks;
  source: Source;
}

export interface HandbookProps {
  handbook?: Handbook;
} 