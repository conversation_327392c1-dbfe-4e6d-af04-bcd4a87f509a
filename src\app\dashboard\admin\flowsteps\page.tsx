"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, MoreVertical } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { FlowStepCategoryEditDialog } from "@/components/flowsteps/FlowStepCategoryEditDialog";
import {
  Type,
  Calendar,
  Hash,
  Mail,
  Phone,
  List,
  CheckSquare,
  CircleDot,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { createBrowserClient } from "@supabase/ssr";
import { PostgrestError } from "@supabase/supabase-js";
import { toastMessages } from "@/lib/toast-messages";
import { v4 as uuidv4 } from "uuid";
import React from "react";
import { FlowStepCategory } from "@/types/app/dashboard/admin/flowsteps/FlowStepCategory"
import { FlowStepData, FlowStepType, FLOW_STEP_TYPES } from "@/types/app/dashboard/admin/flowsteps/FlowStepData"
import type { SortableFlowStepProps } from "@/types/app/dashboard/admin/flowsteps/SortableFlowStepProps"; // Import the interface

const TYPE_ICONS: Record<FlowStepType, React.FC<{ className?: string }>> = {
  text: Type,
  date: Calendar,
  number: Hash,
  email: Mail,
  phone: Phone,
  select: List,
  checkbox: CheckSquare,
  radio: CircleDot,
} as const;

// SortableFlowStepProps interface removed, now imported

function FlowStepTypeIcon({
  type,
  className,
}: {
  type: FlowStepType;
  className?: string;
}) {
  const Icon = TYPE_ICONS[type];
  if (!Icon) {
    // Default to text icon if type is undefined or invalid
    return <Type {...(className && { className })} />;
  }
  return <Icon {...(className && { className })} />;
}

// Create a new memoized component for the flow step content
const FlowStepContent = React.memo(function FlowStepContent({
  flowStep,
  flowStepCategory,
  onDelete,
  isExpanded,
  setIsExpanded,
  editValue,
  setEditValue,
  description,
  setDescription,
  type,
  setType,
  handleSave,
  handleCancel,
  handleKeyDown,
  inputRef,
  index,
}: {
  flowStep: FlowStepData;
  flowStepCategory: FlowStepCategory;
  onDelete: (id: number, flowStepCategoryId: number) => void;
  onSave: (flowStep: FlowStepData) => void;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  editValue: string;
  setEditValue: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  type: FlowStepType;
  setType: (value: FlowStepType) => void;
  handleSave: () => void;
  handleCancel: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  inputRef: React.RefObject<HTMLInputElement | null>;
  index: number;
}) {
  return (
    <Card
      className={`transition-all ${
        isExpanded ? "p-4 shadow-sm" : "p-3"
      } cursor-move hover:shadow-sm bg-white`}
    >
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 flex-1 pr-2">
            <span className="text-sm text-muted-foreground">
              {flowStepCategory.displayId}.{index + 1}
            </span>

            <Input
              ref={inputRef}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="h-6 text-sm flex-1"
              placeholder="Add flow step name"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(true);
              }}
            />
          </div>
          <FlowStepTypeIcon
            type={type}
            className="h-4 w-4 text-muted-foreground"
          />
        </div>

        {isExpanded && (
          <div className="space-y-4 pt-2">
            <div className="space-y-2">
              <div className="text-sm font-medium">Description</div>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Add a description..."
                className="min-h-[80px] resize-y"
              />
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Type</div>
              <Select
                value={type}
                onValueChange={(value: FlowStepType) => setType(value)}
                onOpenChange={(open) => {
                  if (!open) {
                    const selectTrigger = document.activeElement as HTMLElement;
                    selectTrigger?.addEventListener(
                      "keydown",
                      (e) => {
                        if (e.key === "Enter") {
                          handleSave();
                        }
                      },
                      { once: true }
                    );
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {FLOW_STEP_TYPES.map((typeValue) => (
                    <SelectItem key={typeValue} value={typeValue}>
                      <div className="flex items-center">
                        <FlowStepTypeIcon
                          type={typeValue}
                          className="h-4 w-4 mr-2"
                        />
                        {typeValue.charAt(0).toUpperCase() + typeValue.slice(1)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(flowStep.id, flowStepCategory.id)}
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button size="sm" onClick={handleSave}>
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
});

// Update the SortableFlowStep component to be a thin wrapper
function SortableFlowStep({
  flowStep,
  flowStepCategory,
  onDelete,
  onSave,
  index,
}: SortableFlowStepProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editValue, setEditValue] = useState(flowStep.name);
  const [description, setDescription] = useState(flowStep.description);
  const [type, setType] = useState<FlowStepType>(flowStep.type);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `${flowStepCategory.id}.${flowStep.id}`,
    data: {
      flowStep,
      flowStepCategory,
      index,
    },
  });

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    transition: transition || undefined,
    opacity: isDragging ? 0.4 : 1,
    position: "relative" as const,
    zIndex: isDragging ? 1 : 0,
  };

  const handleSave = useCallback(() => {
    onSave({
      ...flowStep,
      name: editValue,
      description,
      type,
    });
    setIsExpanded(false);
  }, [flowStep, editValue, description, type, onSave]);

  const handleCancel = useCallback(() => {
    setEditValue(flowStep.name);
    setDescription(flowStep.description);
    setType(flowStep.type);
    setIsExpanded(false);
  }, [flowStep]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSave();
      }
    },
    [handleSave]
  );

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="transition-transform duration-200 ease-in-out"
    >
      <FlowStepContent
        flowStep={flowStep}
        flowStepCategory={flowStepCategory}
        onDelete={onDelete}
        onSave={onSave}
        isExpanded={isExpanded}
        setIsExpanded={setIsExpanded}
        editValue={editValue}
        setEditValue={setEditValue}
        description={description}
        setDescription={setDescription}
        type={type}
        setType={setType}
        handleSave={handleSave}
        handleCancel={handleCancel}
        handleKeyDown={handleKeyDown}
        inputRef={inputRef}
        index={index}
      />
    </div>
  );
}

export default function FlowStepsPage() {
  const [flowStepCategories, setflowStepCategories] = useState<
    FlowStepCategory[]
  >([]);
  const [flowSteps, setflowSteps] = useState<FlowStepData[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingFlowStepCategory, setEditingFlowStepCategory] =
    useState<FlowStepCategory | null>(null);
  const [isFlowStepCategoryDialogOpen, setIsFlowStepCategoryDialogOpen] =
    useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const filteredFlowSteps = useCallback(
    (flowStepCategoryId: number) => {
      const categoryFlowSteps = flowSteps
        .filter((flowStep) => {
          if (!searchQuery) return flowStep.category_id === flowStepCategoryId;

          // Search by flowstepcategory.displayId.flowstep.id format
          const idSearch = searchQuery.match(/^(\d+)\.(\d+)$/);
          if (idSearch) {
            const [, catDisplayId, flowStepIndex] = idSearch;
            const category = flowStepCategories.find(
              (cat) => cat.displayId === parseInt(catDisplayId)
            );
            const categoryFlowSteps = flowSteps.filter(
              (l) => l.category_id === category?.id
            );
            const targetFlowStep =
              categoryFlowSteps[parseInt(flowStepIndex) - 1];
            return flowStep === targetFlowStep;
          }

          // Search by name
          return (
            flowStep.category_id === flowStepCategoryId &&
            (flowStep.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              flowStep.description
                .toLowerCase()
                .includes(searchQuery.toLowerCase()))
          );
        })
        .sort((a, b) => a.position - b.position);

      return categoryFlowSteps;
    },
    [flowSteps, searchQuery, flowStepCategories]
  );

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!active || !over || active.id === over.id) {
      return;
    }

    if (over && active.id !== over.id) {
      const [activeCatId] = (active.id as string).split(".");
      const categoryId = parseInt(activeCatId);

      // Get all flowsteps for this category and their current order
      const categoryFlowSteps = flowSteps.filter(
        (flowStep) => flowStep.category_id === categoryId
      );
      const otherFlowSteps = flowSteps.filter(
        (flowStep) => flowStep.category_id !== categoryId
      );

      // Find the indices for reordering
      const oldIndex = categoryFlowSteps.findIndex(
        (item) => `${item.category_id}.${item.id}` === active.id
      );
      const newIndex = categoryFlowSteps.findIndex(
        (item) => `${item.category_id}.${item.id}` === over.id
      );

      // Reorder the category flowsteps
      const reorderedCategoryFlowSteps = arrayMove(
        categoryFlowSteps,
        oldIndex,
        newIndex
      ).map((flowStep, index) => ({
        ...flowStep,
        position: index + 1,
      }));

      // Update UI immediately
      setflowSteps([...otherFlowSteps, ...reorderedCategoryFlowSteps]);

      // Prepare the update payload
      const updatePayload = reorderedCategoryFlowSteps.map((flowStep) => ({
        uid: flowStep.uid,
        position: flowStep.position,
        name: flowStep.name,
        description: flowStep.description,
        type: flowStep.type,
        category_id: flowStep.category_id,
      }));

      // Update database in the background
      try {
        const { error } = await supabase
          .from("flowsteps")
          .upsert(updatePayload, {
            onConflict: "uid",
          });

        if (error) throw error;

        toastMessages.form.reorderSuccess({ description: "flowsteps reordered successfully" });
      } catch (error: unknown) {
        const pgError = error as PostgrestError;
        console.error("Error updating flowstep positions:", {
          error: pgError,
          message: pgError?.message,
          details: pgError?.details,
          hint: pgError?.hint,
        });

        // Revert UI changes on error
        setflowSteps((prev) => {
          const currentOtherFlowSteps = prev.filter(
            (flowStep) => flowStep.category_id !== categoryId
          );
          return [...currentOtherFlowSteps, ...categoryFlowSteps];
        });

        toastMessages.form.reorderError("Failed to update flowstep order");
      }
    }
  };

  const handleDeleteFlowStep = async (
    id: number,
    flowStepCategoryId: number
  ) => {
    try {
      const { error } = await supabase
        .from("flowsteps")
        .delete()
        .eq("id", id)
        .eq("category_id", flowStepCategoryId);

      if (error) throw error;

      setflowSteps((prev) =>
        prev.filter(
          (flowStep) =>
            !(flowStep.id === id && flowStep.category_id === flowStepCategoryId)
        )
      );
      toastMessages.form.deleteSuccess({ description: "flowstep deleted" });
    } catch (error) {
      console.error("Error deleting flowstep:", error);
      toastMessages.form.deleteError();
    }
  };

  const handleEditFlowStepCategory = (flowStepCategory: FlowStepCategory) => {
    setEditingFlowStepCategory(flowStepCategory);
    setIsFlowStepCategoryDialogOpen(true);
  };

  const handleSaveFlowStepCategory = async (
    updatedFlowStepCategory: Partial<FlowStepCategory>
  ) => {
    try {
      if (editingFlowStepCategory) {
        const { data, error } = await supabase
          .from("flowstepcategory")
          .update({ name: updatedFlowStepCategory.name })
          .eq("uid", editingFlowStepCategory.uid)
          .select()
          .single();

        if (error) throw error;

        setflowStepCategories((prev) =>
          prev.map((flowStepCategory) =>
            flowStepCategory.uid === editingFlowStepCategory.uid
              ? { ...flowStepCategory, ...data }
              : flowStepCategory
          )
        );
        setIsFlowStepCategoryDialogOpen(false);
        toastMessages.form.saveSuccess({ description: `Category "${updatedFlowStepCategory.name}" updated` });
      } else {
        // Calculate the next position
        const nextPosition =
          flowStepCategories.length > 0
            ? Math.max(...flowStepCategories.map((cat) => cat.position || 0)) +
              1
            : 1;

        const { data, error } = await supabase
          .from("flowstepcategories")
          .insert({
            name: updatedFlowStepCategory.name,
            uid: uuidv4(),
            position: nextPosition,
          })
          .select()
          .single();

        if (error) throw error;

        setflowStepCategories((prev) => [...prev, data]);
        setIsFlowStepCategoryDialogOpen(false);
        toastMessages.form.createSuccess({ description: `Category "${updatedFlowStepCategory.name}" created` });
      }
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error saving category:", pgError);
      toastMessages.form.saveError();
    }
  };

  const handleDeleteFlowStepCategory = async (flowStepCategoryId: number) => {
    try {
      const { error } = await supabase
        .from("flowstepcategories")
        .delete()
        .eq("id", flowStepCategoryId);

      if (error) throw error;

      setflowStepCategories((prev) =>
        prev.filter(
          (flowStepCategory) => flowStepCategory.id !== flowStepCategoryId
        )
      );
      setflowSteps((prev) =>
        prev.filter((flowStep) => flowStep.category_id !== flowStepCategoryId)
      );
      toastMessages.form.deleteSuccess({ description: "Category deleted" });
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error deleting category:", pgError);
      toastMessages.form.deleteError();
    }
  };

  const handleSaveFlowStep = async (updatedFlowStep: FlowStepData) => {
    try {
      const { data, error } = await supabase
        .from("flowsteps")
        .update({
          name: updatedFlowStep.name,
          description: updatedFlowStep.description,
          type: updatedFlowStep.type,
        })
        .eq("uid", updatedFlowStep.uid)
        .select()
        .single();

      if (error) throw error;

      setflowSteps((prev) =>
        prev.map((flowStep) =>
          flowStep.uid === updatedFlowStep.uid
            ? { ...flowStep, ...data }
            : flowStep
        )
      );
      toastMessages.form.saveSuccess({ description: "flowstep updated" });
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error saving flowstep:", pgError);
      toastMessages.form.saveError();
    }
  };

  const handleAddFlowStep = async (displayCategoryId: number) => {
    try {
      // Find the category using displayId
      const category = flowStepCategories.find(
        (cat) => cat.displayId === displayCategoryId
      );
      if (!category) {
        throw new Error("Category not found");
      }

      const newFlowStep = {
        uid: uuidv4(),
        name: "",
        description: "",
        type: "text" as FlowStepType,
        category_id: category.id, // Use the actual database ID
        position:
          flowSteps.filter((l) => l.category_id === category.id).length + 1,
      };

      console.log("Adding new flowstep:", newFlowStep); // Debug log

      const { data, error } = await supabase
        .from("flowsteps")
        .insert(newFlowStep)
        .select()
        .single();

      if (error) throw error;

      setflowSteps((prev) => [...prev, data]);
      toastMessages.form.createSuccess({ description: "flowstep created" });
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error adding flowstep:", pgError);
      toastMessages.form.createError();
    }
  };

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [
          { data: flowStepCategoriesData, error: flowStepCategoriesError },
          { data: flowStepsData, error: flowStepsError },
        ] = await Promise.all([
          supabase.from("flowstepcategories").select("*").order("position"),
          supabase.from("flowsteps").select("*").order("category_id, id"),
        ]);

        if (flowStepCategoriesError) throw flowStepCategoriesError;
        if (flowStepsError) throw flowStepsError;

        // Store categories with their original IDs but add a displayId
        const sortedCategories = (flowStepCategoriesData || []).map(
          (category, index) => ({
            ...category,
            displayId: index + 1,
          })
        );

        setflowStepCategories(sortedCategories);
        setflowSteps(flowStepsData || []);
      } catch (error: unknown) {
        const pgError = error as PostgrestError;
        console.error("Error fetching data:", pgError);
        toastMessages.form.fetchError("flowsteps and categories");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [supabase]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-white rounded-lg">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">Flow Steps</h1>
          <Button
            variant="outline"
            onClick={() => {
              setEditingFlowStepCategory(null);
              setIsFlowStepCategoryDialogOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        </div>
        <Input
          placeholder="Search flow steps... (e.g. 1.1 or flow step name)"
          className="max-w-sm"
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className="grid grid-cols-4 gap-6">
        {flowStepCategories.map((flowStepCategory) => (
          <div
            key={flowStepCategory.id}
            className="space-y-4 bg-gray-100 rounded-b-lg"
          >
            <div className="bg-[#0A2C35] text-white p-3 rounded-t-lg">
              <div className="flex items-center justify-between">
                <span>
                  {flowStepCategory.displayId}. {flowStepCategory.name}
                </span>
                <div className="flex items-center space-x-2">
                  <span>{filteredFlowSteps(flowStepCategory.id).length}</span>
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-white"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() =>
                          handleEditFlowStepCategory(flowStepCategory)
                        }
                      >
                        Edit Category
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-destructive"
                        onClick={() =>
                          handleDeleteFlowStepCategory(flowStepCategory.id)
                        }
                      >
                        Delete Category
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            <div className="space-y-2 p-4 pt-0">
              <DndContext
                sensors={sensors}
                onDragEnd={handleDragEnd}
                collisionDetection={closestCenter}
              >
                <SortableContext
                  items={filteredFlowSteps(flowStepCategory.id).map(
                    (flowStep) => `${flowStepCategory.id}.${flowStep.id}`
                  )}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-2">
                    {filteredFlowSteps(flowStepCategory.id).map(
                      (flowStep, index) => (
                        <SortableFlowStep
                          key={flowStep.uid}
                          flowStep={flowStep}
                          flowStepCategory={flowStepCategory}
                          onDelete={handleDeleteFlowStep}
                          onSave={handleSaveFlowStep}
                          index={index}
                        />
                      )
                    )}
                  </div>
                </SortableContext>
              </DndContext>

              <Button
                variant="ghost"
                className="w-full justify-start text-muted-foreground"
                onClick={() => handleAddFlowStep(flowStepCategory.displayId)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add flowstep
              </Button>
            </div>
          </div>
        ))}
      </div>

      <FlowStepCategoryEditDialog
        flowStepCategory={editingFlowStepCategory}
        isOpen={isFlowStepCategoryDialogOpen}
        onClose={() => {
          setIsFlowStepCategoryDialogOpen(false);
          setEditingFlowStepCategory(null);
        }}
        onSave={handleSaveFlowStepCategory}
      />
    </div>
  );
}
