'use server'

import { headers } from 'next/headers'
import { z } from 'zod'
import { createAdminClient } from '@/lib/supabase/admin'
import { createHash } from 'crypto'

const nameSchema = z.string()
  .min(1, 'Name is required')
  .max(25, 'Name must be 25 characters or less')
  .regex(/^[\p{L}\-'\s]+$/u, 'Name contains invalid characters')

const acceptSchema = z.object({
  token: z.string().min(20).max(100),
  firstName: nameSchema,
  lastName: nameSchema,
  email: z.string().email(),
})

export async function acceptInvitationAnonymous(data: z.infer<typeof acceptSchema>) {
  try {
    // 1. Security checks - Origin and Referer validation
    const headersList = await headers()
    const origin = headersList.get('origin')
    const referer = headersList.get('referer')
    const expectedOrigin = process.env.NEXT_PUBLIC_SITE_URL

    if (expectedOrigin && origin !== expectedOrigin) {
      console.warn('Invalid origin for anonymous invitation acceptance:', { origin, expectedOrigin })
      return { success: false, error: 'Invalid request origin' }
    }

    if (referer && !referer.includes('/onboarding')) {
      console.warn('Invalid referer for anonymous invitation acceptance:', { referer })
      return { success: false, error: 'Invalid request source' }
    }

    // 2. Validate input
    const validatedData = acceptSchema.parse(data)
    const { token, firstName, lastName, email } = validatedData
    const fullName = `${firstName} ${lastName}`

    // 3. Admin client with service-role
    const admin = createAdminClient()

    // 4. Verify invitation row (hash lookup)
    const hash = createHash('sha256').update(token).digest('hex')

    const { data: inv } = await admin
      .from('email_invitations')
      .select('status, expires_at')
      .eq('invitation_hash', hash)
      .eq('email', email)
      .maybeSingle()

    if (!inv) {
      return { success: false, error: 'Invitation not found' }
    }

    if (inv.status === 'accepted' || inv.status === 'added') {
      return { success: false, error: 'Invitation has already been accepted' }
    }

    if (inv.status !== 'delivered') {
      return { success: false, error: 'Invitation is not in a valid state for acceptance' }
    }

    if (new Date(inv.expires_at) < new Date()) {
      return { success: false, error: 'Invitation has expired' }
    }

    // 5. Create or fetch user via the Admin SDK
    // First check if user already exists
    const { data: existingUsers } = await admin.auth.admin.listUsers({
      page: 1,
      perPage: 1000 // Should be enough for email search
    })

    const existingUser = existingUsers.users.find(user => user.email === email)

    let userId: string
    let effectiveFullName: string

    if (existingUser) {
      userId = existingUser.id // Re-invite case - user already exists
      // PRESERVE existing user's name from auth table
      effectiveFullName = existingUser.user_metadata?.full_name || fullName
      console.log('Using existing user:', {
        userId,
        preservedName: effectiveFullName,
        inputName: fullName
      })
    } else {
      // Create new user - trigger will automatically create profile
      const res = await admin.auth.admin.createUser({
        email,
        email_confirm: false, // They'll confirm with the magic link later
        user_metadata: {
          full_name: fullName,
          email: email
        }
      })
      if (res.error) {
        console.error('Failed to create user - Full error details:', {
          message: res.error.message,
          status: res.error.status,
          code: res.error.code,
          details: res.error
        })
        return { success: false, error: `Failed to create user account: ${res.error.message}` }
      }
      userId = res.data.user!.id
      effectiveFullName = fullName // Use provided name for new users
      console.log('Created new user:', { userId, newName: effectiveFullName })
      // Note: handle_new_user trigger will automatically create the profile
    }

    // 6. Complete invitation acceptance with robust function
    const { error } = await admin.rpc('accept_invitation_anonymous_complete', {
      p_hash: hash,
      p_user_id: userId,
      p_email: email,
      p_full_name: effectiveFullName // Use preserved name for existing users
    })

    if (error) {
      console.error('Failed to attach organization:', error)
      return { success: false, error: error.message }
    }

    return { success: true }

  } catch (error) {
    console.error('Anonymous invitation acceptance error:', error)

    if (error instanceof z.ZodError) {
      return { success: false, error: 'Invalid input data' }
    }

    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function declineInvitationAnonymous(token: string) {
  try {
    // Validate token format
    if (!token || token.length < 20 || token.length > 100) {
      return { success: false, error: 'Invalid token format' }
    }

    // Security checks - Origin and Referer validation
    const headersList = await headers()
    const origin = headersList.get('origin')
    const referer = headersList.get('referer')
    const expectedOrigin = process.env.NEXT_PUBLIC_SITE_URL

    if (expectedOrigin && origin !== expectedOrigin) {
      console.warn('Invalid origin for anonymous invitation decline:', { origin, expectedOrigin })
      return { success: false, error: 'Invalid request origin' }
    }

    if (referer && !referer.includes('/onboarding')) {
      console.warn('Invalid referer for anonymous invitation decline:', { referer })
      return { success: false, error: 'Invalid request source' }
    }

    // Use admin client to decline invitation
    const admin = createAdminClient()
    const hash = createHash('sha256').update(token).digest('hex')

    // Update invitation status to declined
    const { error } = await admin
      .from('email_invitations')
      .update({ status: 'declined' })
      .eq('invitation_hash', hash)
      .eq('status', 'delivered')
      .gte('expires_at', new Date().toISOString())

    if (error) {
      console.error('Anonymous invitation decline error:', error)
      return { success: false, error: 'Failed to decline invitation' }
    }

    return { success: true }

  } catch (error) {
    console.error('Anonymous invitation decline error:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}
