'use client'

import type { OrgNameChangedEvent } from '@/lib/eventTypes'
import { useBusEvent } from '@/lib/useBusEvent'

/**
 * Subscribe to organization name change events for a specific org
 */
export function useOrgNameEvents(
  orgId: string | null,
  handler: (event: OrgNameChangedEvent) => void
): void {
  useBusEvent(
    'organization:name:changed',
    (payload) => {
      if (!orgId || payload.orgId !== orgId) return
      handler(payload)
    },
    [orgId, handler]
  )
}
