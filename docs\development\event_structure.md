New eventbus structure:

src/
└── lib/
    ├── eventBus/
    │   ├── index.ts             # re-export core bus & hooks
    │   ├── emitter.ts           # mitt emitter + Events type
    │   ├── core.ts              # OrganizationEventBus class (no hooks)
    │   ├── channels/
    │   │   ├── context.ts       # setupRealtimeSubscription + handler
    │   │   ├── role.ts          # setupRoleSubscription + handler
    │   │   ├── status.ts        # setupMemberStatusSubscription + handler
    │   │   ├── name.ts          # setupOrgNameSubscription + handler
    │   │   └── allMembers.ts    # setupAllMembersSubscription + handler
    │   └── hooks/
    │       ├── useContextEvents.ts
    │       ├── useRoleEvents.ts
    │       ├── useStatusEvents.ts
    │       ├── useNameEvents.ts
    │       └── useAllMemberChanges.ts



### Scratchpad

## EventBus Restructuring Plan

### Current Structure Analysis
- `src/lib/eventBus.ts` is a large monolithic file (~1125 lines) containing:
  - Event type definitions
  - A mitt emitter for events
  - A singleton OrganizationEventBus class with all channel management
  - Hook exports for different event types
- `src/lib/eventTypes.ts` contains event type definitions
- `src/hooks/use-organization-members-event-bus.ts` depends on hooks from eventBus.ts
- `src/hooks/use-organization-members-subscription.ts` is deprecated but still exists
- `src/hooks/use-realtime-subscription.ts` provides lower-level realtime subscription functionality

### Dependencies & Imports
- `src/app/dashboard/admin/users/user-table.tsx` imports from use-organization-members-event-bus.ts
- `src/providers/organization-check-provider.tsx` imports useOrganizationContextEvents from @/lib
- `src/hooks/use-organization-context.ts` imports useOrganizationContextEvents from @/lib
- `src/components/organization/organization-switcher.tsx` imports useOrgNameEvents and useUserRoleEvents from @/lib
- `src/components/dashboard/test-event-system.tsx` imports all event hooks and organizationEventBus
- `src/hooks/use-organization-storage.ts` imports organizationEventBus

### Restructuring Tasks

#### 1. Create Base Directory Structure
- [X] Create src/lib/eventBus/ directory
- [X] Create src/lib/eventBus/channels/ directory
- [X] Create src/lib/eventBus/hooks/ directory

#### 2. Extract Core Files
- [X] Create src/lib/eventBus/emitter.ts
  - Extract mitt emitter and Events type from src/lib/eventBus.ts
  - Import event types from eventTypes.ts
- [X] Create src/lib/eventBus/core.ts
  - Extract OrganizationEventBus class from src/lib/eventBus.ts
  - Remove hook exports
  - Update imports

#### 3. Extract Channel Handlers
- [X] Create src/lib/eventBus/channels/context.ts
  - Extract setupRealtimeSubscription and handleRealtimeEvent from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/channels/role.ts
  - Extract setupRoleSubscription and handleRoleChangeEvent from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/channels/status.ts
  - Extract setupMemberStatusSubscription and handleMemberStatusChangeEvent from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/channels/name.ts
  - Extract setupOrgNameSubscription and handleOrgNameChangeEvent from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/channels/allMembers.ts
  - Extract setupAllMembersSubscription and handleAllMembersEvent from src/lib/eventBus.ts

#### 4. Extract Hook Functions
- [X] Create src/lib/eventBus/hooks/useContextEvents.ts
  - Extract useOrganizationContextEvents from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/hooks/useRoleEvents.ts
  - Extract useUserRoleEvents from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/hooks/useStatusEvents.ts
  - Extract useMemberStatusEvents from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/hooks/useNameEvents.ts
  - Extract useOrgNameEvents from src/lib/eventBus.ts
- [X] Create src/lib/eventBus/hooks/useAllMemberChanges.ts
  - Extract useAllMemberChanges from src/lib/eventBus.ts

#### 5. Create Main Index File
- [X] Create src/lib/eventBus/index.ts
  - Re-export events types from eventTypes.ts
  - Re-export emitter from emitter.ts
  - Re-export OrganizationEventBus instance from core.ts
  - Re-export all hook functions

#### 6. Update Imports in Dependent Files
- [X] Update imports in src/lib/index.ts to point to new path
- [X] Update imports in src/hooks/use-organization-members-event-bus.ts
- [X] Update imports in src/hooks/use-organization-context.ts
- [X] Update imports in src/providers/organization-check-provider.tsx
- [X] Update imports in src/components/organization/organization-switcher.tsx
- [X] Update imports in src/components/dashboard/test-event-system.tsx
- [X] Update imports in src/hooks/use-organization-storage.ts

#### 7. Update useBusEvent.ts
- [X] Update import in src/lib/useBusEvent.ts to use the new path

#### 8. Deprecated Files Handling
- [X] Mark use-organization-members-subscription.ts as deprecated with documentation

#### 9. Testing Steps
- [ ] Test that all event subscriptions still work properly after refactoring
- [ ] Test that role changes, organization context changes, and other real-time events still propagate
- [ ] Test the organization switcher functionality to ensure it still responds to events
- [ ] Test the admin user table to ensure real-time updates still work

#### 10. Cleanup (Optional)
- [ ] Remove the original src/lib/eventBus.ts file once all functionality has been migrated
- [ ] Consider moving src/lib/useBusEvent.ts to src/lib/eventBus/hooks/useBusEvent.ts

### Current Status
- Completed all implementation tasks including:
  - Created directory structure
  - Created all core files (emitter.ts, core.ts)
  - Created all channel handler files for the five subscription types
  - Created all hook function files
  - Created main index.ts file with proper exports
  - Fixed type export issues
  - Updated imports in all dependent files
  - Added clear deprecation notice to the legacy subscription file
- Next steps:
  - Testing the refactored code
  - If testing is successful, remove the original eventBus.ts file