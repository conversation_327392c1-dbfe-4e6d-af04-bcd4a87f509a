import { useEffect } from 'react';
import { AUTH_CONTEXT_CHANGED } from '@/lib/eventBus/constants';
import { emitter } from '@/lib/eventBus';
import { refreshAuthContext as rawRefreshAuthContext } from '@/lib/refresh-auth-context';
import { rbacCache } from '@/lib/permissions-service-client';

// Create a throttled version of refreshAuthContext for more controlled updates
// This helps prevent too many refreshes in quick succession
export const refreshAuthContextThrottled = (function() {
  let throttled = false;
  let queuedCall = false;
  
  return function() {
    // If we're in throttle cooldown period
    if (throttled) {
      // Mark that we have a queued call waiting
      queuedCall = true;
      return;
    }
    
    // Execute immediately if not throttled
    console.log('[refreshAuthContextThrottled] Executing refresh');
    rawRefreshAuthContext();
    throttled = true;
    
    // Set throttle cooldown
    setTimeout(() => {
      // If there was a queued call during the cooldown, execute it now
      if (queuedCall) {
        console.log('[refreshAuthContextThrottled] Executing queued refresh');
        rawRefreshAuthContext();
        queuedCall = false;
      }
      // Reset throttle state
      throttled = false;
    }, 300); // 300ms throttle period
  };
})();

/**
 * A utility to throttle function calls
 */
export function throttle<T extends (...args: unknown[]) => unknown>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle = false;
  let lastFunc: ReturnType<typeof setTimeout> | undefined;
  let lastRan = 0;

  return function throttled(this: unknown, ...args: Parameters<T>): void {
    if (!inThrottle) {
      func.apply(this, args);
      lastRan = Date.now();
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          func.apply(this, args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
}

/**
 * Hook to listen for auth context change events and refresh the auth context.
 * This centralizes the auth context refresh logic to avoid multiple redundant refreshes.
 */
// Global mount flag to ensure the hook is only mounted once across the application
let isGloballyMounted = false;

export function useAuthContextEvents() {
  useEffect(() => {
    // Guard against duplicate mounts - prevent multiple hooks from listening to the same event
    if (isGloballyMounted) {
      console.warn('[useAuthContextEvents] Duplicate hook mount detected and ignored');
      return; // Exit early if already mounted elsewhere
    }
    
    isGloballyMounted = true;
    console.log('[useAuthContextEvents] Setting up event listener for', AUTH_CONTEXT_CHANGED);
    
    const handleAuthContextChanged = (authEvent: any) => {
      console.log('[useAuthContextEvents] Auth context changed event received');

      // Check if this tab initiated the current organization switch
      const currentSwitchId = sessionStorage.getItem('currentOrgSwitchId');
      if (currentSwitchId) {
        console.log('[useAuthContextEvents] Ignoring event - this tab initiated the switch');
        return;
      }

      // Check if user has been redirected by DashboardScenarioManager for current route
      // BUT allow auth refresh for role/status changes even during redirects (store needs to be updated)
      const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
      const redirectKey = `dashboardRedirectInProgress_${currentPath}`;
      const redirectFlag = sessionStorage.getItem(redirectKey);
      const allowedReasons = ['ROLE_CHANGE', 'MEMBER_STATUS_CHANGE'];

      if (redirectFlag && !allowedReasons.includes(authEvent?.reason)) {
        console.log('[useAuthContextEvents] Ignoring auth refresh - redirect in progress for route:', currentPath);
        return;
      }

      if (redirectFlag && allowedReasons.includes(authEvent?.reason)) {
        console.log(`[useAuthContextEvents] Allowing auth refresh for ${authEvent.reason} despite redirect in progress`);
      }

      // Clear RBAC cache immediately when auth context changes
      rbacCache.clear();
      console.log('[useAuthContextEvents] Cleared RBAC cache');

      // Use the globally throttled refreshAuthContext function
      // This ensures we never have multiple parallel refresh calls
      console.log('[useAuthContextEvents] Executing auth context refresh');
      refreshAuthContextThrottled();
    };

    // Listen for the unified auth context changed event
    emitter.on(AUTH_CONTEXT_CHANGED, handleAuthContextChanged);

    return () => {
      emitter.off(AUTH_CONTEXT_CHANGED, handleAuthContextChanged);
      // Reset the global mount flag when component unmounts
      isGloballyMounted = false;
    };
  }, []);
}
