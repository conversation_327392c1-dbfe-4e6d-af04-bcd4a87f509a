import { useEffect, useRef } from 'react'
import { emitter, type Events } from '@/lib/eventBus/emitter'

type EventName = keyof Events

export function useBusEvent<K extends EventName>(
  events: K | K[],
  handler: (payload: Events[K], event: K) => void,
  deps?: unknown[]
) {
  const handlerRef = useRef(handler)
  handlerRef.current = handler

  useEffect(() => {
    const eventList = Array.isArray(events) ? events : [events]
    const wrappers = eventList.map(event => {
      const wrapped = (payload: Events[K]) => handlerRef.current(payload, event)
      emitter.on(event, wrapped)
      return { event, wrapped }
    })

    return () => {
      wrappers.forEach(({ event, wrapped }) => {
        emitter.off(event, wrapped)
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [typeof events === 'string' ? events : events.join(','), ...(deps ?? [])])
}
