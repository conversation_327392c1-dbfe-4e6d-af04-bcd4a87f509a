"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle, Search, Trash2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { useEffect, useState, memo, useCallback } from "react"; // Added memo and useCallback
import { Skeleton } from "@/components/ui/skeleton";
import Image from "next/image";
import { Organization } from "@/types/organization";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toastMessages } from "@/lib/toast-messages";
import { OrganizationTableProps } from "@/types/components/organization/OrganizationTableProps";
import { Restricted } from "@/components/rbac/restricted";
import { useRbacPermission } from "@/hooks/use-rbac-permission";
import { useAllOrganizationEvents } from "@/lib/eventBus";
import type { OrganizationDataChangedEvent } from "@/lib/eventTypes";

// Pagination response interface
interface PaginatedOrganizationsResponse {
  organizations: Organization[];
  totalCount: number;
  page: number;
  pageSize: number;
}

// Loading skeleton component (remains the same)
function TableSkeleton() {
  return (
    <>
      {Array(5)
        .fill(0)
        .map((_, index) => (
          <TableRow key={index}>
            <TableCell>
              <Skeleton className="h-6 w-6 rounded-full" />
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-3">
                <div>
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-24" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-4 w-24" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-6 w-16 rounded-full" />
            </TableCell>
            <TableCell>
              <Skeleton className="h-8 w-8 rounded-full" />
            </TableCell>
          </TableRow>
        ))}
    </>
  );
}

// Pagination component interface
interface TablePaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalOrganizations: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  availablePageSizes?: number[];
}

// Pagination component for organizations table
function TablePagination({
  currentPage,
  totalPages,
  pageSize,
  totalOrganizations,
  onPageChange,
  onPageSizeChange,
  availablePageSizes = [10, 20, 50, 100],
}: TablePaginationProps) {
  if (totalPages === 0) return null;

  return (
    <div className="flex items-center justify-between px-2 py-4 border-t bg-white dark:bg-gray-800 rounded-b-lg shadow">
      <div className="flex-1 text-sm text-gray-600 dark:text-gray-300">
        Showing {Math.min((currentPage - 1) * pageSize + 1, totalOrganizations)} - {Math.min(currentPage * pageSize, totalOrganizations)} of {totalOrganizations} organizations
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Rows per page</p>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize.toString()} />
            </SelectTrigger>
            <SelectContent side="top">
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium text-gray-600 dark:text-gray-300">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// Wrapped with React.memo
export const OrganizationTable = memo(function OrganizationTable({
  initialOrganizations,
}: OrganizationTableProps) { // Using OrganizationTableProps directly
  const permissions = useRbacPermission();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalOrganizations, setTotalOrganizations] = useState(0);

  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true); // Start with loading true for client-side fetching
  const [selectedOrgId, setSelectedOrgId] = useState<string | "all">("all");

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [organizationToDelete, setOrganizationToDelete] =
    useState<Organization | null>(null);

  // Fetch organizations with pagination
  const fetchOrganizations = useCallback(async (page: number, size: number, search?: string) => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', size.toString());
      if (search) {
        params.append('search', search);
      }

      const response = await fetch(`/api/organizations/complete?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch organizations');
      }

      const data: PaginatedOrganizationsResponse = await response.json();
      setOrganizations(data.organizations || []);
      setTotalOrganizations(data.totalCount || 0);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toastMessages.error('Failed to fetch organizations');
      setOrganizations([]);
      setTotalOrganizations(0);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize with data from props or fetch fresh data
  useEffect(() => {
    if (initialOrganizations && initialOrganizations.length > 0) {
      // Use initial data if provided
      setOrganizations(initialOrganizations);
      setTotalOrganizations(initialOrganizations.length);
      setIsLoading(false);
    } else {
      // Fetch fresh data with pagination
      fetchOrganizations(currentPage, pageSize);
    }
  }, [initialOrganizations, fetchOrganizations, currentPage, pageSize]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    fetchOrganizations(page, pageSize, searchQuery);
  }, [fetchOrganizations, pageSize, searchQuery]);

  const handlePageSizeChange = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
    fetchOrganizations(1, size, searchQuery);
  }, [fetchOrganizations, searchQuery]);

  // Search handler with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
      fetchOrganizations(1, pageSize, searchQuery);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchQuery, fetchOrganizations, pageSize]);

  // Use centralized event system instead of direct Supabase subscription
  const handleOrganizationUpdate = async (event: OrganizationDataChangedEvent) => {
    console.log("Developer OrgTable: Real-time update received via centralized event system:", event);

    // Refetch current page when any organization changes
    fetchOrganizations(currentPage, pageSize, searchQuery);

    // Show appropriate toast notification
    // Note: We can't determine the exact operation type from db:organizations:updated events
    // but we can show a generic update message
    toastMessages.organization.updateSuccess();
  };

  useAllOrganizationEvents(handleOrganizationUpdate);

  useEffect(() => {
    console.log("Developer OrgTable - Organizations:", organizations);
    console.log("Developer OrgTable - Current permissions:", permissions);
  }, [organizations, permissions]);

  const handleDeleteClick = (org: Organization) => {
    setOrganizationToDelete(org);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!organizationToDelete) return;
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/organizations/${organizationToDelete.id}`,
        { method: "DELETE" }
      );
      if (!response.ok) throw new Error("Failed to delete organization");
      // Optimistic update handled by real-time event or local state below:
      // setOrganizations(prev => prev.filter(org => org.id !== organizationToDelete.id));
      // The real-time subscription should ideally handle the list update.
      toastMessages.organization.deleteSuccess({
        description: `${organizationToDelete.name} has been successfully deleted.`
      });
    } catch (error) {
      console.error("Error deleting organization:", error);
      toastMessages.organization.deleteError();
    } finally {
      setIsLoading(false);
      setIsDeleteDialogOpen(false);
      setOrganizationToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteDialogOpen(false);
    setOrganizationToDelete(null);
  };

  // Filter organizations by selected org (client-side filtering for org selector)
  const filteredOrganizations = selectedOrgId === "all"
    ? organizations
    : organizations.filter((org) => org.id === selectedOrgId);

  const formatDate = (date: string | null | undefined) => {
    if (!date) return "N/A";
    try { return format(new Date(date), "MMM d, yyyy"); }
    catch (e) { console.error("Error formatting date:", e); return "Invalid date"; }
  };

  // Calculate total pages for pagination
  const totalPages = Math.ceil(totalOrganizations / pageSize) || 1;

  // Breadcrumbs specific to the developer section
  const breadcrumbs = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard/developer">Developer</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Organizations</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <div className="p-6 pt-0">
      <div className="mb-6 space-y-4">
        {breadcrumbs} {/* Updated Breadcrumbs */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search organizations..."
              className="w-full sm:w-[300px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-2 w-full sm:w-auto">
            {/* Organization selector dropdown - crucial for superadmin/developer view */}
            <Select
              value={selectedOrgId}
              onValueChange={(value) => setSelectedOrgId(value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="All Organizations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Organizations</SelectItem>
                {Array.isArray(organizations) && organizations.map((org) => (
                  <SelectItem key={org.id} value={org.id}>
                    {org.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {/* Add Organization button - RBAC for superAdmin/supportAdmin */}
            <Restricted rRoles={["superAdmin", "supportAdmin"]}>
              <Link
                href="/dashboard/developer/create-organization"
                className="whitespace-nowrap"
              >
                <Button>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Organization
                </Button>
              </Link>
            </Restricted>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]"></TableHead>
              <TableHead>Organization</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Updated</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading && filteredOrganizations.length === 0 ? (
              <TableSkeleton />
            ) : filteredOrganizations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No organizations found.
                </TableCell>
              </TableRow>
            ) : (
              filteredOrganizations.map((org) => (
                <TableRow key={org.id}>
                  <TableCell>
                    {org.org_icon ? (
                      <Image
                        src={org.org_icon}
                        alt={org.name}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-xs text-gray-600 dark:text-gray-300">
                        {org.name?.charAt(0).toUpperCase() || 'N'}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{org.name}</div>
                    <div className="text-xs text-gray-500">ID: {org.id}</div>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600 dark:text-gray-300">
                    {formatDate(org.createdAt)}
                  </TableCell>
                  <TableCell className="text-sm text-gray-600 dark:text-gray-300">
                    {formatDate(org.updatedAt)}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={org.isActive ? "default" : "destructive"}
                      className="capitalize"
                    >
                      {org.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {/* Link to a new developer-centric org view/edit page if needed, or a general settings page */}
                      <Link href={`/dashboard/developer/organizations/${org.id}/settings`}>
                        <Button variant="outline" size="sm">
                          Settings
                        </Button>
                      </Link>
                      {/* Delete restricted to superAdmin */}
                      <Restricted rRoles={["superAdmin"]}>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteClick(org)}
                          disabled={isLoading} // Ensure this isLoading reflects specific delete operation if possible
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </Restricted>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        {totalOrganizations > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalOrganizations={totalOrganizations}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              organization &quot;
              <span className="font-medium">
                {organizationToDelete?.name}
              </span>
              &quot; and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-500 hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
});