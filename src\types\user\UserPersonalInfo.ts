/**
 * Extended user personal information interface
 * Contains all personal details including passport information
 */
export interface UserPersonalInfo {
  id: string;
  
  // Basic personal information
  first_name: string | null;
  middle_name: string | null;
  last_name: string | null;
  dob: string | null;
  nationality: string | null;
  sex: string | null;
  height_cm: number | null;
  
  // Additional identity details
  passport_name: string | null;
  birth_name: string | null;
  previous_name: string | null;
  nickname: string | null;
  non_latin_full_name: string | null;
  nationality_birth: string | null;
  place_of_birth: string | null;
  
  // Passport details
  passport_doc_id: string | null;
  passport_country: string | null;
  passport_date_issue: string | null;
  passport_date_expiry: string | null;
  
  // Metadata
  created_at?: string | null;
  updated_at?: string | null;
  updated_by?: string | null;
} 