{"cSpell.words": ["adipisicing", "amet", "Billingpage", "Chaengwattana", "<PERSON><PERSON><PERSON><PERSON>", "Chonburi", "Chor", "cursorrules", "<PERSON><PERSON><PERSON>", "doesn", "ducimus", "<PERSON><PERSON><PERSON>", "Ecom", "elit", "enerate", "extralight", "flagsapi", "flowstep", "flowstepcategories", "flowstepcategory", "flowsteps", "<PERSON><PERSON>", "Genql", "jdhltybpmwuijrxqhzjj", "<PERSON><PERSON><PERSON><PERSON>", "labelcategories", "labelcategory", "<PERSON><PERSON><PERSON>", "laplclsavwpugsxobxbk", "<PERSON><PERSON><PERSON>", "modelcontextprotocol", "molestiae", "nesciunt", "<PERSON><PERSON>", "Officia", "orgaccounting", "orgadmin", "orgclient", "orgmember", "perferendis", "pgbouncer", "Phitsanulok", "<PERSON><PERSON>", "placeat", "plpgsql", "pobox", "Pomorskie", "pooler", "Postgrest", "proname", "Q<PERSON><PERSON>", "repudiandae", "Rhyno", "RORO", "<PERSON><PERSON><PERSON>", "setflow", "shadcn", "sonner", "SQLERRM", "Suanplu", "subcomponents", "<PERSON><PERSON>", "SUPABASE", "Supavisor", "superadmin", "superadmins", "<PERSON><PERSON><PERSON>", "Taskfile", "tempora", "uuidv", "vercel", "vero", "vibesync", "voluptas", "voluptatem", "WCAG", "XVCJ"], "typescript.tsdk": "node_modules\\typescript\\lib"}