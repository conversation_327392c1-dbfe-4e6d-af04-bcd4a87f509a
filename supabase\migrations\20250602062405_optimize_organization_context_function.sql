-- Optimized organization context function to reduce duplicate events
-- This replaces the existing function that makes two separate UPDATE operations
-- with a single UPDATE using CASE to minimize database events

CREATE OR REPLACE FUNCTION public.set_user_organization_context(
  p_user_id UUID,
  p_org_id UUID
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_current_context_org_id UUID;
BEGIN
  -- Check if the requested org is already the current context
  SELECT org_id INTO v_current_context_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  LIMIT 1;

  -- If the requested org is already the current context, do nothing
  IF v_current_context_org_id = p_org_id THEN
    RETURN;
  END IF;

  -- Use a single UPDATE with CASE to minimize database events
  -- This updates both old and new context in one operation
  UPDATE public.organization_members
  SET is_current_context = CASE
    WHEN org_id = p_org_id THEN true
    WHEN org_id = v_current_context_org_id THEN false
    ELSE is_current_context
  END
  WHERE user_id = p_user_id
  AND (org_id = p_org_id OR org_id = v_current_context_org_id);
END;
$$;

-- Grant access to authenticated users
GRANT EXECUTE ON FUNCTION public.set_user_organization_context(UUID, UUID) TO authenticated;