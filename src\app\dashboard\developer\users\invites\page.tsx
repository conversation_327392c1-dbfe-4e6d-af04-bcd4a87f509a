import { withRbac<PERSON>ermission } from "@/lib/rbac/permissions-server";
import { createClient } from "@/lib/supabase/server";
import { RoleId } from "@/lib/rbac/roles";
import DeveloperInvitationsClient from "@/components/invitations/developer-invitations-client";
import { getCurrentUserActiveOrganization } from "@/lib/organization-utils-server";
import { PageProps } from "@/types/app/PageProps";

const DeveloperInvitesPageInternal = async (_props: PageProps) => {
  // Get current organization context for default filtering
  const { organization: currentOrg } = await getCurrentUserActiveOrganization();
  const currentOrgId = currentOrg?.id;

  // Fetch invitations for current organization by default (developer view)
  const supabase = await createClient();

  let query = supabase
    .from('email_invitations')
    .select(`
      id,
      org_id,
      email,
      role_id,
      status,
      created_at,
      expires_at,
      personal_message,
      resend_email_id,
      invited_by
    `)
    .in('role_id', [RoleId.ORGCLIENT, RoleId.ORGACCOUNTING, RoleId.ORGMEMBER, RoleId.ORGADMIN]);

  // Filter by current organization if available
  if (currentOrgId) {
    query = query.eq('org_id', currentOrgId);
  }

  const { data: invitations, error } = await query.order('created_at', { ascending: false });

  // Fetch inviter profiles separately to avoid foreign key join issues
  let enrichedInvitations = invitations || [];
  if (invitations && invitations.length > 0) {
    const inviterIds = [...new Set(invitations.map(inv => inv.invited_by).filter(Boolean))];

    if (inviterIds.length > 0) {
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, full_name')
        .in('id', inviterIds);

      // Map profiles to invitations
      enrichedInvitations = invitations.map(invitation => ({
        ...invitation,
        invited_by_profile: profiles?.filter(p => p.id === invitation.invited_by) || []
      }));
    }
  }

  if (error) {
    console.error('Failed to fetch developer invitations:', error);
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">User Invitations</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage email invitations for users across all organizations. As a developer, you can invite users to any organization.
        </p>
      </div>

      <DeveloperInvitationsClient initialInvitations={enrichedInvitations} />
    </div>
  );
};

const DeveloperInvitesPage = withRbacPermission(
  { rMinRole: "supportAdmin" }, // Accessible by supportAdmin and above
  { redirectTo: "/dashboard" }
)(DeveloperInvitesPageInternal);

export default DeveloperInvitesPage;
