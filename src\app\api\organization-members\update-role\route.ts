import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { canChangeUserRole } from '@/lib/permission-utils'
import type { OrganizationMemberBasic } from '@/types/organization/OrganizationMember'

export async function PATCH(request: Request) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the request body
    const body = await request.json()
    const { orgId, userId, newRoleId } = body

    if (!orgId || !userId || !newRoleId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Verify user has permission to update roles
    const { data: userRole, error: userRoleError } = await supabase
      .from('organization_members')
      .select('org_member_role')
      .eq('org_id', orgId)
      .eq('user_id', user.id)
      .eq('org_member_is_active', true)
      .single()

    if (userRoleError || !userRole) {
      console.error('Error checking permissions:', userRoleError)
      return NextResponse.json({ error: 'You do not have permission to update roles in this organization' }, { status: 403 })
    }

    // Verify target member exists and get their current role
    const { data: targetMember, error: targetMemberError } = await supabase
      .from('organization_members')
      .select('org_member_role, org_member_is_active')
      .eq('org_id', orgId)
      .eq('user_id', userId)
      .single()

    if (targetMemberError || !targetMember) {
      console.error('Error finding target member:', targetMemberError)
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    // Build the member object for permission check
    const memberData: OrganizationMemberBasic = {
      user_id: userId,
      org_id: orgId,
      org_member_role: targetMember.org_member_role,
      org_member_is_active: targetMember.org_member_is_active
    };

    // Check permission using our domain-specific utility
    const hasPermission = canChangeUserRole(
      memberData,
      userRole.org_member_role,
      user.id,
      parseInt(newRoleId)
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions to update roles' }, { status: 403 })
    }

    // Update the member's role
    const { data, error: updateError } = await supabase
      .from('organization_members')
      .update({
        org_member_role: newRoleId,
        org_member_updated_by: user.id,
        updated_at: new Date().toISOString(),
      })
      .eq('org_id', orgId)
      .eq('user_id', userId)
      .select()

    if (updateError) {
      console.error('Error updating role:', updateError)
      return NextResponse.json({ error: 'Failed to update role' }, { status: 500 })
    }

    console.log(`Role for user ${userId} in org ${orgId} updated to ${newRoleId}.`);

    return NextResponse.json({
      message: 'Role updated successfully',
      data
    })
  } catch (error) {
    console.error('Unexpected error in update-role endpoint:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 