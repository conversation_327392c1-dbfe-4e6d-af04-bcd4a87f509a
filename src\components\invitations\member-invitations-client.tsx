'use client'

import { useState } from 'react'
import { InvitationTable, InvitationTableBusinessRules, EmailInvitation } from '@/components/shared/invitation-table'
import { RoleId } from '@/lib/rbac/roles'
import { toast } from 'sonner'
import { fetchInvitationsWithProfiles } from '@/lib/invitations/invitation-utils'
import SendInvitationModal from './send-invitation-modal'
import { deleteEmailInvitation } from '@/app/actions/delete-email-invitation'
import { resendEmailInvitation } from '@/app/actions/resend-email-invitation'
import { useAuthContextStore } from '@/stores/useAuthContextStore'

interface MemberInvitationsClientProps {
  initialInvitations: EmailInvitation[]
}

export default function MemberInvitationsClient({ initialInvitations }: MemberInvitationsClientProps) {
  const [invitations, setInvitations] = useState<EmailInvitation[]>(initialInvitations)
  const [isLoading, setIsLoading] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Get current organization context
  const { orgId: currentOrgId } = useAuthContextStore()

  // Define business rules for organization member invitations
  const orgMemberInviteBusinessRules: InvitationTableBusinessRules = {
    inviteMinRole: "orgAdmin", // orgAdmin+ can invite members
    deleteMinRole: "orgAdmin", // orgAdmin+ can delete invitations
    resendMinRole: "orgAdmin", // orgAdmin+ can resend failed invitations
    allowedRoles: ["orgClient", "orgAccounting", "orgMember", "orgAdmin"], // Can invite multiple roles
    maxAssignableRole: "orgAdmin", // Cannot assign roles above orgAdmin
  }

  const refreshInvitations = async () => {
    try {
      setIsLoading(true)

      // SECURITY: Only fetch invitations for current organization context
      if (!currentOrgId) {
        console.error('No current organization context available')
        toast.error('No organization context available')
        return
      }

      const data = await fetchInvitationsWithProfiles(currentOrgId, [RoleId.ORGCLIENT, RoleId.ORGACCOUNTING, RoleId.ORGMEMBER, RoleId.ORGADMIN])
      setInvitations(data)
    } catch (error) {
      console.error('Error refreshing invitations:', error)
      toast.error('Failed to refresh invitations')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInviteUser = () => {
    setIsModalOpen(true)
  }

  const handleDeleteInvitation = async (invitationId: string) => {
    try {
      const result = await deleteEmailInvitation(invitationId)

      if (result.success) {
        toast.success('Invitation deleted successfully')
        await refreshInvitations()
      } else {
        toast.error(result.error || 'Failed to delete invitation')
      }
    } catch (error) {
      console.error('Error deleting invitation:', error)
      toast.error('Failed to delete invitation')
    }
  }

  const handleResendInvitation = async (invitationId: string) => {
    try {
      const result = await resendEmailInvitation(invitationId)

      if (result.success) {
        toast.success('Invitation resent successfully')
        await refreshInvitations()
      } else {
        toast.error(result.error || 'Failed to resend invitation')
      }
    } catch (error) {
      console.error('Error resending invitation:', error)
      toast.error('Failed to resend invitation')
    }
  }

  return (
    <>
      <InvitationTable
        organizationIdScope="CURRENT_CONTEXT"
        businessRules={orgMemberInviteBusinessRules}
        showOrganizationColumn={false} // Not needed for current context
        tableTitle="Organization Member Invitations"
        invitations={invitations}
        isLoading={isLoading}
        onInviteUser={handleInviteUser}
        onDeleteInvitation={handleDeleteInvitation}
        onResendInvitation={handleResendInvitation}
      />

      <SendInvitationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={refreshInvitations}
        allowedRoles={['orgClient', 'orgAccounting', 'orgMember', 'orgAdmin']}
        orgId={currentOrgId || undefined} // Pass current organization context
      />
    </>
  )
}
