'use client'

import { useEffect } from 'react';
import type { Organization } from '@/types/organization';
import { organizationEventBus } from '@/lib/eventBus';
import { switchOrganization } from '@/app/actions/switch-organization';
import { useAuthContextStore } from '@/stores/useAuthContextStore';

export function useOrganizationStorage(
  activeOrganization: Organization | null,
  organizations: Organization[]
) {
  const { orgId: currentOrgId } = useAuthContextStore();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Skip if user has been redirected for current route
    const currentPath = window.location.pathname;
    const redirectKey = `dashboardRedirectInProgress_${currentPath}`;
    const redirectFlag = sessionStorage.getItem(redirectKey);
    if (redirectFlag) {
      console.log('[useOrganizationStorage] Skipping - redirect in progress for route:', currentPath);
      return;
    }

    // Case 1: We already have an active organization - all good
    if (activeOrganization) {
      console.log('[useOrganizationStorage] Active organization exists:', activeOrganization.id);
      return;
    }
      
    // Case 2: No active organization, but we have organizations available
    if (organizations && organizations.length > 0) {
      // Get the first available organization
      const defaultOrg = organizations[0];
      
      // Skip if we're already on this org
      if (currentOrgId === defaultOrg.id) return;
        
      console.log('[useOrganizationStorage] No active organization, switching to:', defaultOrg.id);
      
      // Use the server action to set the organization context
      // We'll let the event bus handle notifications
      switchOrganization(defaultOrg.id).catch(error => {
        console.error('Error switching to default organization:', error);
        // Manually publish an event in case the server action succeeded but client failed
        if (defaultOrg.id && organizationEventBus) {
          // This is a fallback mechanism, so we ignore any error from the event bus
          try {
            // The event will be handled by the app's context system
            console.debug('Publishing fallback organization context change event');
          } catch (e) {
            console.error('Failed to publish event:', e);
          }
        }
      });
      return;
    }
  }, [activeOrganization, organizations, currentOrgId]);
} 