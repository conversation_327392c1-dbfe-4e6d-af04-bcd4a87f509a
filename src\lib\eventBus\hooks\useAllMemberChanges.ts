'use client'

import type { AnyMemberChangeEvent } from '@/lib/eventTypes'
import { useBusEvent } from '@/lib/useBusEvent'

/**
 * Subscribe to all member changes for an organization
 */
export function useAllMemberChanges(
  orgId: string | null,
  handler: (event: AnyMemberChangeEvent) => void
): void {
  useBusEvent(
    'organization:member:changed',
    (payload) => {
      // Optionally filter by orgId if your events are org-specific
      if (!orgId || payload.orgId !== orgId) return
      handler(payload)
    },
    [orgId, handler]
  )
}