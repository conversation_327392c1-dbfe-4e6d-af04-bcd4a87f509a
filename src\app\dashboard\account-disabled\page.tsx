"use client";

import React from "react";
import { useDashboard } from "@/components/providers/dashboard-provider";
import Link from "next/link";

export default function AccountDisabledPage() {
  const { activeOrgName } = useDashboard();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">Account Disabled</h1>
        
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-yellow-700">
            {activeOrgName ? (
              <>
                Your account in the organization &ldquo;{activeOrgName}&rdquo; has been disabled.
                Please contact an administrator from this organization to reactivate your account, or switch to another organization if you have access to multiple organizations.
              </>
            ) : (
              <>
                Your account has been disabled in the current organization.
                Please contact an administrator to reactivate your account or switch to another organization.
              </>
            )}
          </p>
        </div>
        
        <div className="flex justify-center space-x-4">
          <Link
            href="/dashboard"
            className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md"
          >
            Switch Organization
          </Link>
          <Link
            href="/auth/signout"
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md"
          >
            Sign Out
          </Link>
        </div>
      </div>
    </div>
  );
} 