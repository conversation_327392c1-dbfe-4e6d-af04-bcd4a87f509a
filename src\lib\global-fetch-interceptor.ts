import { handleAuthenticationError } from '@/lib/auth-error-handler';

// Store the original fetch function
const originalFetch = globalThis.fetch;

/**
 * Global fetch interceptor that handles 401 authentication errors
 * This ensures that any fetch call in the application will redirect to login on session expiry
 */
async function interceptedFetch(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
  try {
    const response = await originalFetch(input, init);
    // Check for 401 errors
    if (response.status === 401) {
      // Clone the response to read the body without consuming it
      const clonedResponse = response.clone();

      try {
        const errorText = await clonedResponse.text();
        const error = new Error(`HTTP 401: ${errorText || 'Unauthorized'}`);
        handleAuthenticationError(error, 401);
      } catch (e) {
        // If we can't read the response body, still handle the 401
        const error = new Error('HTTP 401: Unauthorized');
        handleAuthenticationError(error, 401);
      }
    }

    return response;
  } catch (error) {
    // Handle network errors and other exceptions
    if (error instanceof Error) {
      // Only handle authentication errors, let other errors bubble up
      if (error.message.includes('401') ||
          error.message.includes('Authentication required') ||
          error.message.includes('Unauthorized')) {
        handleAuthenticationError(error);
      }
    }
    throw error;
  }
}

/**
 * Initialize the global fetch interceptor
 * This should be called once at application startup
 */
export function initializeGlobalFetchInterceptor() {
  if (typeof window !== 'undefined' && globalThis.fetch === originalFetch) {
    console.log('[GlobalFetchInterceptor] Initializing global fetch interceptor');
    globalThis.fetch = interceptedFetch;
  }
}

/**
 * Restore the original fetch function (useful for testing)
 */
export function restoreOriginalFetch() {
  if (typeof window !== 'undefined') {
    globalThis.fetch = originalFetch;
  }
}
