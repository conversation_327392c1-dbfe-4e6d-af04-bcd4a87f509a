# Email Invitation API Migration Plan

## Executive Summary

This document outlines the migration plan to refactor the current email invitation system from server actions to dedicated API routes. The goal is to achieve better separation of concerns, modularity, and granular control over CRUD operations while maintaining the existing RBAC system and business rules.

## Current State Assessment

### Existing Server Actions
- `src/app/actions/send-email-invitation.ts` - Create invitations with email sending
- `src/app/actions/delete-email-invitation.ts` - Delete invitations with RBAC validation
- `src/app/actions/resend-email-invitation.ts` - Resend invitations with new tokens
- `src/app/actions/accept-email-invitation.ts` - Accept/decline invitations (onboarding flow)

### Target API Routes (Currently Empty)
- `src/app/api/email-invitations/create-invite/route.ts` - POST for creating invitations
- `src/app/api/email-invitations/delete-invite/route.ts` - DELETE for removing invitations
- `src/app/api/email-invitations/update-invite/route.ts` - PATCH for updating/resending invitations
- `src/app/api/email-invitations/read-invite/route.ts` - GET for fetching invitations

### Affected Components
- `src/app/dashboard/clients/invites/page.tsx` - Client invitation management
- `src/app/dashboard/admin/members/invites/page.tsx` - Member invitation management
- `src/app/dashboard/developer/users/invites/page.tsx` - Developer invitation management
- `src/components/shared/invitation-table.tsx` - Shared invitation table component
- `src/components/invitations/client-invitations-client.tsx` - Client-specific invitation UI
- `src/components/invitations/member-invitations-client.tsx` - Member-specific invitation UI
- `src/components/invitations/developer-invitations-client.tsx` - Developer-specific invitation UI

### Current RBAC Business Rules
1. **Client Invitations**: `orgMember+` can invite `orgClient` roles only
2. **Member Invitations**: `orgAdmin+` can invite `orgClient`, `orgAccounting`, `orgMember`, `orgAdmin` roles
3. **Developer Invitations**: `supportAdmin+` can invite any role to any organization

## Migration Strategy

### Phase 1: API Route Implementation
Implement the four dedicated API routes with proper RBAC validation, error handling, and response patterns consistent with existing API routes.

### Phase 2: Component Migration
Update all invitation components to use the new API routes instead of server actions.

### Phase 3: Server Action Cleanup
Remove or deprecate the old server actions once migration is complete.

## Detailed Implementation Plan

### Task 1: Implement Create Invitation API Route
**File**: `src/app/api/email-invitations/create-invite/route.ts`
**Priority**: High
**Dependencies**: None

#### Requirements:
- Accept POST requests with invitation data (orgId, email, roleId, personalMessage)
- Validate input using existing Zod schemas
- Implement RBAC permission checking using `checkRbacPermission`
- Support role-specific business rules (client vs member vs developer permissions)
- Generate secure tokens and create database records
- Send emails using existing Resend integration
- Return consistent API responses with proper error handling
- Follow existing API route patterns for authentication and authorization

#### RBAC Implementation:
```typescript
// Base permission check
const hasPermission = await checkRbacPermission({
  crMinRole: 'orgMember', // Base permission
  orgContext: 'current'
})

// Additional role-specific validation based on request context
// - Client pages: Can only invite orgClient roles
// - Member pages: orgAdmin+ can invite orgClient, orgAccounting, orgMember, orgAdmin
// - Developer pages: supportAdmin+ can invite any role to any org
```

### Task 2: Implement Delete Invitation API Route
**File**: `src/app/api/email-invitations/delete-invite/route.ts`
**Priority**: High
**Dependencies**: None

#### Requirements:
- Accept DELETE requests with invitation ID
- Validate invitation exists and belongs to accessible organization
- Implement RBAC permission checking for deletion rights
- Prevent deletion of accepted/added invitations
- Return appropriate success/error responses
- Follow existing API route patterns

#### RBAC Implementation:
```typescript
const hasPermission = await checkRbacPermission({
  rdMinRole: 'orgMember', // Base delete permission
  orgContext: 'current'
})
```

### Task 3: Implement Update/Resend Invitation API Route
**File**: `src/app/api/email-invitations/update-invite/route.ts`
**Priority**: High
**Dependencies**: None

#### Requirements:
- Accept PATCH requests for resending invitations
- Generate new secure tokens and update expiry dates
- Send new invitation emails
- Update invitation status and tracking information
- Implement RBAC permission checking for update rights
- Handle failed email scenarios gracefully

#### RBAC Implementation:
```typescript
const hasPermission = await checkRbacPermission({
  cruMinRole: 'orgMember', // Base update permission
  orgContext: 'current'
})
```

### Task 4: Implement Read Invitation API Route
**File**: `src/app/api/email-invitations/read-invite/route.ts`
**Priority**: Medium
**Dependencies**: None

#### Requirements:
- Accept GET requests with optional filtering parameters
- Support organization-scoped queries (current org vs all orgs)
- Implement pagination and sorting
- Return enriched invitation data with organization and profile information
- Implement RBAC permission checking for read access
- Support different access patterns for client/member/developer views

#### RBAC Implementation:
```typescript
const hasPermission = await checkRbacPermission({
  rMinRole: 'orgMember', // Base read permission
  orgContext: 'current' // or 'any' for developer views
})
```

### Task 5: Update Client Invitation Components
**Files**:
- `src/components/invitations/client-invitations-client.tsx`
- `src/app/dashboard/clients/invites/page.tsx`
**Priority**: High
**Dependencies**: Task 1, 2, 3, 4

#### Requirements:
- Replace server action calls with API route calls
- Update error handling to work with API responses
- Maintain existing UI/UX patterns
- Preserve RBAC business rules in component logic
- Update loading states and error messages

#### Changes:
```typescript
// Replace server action imports
// OLD: import { sendEmailInvitation } from '@/app/actions/send-email-invitation'
// NEW: Use fetch() calls to API routes

// Update invitation creation
const response = await fetch('/api/email-invitations/create-invite', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(invitationData)
})
```

### Task 6: Update Member Invitation Components
**Files**:
- `src/components/invitations/member-invitations-client.tsx`
- `src/app/dashboard/admin/members/invites/page.tsx`
**Priority**: High
**Dependencies**: Task 1, 2, 3, 4

#### Requirements:
- Replace server action calls with API route calls
- Update error handling to work with API responses
- Maintain existing UI/UX patterns
- Preserve RBAC business rules in component logic
- Update loading states and error messages

### Task 7: Update Developer Invitation Components
**Files**:
- `src/components/invitations/developer-invitations-client.tsx`
- `src/app/dashboard/developer/users/invites/page.tsx`
**Priority**: High
**Dependencies**: Task 1, 2, 3, 4

#### Requirements:
- Replace server action calls with API route calls
- Update error handling to work with API responses
- Maintain existing UI/UX patterns
- Preserve RBAC business rules in component logic
- Support cross-organization invitation management
- Update loading states and error messages

### Task 8: Update Shared Invitation Table Component
**File**: `src/components/shared/invitation-table.tsx`
**Priority**: Medium
**Dependencies**: Task 1, 2, 3, 4

#### Requirements:
- Update callback functions to use API routes
- Maintain existing RBAC permission checking patterns
- Preserve business rule validation
- Update error handling and user feedback

### Task 9: Server Action Deprecation
**Files**:
- `src/app/actions/send-email-invitation.ts`
- `src/app/actions/delete-email-invitation.ts`
- `src/app/actions/resend-email-invitation.ts`
**Priority**: Low
**Dependencies**: Task 5, 6, 7, 8

#### Requirements:
- Add deprecation warnings to server actions
- Update documentation to reference new API routes
- Plan removal timeline
- Ensure no remaining dependencies

## Technical Implementation Details

### API Route Patterns

#### Authentication & Authorization Pattern
```typescript
export async function POST(request: NextRequest) {
  try {
    // 1. Get authenticated user
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // 2. Check RBAC permissions
    const hasPermission = await checkRbacPermission({
      crMinRole: 'orgMember',
      orgContext: 'current'
    })

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // 3. Business logic implementation
    // ...

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
```

#### Input Validation Pattern
```typescript
import { z } from 'zod'

const createInviteSchema = z.object({
  orgId: z.string().uuid(),
  email: z.string().email(),
  roleId: z.number().int().min(1).max(6),
  personalMessage: z.string().max(500).optional(),
})

// In route handler
const body = await request.json()
const validatedData = createInviteSchema.parse(body)
```

#### Response Pattern
```typescript
// Success response
return NextResponse.json({
  success: true,
  data: invitation,
  message: 'Invitation created successfully'
}, { status: 201 })

// Error response
return NextResponse.json({
  success: false,
  error: 'Validation failed',
  details: validationErrors
}, { status: 400 })
```

### Component Update Patterns

#### API Call Pattern
```typescript
const createInvitation = async (data: InvitationData) => {
  try {
    setIsLoading(true)

    const response = await fetch('/api/email-invitations/create-invite', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.error || 'Failed to create invitation')
    }

    toast.success('Invitation sent successfully')
    await refreshInvitations()

  } catch (error) {
    console.error('Error creating invitation:', error)
    toast.error(error.message || 'Failed to create invitation')
  } finally {
    setIsLoading(false)
  }
}
```

#### Error Handling Pattern
```typescript
const handleApiError = (error: any, defaultMessage: string) => {
  const message = error?.message || defaultMessage
  toast.error(message)
  console.error('API Error:', error)
}
```

## Security Considerations

### RBAC Validation
- All API routes must implement proper RBAC validation using `checkRbacPermission`
- Business rules must be enforced at both API and component levels
- Organization context must be properly validated for all operations

### Input Validation
- All inputs must be validated using Zod schemas
- Email addresses must be validated and sanitized
- Role IDs must be validated against allowed values

### Token Security
- Continue using SHA-256 hashed tokens in database
- Generate cryptographically secure tokens using `randomBytes`
- Implement proper token expiry validation

### Rate Limiting
- Consider implementing rate limiting for invitation creation
- Monitor for abuse patterns in invitation sending

## Testing Strategy

### Unit Tests
- Test each API route with various permission scenarios
- Test input validation and error handling
- Test RBAC business rule enforcement

### Integration Tests
- Test complete invitation flow from creation to acceptance
- Test component integration with new API routes
- Test error scenarios and edge cases

### Manual Testing
- Test all three invitation contexts (client, member, developer)
- Verify RBAC permissions work correctly
- Test email sending and token generation

## Migration Timeline

### Week 1: API Route Implementation
- Complete Tasks 1-4 (API route implementation)
- Unit testing for API routes
- Documentation updates

### Week 2: Component Migration
- Complete Tasks 5-8 (component updates)
- Integration testing
- Manual testing of all flows

### Week 3: Cleanup and Validation
- Complete Task 9 (server action deprecation)
- Final testing and validation
- Performance monitoring

## Success Criteria

1. All invitation CRUD operations work through dedicated API routes
2. RBAC business rules are properly enforced
3. No regression in functionality or user experience
4. Improved separation of concerns and code modularity
5. Consistent error handling and response patterns
6. Proper security validation at all levels

## Rollback Plan

If issues arise during migration:
1. Revert component changes to use server actions
2. Keep API routes for future use
3. Address issues and retry migration
4. Maintain backward compatibility during transition

## Post-Migration Benefits

1. **Modularity**: Clear separation between API logic and UI components
2. **Testability**: Easier to unit test API routes independently
3. **Reusability**: API routes can be used by multiple clients
4. **Consistency**: Standardized API patterns across the application
5. **Monitoring**: Better ability to monitor and log API usage
6. **Performance**: Potential for better caching and optimization

---

## Scratchpad: Actionable Tasks

### ✅ COMPLETED
- [x] Assessment of current invitation system
- [x] Analysis of existing RBAC patterns
- [x] Documentation of migration plan

### 🔄 IN PROGRESS
- [ ] None currently

### 📋 TODO - Phase 1: API Route Implementation

#### Task 1.1: Create Invitation API Route
**File**: `src/app/api/email-invitations/create-invite/route.ts`
**Estimated Time**: 4 hours
**Status**: Not Started

**Checklist**:
- [ ] Implement POST handler with authentication
- [ ] Add Zod validation schema for input
- [ ] Implement RBAC permission checking
- [ ] Add role-specific business rule validation
- [ ] Implement secure token generation
- [ ] Add database insertion logic
- [ ] Integrate email sending with Resend
- [ ] Add proper error handling and logging
- [ ] Test with different user roles and permissions

**Key Code Patterns to Implement**:
```typescript
// Authentication pattern from existing API routes
const { data: { user }, error: userError } = await supabase.auth.getUser()

// RBAC pattern from existing routes
const hasPermission = await checkRbacPermission({
  crMinRole: 'orgMember',
  orgContext: 'current'
})

// Input validation pattern
const createInviteSchema = z.object({
  orgId: z.string().uuid(),
  email: emailSchema, // Reuse existing email validation
  roleId: z.number().int().min(1).max(6),
  personalMessage: z.string().max(500).optional(),
})
```

#### Task 1.2: Delete Invitation API Route
**File**: `src/app/api/email-invitations/delete-invite/route.ts`
**Estimated Time**: 2 hours
**Status**: Not Started

**Checklist**:
- [ ] Implement DELETE handler with authentication
- [ ] Add invitation ID validation
- [ ] Implement RBAC permission checking
- [ ] Add invitation existence and ownership validation
- [ ] Prevent deletion of accepted/added invitations
- [ ] Add proper error handling and logging
- [ ] Test with different user roles and permissions

#### Task 1.3: Update/Resend Invitation API Route
**File**: `src/app/api/email-invitations/update-invite/route.ts`
**Estimated Time**: 3 hours
**Status**: Not Started

**Checklist**:
- [ ] Implement PATCH handler with authentication
- [ ] Add invitation ID validation
- [ ] Implement RBAC permission checking
- [ ] Add new token generation logic
- [ ] Update expiry dates and status
- [ ] Integrate email resending with Resend
- [ ] Handle failed email scenarios
- [ ] Add proper error handling and logging
- [ ] Test with different user roles and permissions

#### Task 1.4: Read Invitation API Route
**File**: `src/app/api/email-invitations/read-invite/route.ts`
**Estimated Time**: 3 hours
**Status**: Not Started

**Checklist**:
- [ ] Implement GET handler with authentication
- [ ] Add query parameter validation (pagination, filtering)
- [ ] Implement RBAC permission checking
- [ ] Add organization-scoped queries
- [ ] Implement pagination and sorting
- [ ] Enrich data with organization and profile information
- [ ] Support different access patterns (current org vs all orgs)
- [ ] Add proper error handling and logging
- [ ] Test with different user roles and permissions

### 📋 TODO - Phase 2: Component Migration

#### Task 2.1: Update Client Invitation Components
**Files**:
- `src/components/invitations/client-invitations-client.tsx`
- `src/app/dashboard/clients/invites/page.tsx`
**Estimated Time**: 3 hours
**Status**: Not Started
**Dependencies**: Tasks 1.1, 1.2, 1.3, 1.4

**Checklist**:
- [ ] Replace `sendEmailInvitation` server action with API call
- [ ] Replace `deleteEmailInvitation` server action with API call
- [ ] Replace `resendEmailInvitation` server action with API call
- [ ] Update error handling for API responses
- [ ] Update loading states and user feedback
- [ ] Test client invitation flow end-to-end
- [ ] Verify RBAC business rules still work
- [ ] Update any TypeScript types if needed

#### Task 2.2: Update Member Invitation Components
**Files**:
- `src/components/invitations/member-invitations-client.tsx`
- `src/app/dashboard/admin/members/invites/page.tsx`
**Estimated Time**: 3 hours
**Status**: Not Started
**Dependencies**: Tasks 1.1, 1.2, 1.3, 1.4

**Checklist**:
- [ ] Replace server action calls with API route calls
- [ ] Update error handling for API responses
- [ ] Update loading states and user feedback
- [ ] Test member invitation flow end-to-end
- [ ] Verify RBAC business rules still work
- [ ] Update any TypeScript types if needed

#### Task 2.3: Update Developer Invitation Components
**Files**:
- `src/components/invitations/developer-invitations-client.tsx`
- `src/app/dashboard/developer/users/invites/page.tsx`
**Estimated Time**: 4 hours
**Status**: Not Started
**Dependencies**: Tasks 1.1, 1.2, 1.3, 1.4

**Checklist**:
- [ ] Replace server action calls with API route calls
- [ ] Update error handling for API responses
- [ ] Update loading states and user feedback
- [ ] Test developer invitation flow end-to-end
- [ ] Verify cross-organization invitation management works
- [ ] Verify RBAC business rules still work
- [ ] Update any TypeScript types if needed

#### Task 2.4: Update Shared Invitation Table Component
**File**: `src/components/shared/invitation-table.tsx`
**Estimated Time**: 2 hours
**Status**: Not Started
**Dependencies**: Tasks 1.1, 1.2, 1.3, 1.4

**Checklist**:
- [ ] Update callback function signatures if needed
- [ ] Ensure RBAC permission checking still works
- [ ] Update error handling patterns
- [ ] Test with all three invitation contexts
- [ ] Verify business rule validation still works

### 📋 TODO - Phase 3: Cleanup and Validation

#### Task 3.1: Server Action Deprecation
**Files**:
- `src/app/actions/send-email-invitation.ts`
- `src/app/actions/delete-email-invitation.ts`
- `src/app/actions/resend-email-invitation.ts`
**Estimated Time**: 1 hour
**Status**: Not Started
**Dependencies**: Tasks 2.1, 2.2, 2.3, 2.4

**Checklist**:
- [ ] Add deprecation warnings to server actions
- [ ] Update JSDoc comments with migration notes
- [ ] Verify no remaining dependencies exist
- [ ] Plan removal timeline
- [ ] Update any remaining documentation

#### Task 3.2: Integration Testing
**Estimated Time**: 4 hours
**Status**: Not Started
**Dependencies**: All previous tasks

**Checklist**:
- [ ] Test complete invitation flow for client context
- [ ] Test complete invitation flow for member context
- [ ] Test complete invitation flow for developer context
- [ ] Test RBAC permissions across all contexts
- [ ] Test error scenarios and edge cases
- [ ] Test email sending and token generation
- [ ] Verify no regressions in existing functionality
- [ ] Performance testing of new API routes

### 🎯 PRIORITY MATRIX

**High Priority (Week 1)**:
1. Task 1.1 - Create Invitation API Route
2. Task 1.2 - Delete Invitation API Route
3. Task 1.3 - Update/Resend Invitation API Route
4. Task 1.4 - Read Invitation API Route

**Medium Priority (Week 2)**:
5. Task 2.1 - Update Client Invitation Components
6. Task 2.2 - Update Member Invitation Components
7. Task 2.3 - Update Developer Invitation Components
8. Task 2.4 - Update Shared Invitation Table Component

**Low Priority (Week 3)**:
9. Task 3.1 - Server Action Deprecation
10. Task 3.2 - Integration Testing

### 🚨 RISK FACTORS

1. **RBAC Complexity**: Different permission models across client/member/developer contexts
2. **Email Integration**: Ensuring Resend integration works correctly in API routes
3. **Token Security**: Maintaining secure token generation and validation
4. **Database Consistency**: Ensuring proper transaction handling
5. **Component Dependencies**: Managing dependencies between components during migration

### 📝 NOTES

- Keep the onboarding flow (`accept-email-invitation.ts`) unchanged as specified
- Maintain backward compatibility during migration
- Test thoroughly with different user roles and organization contexts
- Monitor for any performance impacts
- Document any breaking changes or new patterns