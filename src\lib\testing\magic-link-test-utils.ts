/**
 * Magic Link Testing Utilities
 * 
 * Comprehensive testing utilities for validating the hybrid magic link implementation.
 * These utilities help ensure both legacy and custom magic link systems work correctly.
 */

import { testAdminClient } from '@/lib/supabase/admin';
import { validateEnvironment } from '@/lib/config/env-validation';
import { isValidMagicLinkUrl } from '@/lib/email/admin-magic-link-service';

/**
 * Test result interface
 */
interface TestResult {
  name: string;
  success: boolean;
  message: string;
  duration?: number;
  details?: Record<string, any>;
}

/**
 * Test suite results
 */
interface TestSuiteResult {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: TestResult[];
  duration: number;
}

/**
 * Run a single test with timing
 */
async function runTest(name: string, testFn: () => Promise<boolean | { success: boolean; message?: string; details?: any }>): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const result = await testFn();
    const duration = Date.now() - startTime;
    
    if (typeof result === 'boolean') {
      return {
        name,
        success: result,
        message: result ? 'Test passed' : 'Test failed',
        duration,
      };
    }
    
    return {
      name,
      success: result.success,
      message: result.message || (result.success ? 'Test passed' : 'Test failed'),
      duration,
      details: result.details,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      name,
      success: false,
      message: `Test threw exception: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration,
    };
  }
}

/**
 * Test environment configuration
 */
export async function testEnvironmentConfiguration(): Promise<TestResult> {
  return runTest('Environment Configuration', async () => {
    const result = validateEnvironment();
    
    if (!result.success) {
      return {
        success: false,
        message: `Environment validation failed: ${result.errors?.join(', ')}`,
        details: { errors: result.errors },
      };
    }
    
    return {
      success: true,
      message: 'Environment configuration is valid',
      details: { 
        warnings: result.warnings,
        customMagicLinksEnabled: result.data?.USE_CUSTOM_MAGIC_LINKS === 'true',
      },
    };
  });
}

/**
 * Test admin client connectivity
 */
export async function testAdminClientConnectivity(): Promise<TestResult> {
  return runTest('Admin Client Connectivity', async () => {
    const isWorking = await testAdminClient();
    
    return {
      success: isWorking,
      message: isWorking 
        ? 'Admin client can connect and access admin API' 
        : 'Admin client failed to connect or access admin API',
    };
  });
}

/**
 * Test magic link URL validation
 */
export async function testMagicLinkUrlValidation(): Promise<TestResult> {
  return runTest('Magic Link URL Validation', async () => {
    const validUrl = 'https://project.supabase.co/auth/v1/verify?token_hash=abc123&type=magiclink&redirect_to=https://example.com';
    const invalidUrl = 'https://example.com/invalid';
    
    const validResult = isValidMagicLinkUrl(validUrl);
    const invalidResult = isValidMagicLinkUrl(invalidUrl);
    
    if (validResult && !invalidResult) {
      return {
        success: true,
        message: 'Magic link URL validation works correctly',
        details: { validUrl, invalidUrl },
      };
    }
    
    return {
      success: false,
      message: `Magic link URL validation failed: valid=${validResult}, invalid=${invalidResult}`,
      details: { validUrl, invalidUrl, validResult, invalidResult },
    };
  });
}

/**
 * Test API endpoint accessibility
 */
export async function testApiEndpointAccessibility(): Promise<TestResult> {
  return runTest('API Endpoint Accessibility', async () => {
    try {
      // Test that the API endpoint exists and returns proper error for missing data
      const response = await fetch('/api/auth/magic-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}), // Invalid request to test error handling
      });
      
      const data = await response.json();
      
      // Should return 400 for invalid request
      if (response.status === 400 && !data.success) {
        return {
          success: true,
          message: 'API endpoint is accessible and handles invalid requests correctly',
          details: { status: response.status, response: data },
        };
      }
      
      return {
        success: false,
        message: `API endpoint returned unexpected response: ${response.status}`,
        details: { status: response.status, response: data },
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to reach API endpoint: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  });
}

/**
 * Test rate limiting functionality
 */
export async function testRateLimiting(): Promise<TestResult> {
  return runTest('Rate Limiting', async () => {
    const testEmail = '<EMAIL>';
    const requests = [];
    
    // Make multiple rapid requests to trigger rate limiting
    for (let i = 0; i < 6; i++) {
      requests.push(
        fetch('/api/auth/magic-link', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testEmail,
            website: '', // Honeypot
            captchaToken: 'test-token',
          }),
        })
      );
    }
    
    const responses = await Promise.all(requests);
    const statuses = responses.map(r => r.status);
    
    // Should have at least one 429 (rate limited) response
    const hasRateLimit = statuses.includes(429);
    
    return {
      success: hasRateLimit,
      message: hasRateLimit 
        ? 'Rate limiting is working correctly' 
        : 'Rate limiting may not be working - no 429 responses received',
      details: { statuses, totalRequests: requests.length },
    };
  });
}

/**
 * Test honeypot protection
 */
export async function testHoneypotProtection(): Promise<TestResult> {
  return runTest('Honeypot Protection', async () => {
    const response = await fetch('/api/auth/magic-link', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        website: 'bot-filled-this', // Honeypot triggered
        captchaToken: 'test-token',
      }),
    });
    
    const data = await response.json();
    
    // Should return 200 with success=true to not reveal honeypot
    if (response.status === 200 && data.success === true) {
      return {
        success: true,
        message: 'Honeypot protection is working correctly',
        details: { status: response.status, response: data },
      };
    }
    
    return {
      success: false,
      message: 'Honeypot protection may not be working correctly',
      details: { status: response.status, response: data },
    };
  });
}

/**
 * Run comprehensive test suite
 */
export async function runMagicLinkTestSuite(): Promise<TestSuiteResult> {
  const startTime = Date.now();
  const tests = [
    testEnvironmentConfiguration,
    testAdminClientConnectivity,
    testMagicLinkUrlValidation,
    testApiEndpointAccessibility,
    testRateLimiting,
    testHoneypotProtection,
  ];
  
  console.info('🧪 Running Magic Link Test Suite...');
  
  const results: TestResult[] = [];
  
  for (const test of tests) {
    const result = await test();
    results.push(result);
    
    const status = result.success ? '✅' : '❌';
    console.info(`${status} ${result.name}: ${result.message} (${result.duration}ms)`);
    
    if (result.details) {
      console.debug(`   Details:`, result.details);
    }
  }
  
  const duration = Date.now() - startTime;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = results.filter(r => !r.success).length;
  
  const suiteResult: TestSuiteResult = {
    suiteName: 'Magic Link Hybrid Implementation',
    totalTests: results.length,
    passedTests,
    failedTests,
    results,
    duration,
  };
  
  console.info(`\n📊 Test Suite Results:`);
  console.info(`   Total Tests: ${suiteResult.totalTests}`);
  console.info(`   Passed: ${passedTests}`);
  console.info(`   Failed: ${failedTests}`);
  console.info(`   Duration: ${duration}ms`);
  
  if (failedTests > 0) {
    console.warn(`\n⚠️  ${failedTests} test(s) failed. Please review the results above.`);
  } else {
    console.info(`\n🎉 All tests passed! Magic link system is ready.`);
  }
  
  return suiteResult;
}

/**
 * Quick health check for magic link system
 */
export async function quickHealthCheck(): Promise<boolean> {
  try {
    const envTest = await testEnvironmentConfiguration();
    const adminTest = await testAdminClientConnectivity();
    
    return envTest.success && adminTest.success;
  } catch {
    return false;
  }
}
