# Organization Context Migration Plan

## Overview
This document outlines the plan to migrate from cookie-based organization context to a database-driven approach using the `is_current_context` column in the `organization_members` table.

## 1. Database Changes

### 1.1 Create DB Function for User Organization Context
```sql
CREATE OR REPLACE FUNCTION public.set_user_organization_context(
  p_user_id UUID,
  p_org_id UUID
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- First, set all contexts to false for this user
  UPDATE public.organization_members
  SET is_current_context = false
  WHERE user_id = p_user_id;
  
  -- Then set the selected org to true
  UPDATE public.organization_members
  SET is_current_context = true
  WHERE user_id = p_user_id
  AND org_id = p_org_id;
END;
$$;
```

### 1.2 Create DB Function for Initializing User Context
```sql
CREATE OR REPLACE FUNCTION public.initialize_user_organization_context(
  p_user_id UUID
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_org_id UUID;
  v_default_org_id UUID;
  v_any_org_id UUID;
BEGIN
  -- Check if user already has an active context
  SELECT org_id INTO v_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_current_context = true
  AND org_member_is_active = true
  LIMIT 1;
  
  -- Return existing context if found
  IF v_org_id IS NOT NULL THEN
    RETURN v_org_id;
  END IF;
  
  -- Try to find default organization
  SELECT org_id INTO v_default_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND is_default_org = true
  AND org_member_is_active = true
  LIMIT 1;
  
  -- If default org found, set it as context
  IF v_default_org_id IS NOT NULL THEN
    PERFORM public.set_user_organization_context(p_user_id, v_default_org_id);
    RETURN v_default_org_id;
  END IF;
  
  -- No default org, try any active org
  SELECT org_id INTO v_any_org_id
  FROM public.organization_members
  WHERE user_id = p_user_id
  AND org_member_is_active = true
  LIMIT 1;
  
  -- If any org found, set it as context
  IF v_any_org_id IS NOT NULL THEN
    PERFORM public.set_user_organization_context(p_user_id, v_any_org_id);
    RETURN v_any_org_id;
  END IF;
  
  -- No organizations found
  RETURN NULL;
END;
$$;
```

### 1.3 Replace `current_user_has_organizations` Function
```sql
CREATE OR REPLACE FUNCTION public.current_user_has_organizations()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_has_orgs BOOLEAN;
  v_org_id UUID;
BEGIN
  -- Get current user
  v_user_id := auth.uid();
  
  IF v_user_id IS NULL THEN
    RETURN false;
  END IF;
  
  -- Check if user has any organizations
  SELECT EXISTS (
    SELECT 1
    FROM public.organization_members
    WHERE user_id = v_user_id
    AND org_member_is_active = true
  ) INTO v_has_orgs;
  
  -- If user has organizations, ensure one is set as current context
  IF v_has_orgs THEN
    v_org_id := public.initialize_user_organization_context(v_user_id);
  END IF;
  
  RETURN v_has_orgs;
END;
$$;
```

## 2. Server-Side Changes

### 2.1 Update `organization-utils-server.ts`

- Update `getCurrentUserActiveOrganization` to use DB context instead of cookie
- Add new utility function to get organization by context

### 2.2 Update Organization Switch Route

- Modify `/api/organizations/switch/route.ts` to use DB function instead of cookie
- Remove cookie setting code

### 2.3 Update RBAC Permission Checks

- Modify `permissions-server.ts` to use DB context instead of cookie
- Ensure middleware uses DB context for admin route checks

## 3. Client-Side Changes

### 3.1 Create Organization Context Hook

- Create a new hook that uses the realtime subscription for context changes
- Ensure it updates when organization context changes

### 3.2 Update Organization Check Provider

- Update `organization-check-provider.tsx` to use DB functions
- Remove cookie-based checks

### 3.3 Update Organization Switcher Components

- Update any UI components that switch organizations to use new functions
- Remove cookie references

## 4. Testing Plan

### 4.1 Unit Tests
- Test DB functions with different scenarios
- Test server utilities with mocked responses

### 4.2 Integration Tests
- Test organization switching end-to-end
- Verify context persists across page refreshes
- Test fallback logic with different user scenarios

### 4.3 Manual Testing Checklist
- [ ] User can switch organizations
- [ ] Context persists after page refresh
- [ ] New user with no organizations sees create/join modal
- [ ] Admin routes check permissions correctly
- [ ] Realtime updates work when context changes from another session

## 5. Cleanup

### 5.1 Remove Cookie References
- Search for `active_organization_id` across the codebase
- Remove all cookie-related code

### 5.2 Documentation
- Update readme with new approach
- Document DB functions for future reference

## Implementation Tasks

### Phase 1: Database Setup
- [ ] Create DB functions for context management
- [ ] Test functions directly in Supabase

### Phase 2: Server-Side Implementation
- [ ] Update `organization-utils-server.ts` 
- [ ] Modify organization switch route
- [ ] Update RBAC permission checks
- [ ] Modify middleware for admin routes

### Phase 3: Client-Side Implementation
- [ ] Create organization context hook with realtime subscription
- [ ] Update organization check provider
- [ ] Update organization switcher components

### Phase 4: Testing & Validation
- [ ] Test with different user scenarios
- [ ] Verify persistence across page refreshes
- [ ] Test realtime updates

### Phase 5: Cleanup
- [ ] Remove all cookie references
- [ ] Update documentation

## Key File Implementations

### File 1: Updated `getCurrentUserActiveOrganization` Function

```typescript
// In organization-utils-server.ts
export async function getCurrentUserActiveOrganization(orgIdParam?: string): Promise<{ organization: Organization | null; error: Error | null }> {
  console.log('Resolving active organization:', {
    context: orgIdParam ? 'URL parameter' : 'Default flow',
    orgIdParam: orgIdParam || 'not provided'
  })
  
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      console.error('Auth error in getCurrentUserActiveOrganization:', userError || 'No user found')
      return { organization: null, error: userError || new Error('No authenticated user found') }
    }
    
    // If orgIdParam is provided (from URL), verify access to that specific organization
    if (orgIdParam) {
      const organization = await getUserOrganizationById(user.id, orgIdParam)
      if (organization) {
        console.log('Access granted to requested organization:', {
          userId: user.id,
          orgId: orgIdParam,
          orgName: organization.name,
          role: organization.role
        })
        return { organization, error: null }
      }
      console.log('No access to requested organization:', {
        userId: user.id,
        orgId: orgIdParam
      })
    }

    // Get active organization from database context
    const { data: currentContextOrg, error: contextError } = await supabase
      .from('organization_members')
      .select(ORGANIZATION_SELECT_QUERY)
      .eq('user_id', user.id)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)
      .eq('organizations.is_active', true)
      .single()
    
    if (!contextError && currentContextOrg) {
      const typedMembership = currentContextOrg as unknown as OrganizationMembershipResponse
      const organization = {
        id: typedMembership.organizations.id,
        name: typedMembership.organizations.org_name,
        org_icon: typedMembership.organizations.org_icon || '',
        role: typedMembership.roles.role_name,
        org_member_role: typedMembership.org_member_role,
        isActive: typedMembership.organizations.is_active,
        isDefault: typedMembership.is_default_org,
        createdAt: typedMembership.organizations.created_at,
        updatedAt: typedMembership.organizations.updated_at
      }
      console.log('Using current context organization from DB:', {
        userId: user.id,
        orgId: organization.id,
        orgName: organization.name,
        role: organization.role
      })
      return { organization, error: null }
    }
    
    // No context found, initialize one
    const { data: orgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: user.id
    })
    
    if (initError || !orgId) {
      console.log('No organizations found or failed to initialize context:', initError || 'No orgId returned')
      return { organization: null, error: new Error('No organization found for user') }
    }
    
    // Get the organization that was set as context
    const organization = await getUserOrganizationById(user.id, orgId)
    if (organization) {
      console.log('Using initialized organization context:', {
        userId: user.id,
        orgId: organization.id,
        orgName: organization.name,
        role: organization.role
      })
      return { organization, error: null }
    }
    
    // This should not happen if the function worked correctly
    console.log('Failed to get organization after context initialization')
    return { organization: null, error: new Error('Failed to initialize organization context') }
  } catch (err) {
    console.error('Error in getCurrentUserActiveOrganization:', err)
    return { organization: null, error: err instanceof Error ? err : new Error(String(err)) }
  }
}
```

### File 2: Updated Organization Switch Route

```typescript
// In src/app/api/organizations/switch/route.ts
export async function POST(request: Request) {
  try {
    const { organizationId } = await request.json()
    const supabase = await createClient()

    // Verify the user has access to this organization
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        org_member_role,
        org_member_is_active,
        organizations!inner (
          id,
          is_active,
          org_name,
          org_icon
        )
      `)
      .eq('user_id', user.id)
      .eq('org_id', organizationId)
      .eq('org_member_is_active', true)
      .eq('organizations.is_active', true)
      .single()

    if (membershipError || !membership) {
      console.error('Error verifying organization access:', membershipError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const typedMembership = membership as unknown as OrganizationResponse

    // Set organization as current context using the DB function
    const { error: contextError } = await supabase.rpc('set_user_organization_context', {
      p_user_id: user.id,
      p_org_id: organizationId
    })

    if (contextError) {
      console.error('Error setting organization context:', contextError)
      return NextResponse.json({ 
        error: 'Failed to set organization context',
        message: contextError.message 
      }, { status: 500 })
    }

    // Create response with organization data
    return NextResponse.json({ 
      success: true, 
      message: 'Organization switched successfully',
      organization: {
        id: typedMembership.organizations.id,
        name: typedMembership.organizations.org_name,
        org_icon: typedMembership.organizations.org_icon || '',
        role: typedMembership.org_member_role === 1 ? 'superadmin' : 'member',
        org_member_role: typedMembership.org_member_role,
        isActive: true
      }
    })
  } catch (error) {
    console.error('Error switching organization:', error)
    return NextResponse.json({ 
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
```

### File 3: Updated Organization Check Provider

```typescript
"use client";

import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { JoinOrganizationModal } from "@/components/organization/join-organization-modal";
import { useRealtimeSubscription } from "@/hooks/use-realtime-subscription";

// Paths that don't require organization check
const EXCLUDED_PATHS = [
  "/auth",
  "/login",
  "/signup",
  "/dashboard/admin/organization/create-organization",
  "/dashboard/admin/organization/create-organization-simple",
  "/dashboard/admin/organization/create-organization-test",
  "/api",
  "/",
  "/about",
  "/contact",
  "/pricing",
  "/terms",
  "/privacy",
];

export function OrganizationCheckProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const pathname = usePathname();
  const router = useRouter();
  const supabase = createClient();

  // Check if the current path is excluded from organization check
  const isExcludedPath = EXCLUDED_PATHS.some((path) =>
    pathname?.startsWith(path)
  );

  // Check if the current path is a create-organization route
  const isCreateOrgRoute = pathname?.includes("/create-organization");

  // Set up realtime subscription for organization context changes
  useRealtimeSubscription({
    table: "organization_members",
    filter: userId ? `user_id=eq.${userId}` : undefined,
    handlePayload: (payload) => {
      if (
        payload.new && 
        payload.eventType === "UPDATE" && 
        payload.new.is_current_context === true
      ) {
        // Organization context has changed, revalidate
        checkUserOrganizations();
      }
    },
  });

  const checkUserOrganizations = async () => {
    try {
      setIsLoading(true);

      // Check if user is authenticated
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      // If not authenticated and not on an excluded path, redirect to login
      if (sessionError || !session) {
        if (!isExcludedPath && !isCreateOrgRoute) {
          router.push("/auth/login");
        }
        return;
      }

      // Store user ID for realtime subscription
      setUserId(session.user.id);

      // Skip check for excluded paths and create-organization routes
      if (isExcludedPath || isCreateOrgRoute) {
        return;
      }

      // Call the user_has_organizations function with auto-initialization
      const { data, error } = await supabase.rpc(
        "current_user_has_organizations"
      );

      if (error) {
        console.error("Error checking user organizations:", error);
        return;
      }

      // If user doesn't belong to any organization, show the modal
      if (!data) {
        setShowModal(true);
      }
    } catch (error) {
      console.error("Error in organization check:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkUserOrganizations();
  }, [pathname]);

  const handleCloseModal = () => {
    // If user is on a protected page and tries to close the modal,
    // redirect them to create organization page
    if (!isExcludedPath) {
      router.push("/dashboard/admin/organization/create-organization");
    }
    setShowModal(false);
  };

  return (
    <>
      {children}
      {isLoading ? null : (
        <JoinOrganizationModal
          open={showModal}
          onOpenChange={setShowModal}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
}
```

### File 4: New Organization Context Hook

```typescript
// src/hooks/use-organization-context.ts
'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { useRealtimeSubscription } from '@/hooks/use-realtime-subscription';
import type { Organization } from '@/types/organization';

export function useOrganizationContext() {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const supabase = createClient();

  // Set up realtime subscription
  const { isConnected } = useRealtimeSubscription({
    table: 'organization_members',
    filter: userId ? `user_id=eq.${userId} AND is_current_context=eq.true` : undefined,
    handlePayload: (payload) => {
      if (payload.eventType === 'UPDATE' && payload.new?.is_current_context) {
        fetchCurrentOrganization();
      }
    },
  });

  // Fetch the current organization context
  const fetchCurrentOrganization = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session) {
        setError(sessionError || new Error('No session found'));
        setOrganization(null);
        return;
      }

      // Store the user ID for subscription filter
      setUserId(session.user.id);

      // Get current context
      const { data, error } = await supabase
        .from('organization_members')
        .select(`
          org_id,
          org_member_role,
          is_default_org,
          organizations!inner (
            id,
            org_name,
            org_icon,
            created_at,
            updated_at,
            is_active
          ),
          roles!inner (
            role_id,
            role_name
          )
        `)
        .eq('user_id', session.user.id)
        .eq('is_current_context', true)
        .eq('org_member_is_active', true)
        .eq('organizations.is_active', true)
        .single();

      if (error) {
        setError(new Error(`Failed to fetch organization: ${error.message}`));
        setOrganization(null);
        return;
      }

      if (!data) {
        // No organization found with current context
        setOrganization(null);
        return;
      }

      setOrganization({
        id: data.organizations.id,
        name: data.organizations.org_name,
        org_icon: data.organizations.org_icon || '',
        role: data.roles.role_name,
        org_member_role: data.org_member_role,
        isActive: data.organizations.is_active,
        isDefault: data.is_default_org,
        createdAt: data.organizations.created_at,
        updatedAt: data.organizations.updated_at
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Switch to a different organization
  const switchOrganization = async (orgId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.rpc('set_user_organization_context', {
        p_user_id: userId,
        p_org_id: orgId
      });

      if (error) {
        throw error;
      }

      // The fetchCurrentOrganization will be triggered automatically by the realtime subscription
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchCurrentOrganization();
  }, []);

  return {
    organization,
    loading,
    error,
    isConnected,
    switchOrganization,
    refreshOrganization: fetchCurrentOrganization
  };
}
```

### File 5: Updated RBAC Permissions File

```typescript
// In permissions-server.ts
// Determine which organization to check
let targetOrgId = resourceOrgId || orgId;
  
if (!targetOrgId && orgContext === 'current') {
  // Get current context organization from DB
  const { data: currentOrg } = await supabase
    .from('organization_members')
    .select('org_id')
    .eq('user_id', user.id)
    .eq('is_current_context', true)
    .eq('org_member_is_active', true)
    .single();
  
  targetOrgId = currentOrg?.org_id;

  if (!targetOrgId) {
    // Try to initialize context if not found
    const { data: initializedOrgId } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: user.id
    });
    
    targetOrgId = initializedOrgId;
  }
}

if (!targetOrgId && orgContext === 'current') {
  if (!options.silentFail) {
    redirect('/dashboard');
  }
  return false;
}
```
