import { redirect } from 'next/navigation';
import { createAdminClient } from '@/lib/supabase/admin';
import { z } from 'zod';
import { createHash } from 'crypto';
import OnboardingModal from '@/components/onboarding/onboarding-modal';

const tokenSchema = z.string().min(20).max(100);

export default async function OnboardingPage({
  searchParams
}: {
  searchParams: Promise<{ token?: string }>
}) {
  // 1. Await searchParams and validate token format
  const params = await searchParams;
  const tokenResult = tokenSchema.safeParse(params.token);
  if (!tokenResult.success) {
    redirect('/?error=invalid-invitation');
  }

  const token = tokenResult.data;
  const admin = createAdminClient();

  // 2. Server-side validation and data fetching using hash
  const hash = createHash('sha256').update(token).digest('hex');

  const { data: invite, error } = await admin
    .from('email_invitations')
    .select(`
      id,
      org_id,
      role_id,
      email,
      status,
      expires_at,
      personal_message,
      organizations (org_name),
      roles (role_name)
    `)
    .eq('invitation_hash', hash)
    .eq('status', 'delivered')
    .gte('expires_at', new Date().toISOString())
    .maybeSingle();

  // 3. Redirect if invalid or expired
  if (error || !invite) {
    redirect('/?error=invalid-invitation');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <OnboardingModal invite={invite} token={token} />
    </div>
  );
}
