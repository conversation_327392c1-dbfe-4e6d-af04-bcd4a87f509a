## Current Implementation Evaluation

*   **Strengths**:
    *   **<PERSON><PERSON>**: Ensures only one instance manages events, which is crucial.
    *   **Event Bus (`emitter`)**: Good use of `mitt` for decoupled internal communication.
    *   **Event Interpreters**: The concept of interpreters (`registerRoleInterpreter`, etc.) that transform raw database changes into semantic business events (`user:role:changed`, etc.) is sound.
    *   **Debounced Refresh**: The `debouncedRefreshServerContextAndSoftRefresh` is a good strategy to prevent API call storms.
    *   **Supabase Subscriptions**: Manages real-time updates effectively.

*   **Areas for Improvement (as you've identified)**:
    *   **High Complexity in `DashboardEventManager.tsx`**: The component juggles too many responsibilities:
        *   Singleton lifecycle.
        *   Initial user data fetching.
        *   Raw Supabase change subscriptions (for `organization_members`, `organizations`).
        *   Specific Supabase subscription for active organization's status.
        *   Handling semantic bus events (`user:role:changed`, `member:status:changed`, etc.).
        *   Implementing complex conditional logic for redirects and UI updates based on these events and various states (current role, org status, user status, current path). This is where the scenario logic from `scenario_test.md` is embedded.
        *   Server context refreshing and UI soft-refresh initiation.
        *   Emitting `client:authContextUpdated` for other UI parts.
    *   **Scenario Logic Intermingling**: The decision logic based on `scenario_test.md` (evidenced by comments like `// Scenario X`) is deeply intertwined with event handling and refresh mechanisms within large `useEffect` and `useBusEvent` blocks. This makes it hard to follow and modify.
    *   **Scenario Coverage**: While many scenarios seem to be addressed with specific console logs (e.g., Scenario 11, 13, 15, 16), the way they are implemented makes comprehensive verification difficult. Scenario 5 (org deactivation for non-superadmin) appears to be handled indirectly, relying on the soft refresh and subsequent page/layout logic to enforce the redirect, rather than an immediate `router.push` within the direct event handler. This indirection can obscure the flow.

## Detailed Optimization Plan

The core idea is to separate concerns, making each part of the system responsible for a more focused task. The scenario logic from `scenario_test.md` will be centralized in a dedicated manager.

Here's a proposed breakdown:

**1. `DashboardEventManagerCore.tsx` (New or Refactored Leaner `DashboardEventManager`)**

*   **Responsibilities**:
    *   Maintain the singleton pattern (current DOM marker approach or a React context-based approach if simpler).
    *   Fetch initial user ID.
    *   Establish and manage **generic** Supabase real-time subscriptions for:
        *   `organization_members` table (all events: `*`).
        *   `organizations` table (updates: `UPDATE`).
    *   Upon receiving raw database changes, emit granular, low-level events onto the internal event bus (`emitter`). For example:
        *   `db:organization_members:inserted`
        *   `db:organization_members:updated`
        *   `db:organization_members:deleted`
        *   `db:organizations:updated`
    *   This component will no longer handle semantic event interpretation or scenario logic. It's purely a conduit for raw DB changes.
    *   It will manage its Supabase channels and their cleanup.

**2. Event Interpreters (Leverage and Formalize Existing - `src/lib/eventBus/channels/*.ts`)**

*   **Responsibilities**:
    *   These interpreters (e.g., `registerRoleInterpreter`, `registerStatusInterpreter`, `registerNameInterpreter`, `registerContextInterpreter`, `registerAllMembersInterpreter`) will subscribe to the `db:*:*` events emitted by `DashboardEventManagerCore`.
    *   They parse the raw database payloads and emit higher-level, meaningful **business/semantic events** onto the `emitter`. Examples of events they already (or should) emit:
        *   `user:role:changed` (payload: `{ userId, orgId, newRoleId, oldRoleId, ... }`)
        *   `member:status:changed` (payload: `{ userId, orgId, isActive, oldIsActive, ... }`)
        *   `organization:name:changed` (payload: `{ orgId, newName, ... }`)
        *   `organization:context:changed` (payload: `{ userId, orgId, ... }`)
    *   **New/Formalized Interpreter - `ActiveOrgStatusInterpreter.ts`**:
        *   This logic currently resides in a `useEffect` within `DEM`. It should be extracted.
        *   Subscribes to `db:organizations:updated` from `DashboardEventManagerCore`.
        *   Filters for updates to the `activeOrganization.id` (obtained from `useDashboard` or passed in).
        *   Monitors changes to the `is_active` field.
        *   Emits specific events:
            *   `activeOrganization:activated` (payload: `{ orgId }`)
            *   `activeOrganization:deactivated` (payload: `{ orgId }`)

**3. `useServerContextRefresher.ts` (New Hook)**

*   **Responsibilities**:
    *   Encapsulate the `refreshServerContextAndSoftRefresh` logic, including the debouncing mechanism (`debounceTimeoutRef`).
    *   Expose a stable, debounced function (e.g., `triggerContextRefresh()`) that returns a Promise with the `RefreshedAuthContext | null`.
    *   Internally use `useRouter()` for `router.refresh()` and manage `isProcessingEvent` state (perhaps renamed to `isRefreshing`).

**4. `DashboardScenarioManager.tsx` (New Component) or `useDashboardScenarios.ts` (New Hook)**

*   **This is the new centralized brain for handling outcomes based on `scenario_test.md`.**
*   **Responsibilities**:
    *   Subscribe to the semantic events emitted by the Event Interpreters (e.g., `user:role:changed`, `member:status:changed`, `activeOrganization:activated`, `activeOrganization:deactivated`).
    *   Use `useDashboard()` to get `activeOrganization`, current `userId`, and other relevant context.
    *   Use the `useServerContextRefresher()` hook to get `triggerContextRefresh`.
    *   Use `useRouter()` for navigation.
    *   **Scenario Logic Implementation**:
        *   For each relevant incoming semantic event:
            1.  Filter if the event pertains to the current user and active organization.
            2.  Call `await triggerContextRefresh()` to ensure decisions are based on the latest server state. This is crucial.
            3.  Using the event payload and the `refreshedAuthContext`, evaluate conditions corresponding to the scenarios in `scenario_test.md`.
                *   This is where the comments like `// Scenario X: ...` will now live, making the mapping explicit.
                *   Example: If `user:role:changed` event occurs, and after refresh, context shows `roleId === SUPERADMIN_ROLE_ID`, `orgIsInactive === true`, `userIsActive === true`, and `currentPath` was `/dashboard/organization-disabled` (mapping to your Scenario 11 for promotion to superadmin in an inactive org), then `router.push('/dashboard')`.
            4.  If a redirect is determined by the scenario logic, execute `router.push(targetPage)`.
            5.  After processing and potential redirect, emit `client:authContextUpdated` with the relevant parts of the `refreshedAuthContext` (e.g., `isOrgActive`, `isUserActive`, `newRoleId`). This allows other UI components (sidebar, org switcher) to update reactively.
    *   This component/hook centralizes all redirect decisions and scenario-specific side effects.

**5. Integration in Layout/Provider Tree**

*   The main application layout (e.g., `DashboardLayout` or a root provider) would:
    *   Render/initialize `DashboardEventManagerCore` (once).
    *   Ensure Event Interpreters are registered (this might be done via module imports or a setup function called by `DashboardEventManagerCore`).
    *   Render/initialize `DashboardScenarioManager` (or call the `useDashboardScenarios` hook).

### Benefits of This Approach:

*   **Clear Separation of Concerns**:
    *   `DashboardEventManagerCore`: Raw DB event fetching and broadcasting.
    *   `Event Interpreters`: Transforming raw DB events into meaningful business events.
    *   `useServerContextRefresher`: Dedicated, debounced server state synchronization.
    *   `DashboardScenarioManager`: Centralized decision-making for redirects and UI state based on business events and scenarios.
*   **Improved Readability & Maintainability**: Each module is smaller and has a well-defined purpose. The complex scenario logic is isolated.
*   **Explicit Scenario Mapping**: `DashboardScenarioManager` becomes the clear source of truth for how `scenario_test.md` translates into application behavior.
*   **Enhanced Testability**: Individual hooks and components can be unit-tested more easily. The `DashboardScenarioManager` can be tested by mocking input events, router, and context.
*   **Reduced Complexity**: The original `DashboardEventManager.tsx` will be significantly simplified or replaced by `DashboardEventManagerCore`.

### Addressing `scenario_test.md` Coverage:

By systematically implementing each row of `scenario_test.md` as a conditional block within `DashboardScenarioManager`, triggered by the appropriate semantic event and considering the refreshed context, you can ensure and verify coverage more effectively.

This plan aims to create a more robust, understandable, and maintainable event management system by modularizing responsibilities and centralizing the complex decision-making logic.




1: Is user authenticated?
    - yes > continue,
    - no > redirect to login page
2: Check current context?
    - has current context > continue
    - has no current context
        - Does user have default org?
            - yes, set as current context and continue
            - no, select any org in which user is a member, set current context and continue
3: Is user active in current context org?
    - yes: continue
    - no: redirect to dashboard/account-disabled page and block access to navigation and all other dashboard pages
4: Is current context org active?
    - yes: continue (re)rendering according to rbac
    - no:
        Is current user superadmin in current context org?
            yes: continue rendering according to rbac
            no:  redirect to dashboard/organization-disabled page and block access to navigation and all other dashboard pages

For every realtime event regarding the current user's role, current user's status in current context org or current context org status, we can simply follow the same determination model:

e.g:
- User switches organizations in organization-switcher
- User's role changes in current context organization
- User's status changes in current context organization
- Current context organization status changes for current user

Exceptions in the event system are for
- allmembers, which provides updates about other members in the same organization(s) as where the current user is a member of. This is specific to certain pages and not for the dashboard rendering itself

- inserts (user is added to new organization), which isn't current context but required for organization switcher and some other components
- deletes (user is removed from an organization) either current context org or another org.

We'll handle the exceptions at a later stage, once the primary logic is in place.

## Event Management Refactoring Scratchpad

This scratchpad outlines the actionable tasks to modularize and streamline the event management system, leveraging the existing RBAC infrastructure, the proposed determination model, Zustand for state management, and SWR for data fetching.

**Phase 1: Core Infrastructure & Event Broadcasting**

*   **[X] Task 1: Create `DashboardEventManagerCore.tsx`**
    *   **Description**: This component will be a lean version of the current `DashboardEventManager`.
    *   **Responsibilities**:
        *   Maintain the singleton pattern (existing DOM marker or React context).
        *   Fetch initial `userId` if needed (though `useDashboard` might provide this).
        *   Establish generic Supabase real-time subscriptions for:
            *   `organization_members` table (event: `*`).
            *   `organizations` table (event: `UPDATE`).
        *   On receiving raw DB changes, emit low-level, specific events onto the `emitter` (e.g., `db:organization_members:inserted`, `db:organization_members:updated`, `db:organization_members:deleted`, `db:organizations:updated`).
        *   Manage its Supabase channels and their cleanup.
    *   **Files to Modify/Create**:
        *   `src/components/dashboard/dashboard-event-manager-core.tsx` (New)
        *   `src/components/dashboard/dashboard-event-manager.tsx` (To be heavily refactored or replaced by `DashboardEventManagerCore` and `DashboardScenarioManager`).
        *   Relevant layout file to integrate `DashboardEventManagerCore`.
    *   **Notes**: This component *will not* interpret events or handle scenario logic.

*   **[X] Task 2: Formalize and Enhance Event Interpreters**
    *   **Description**: Refine existing event interpreters and create any new ones needed.
    *   **Responsibilities**:
        *   Located in `src/lib/eventBus/channels/`.
        *   Subscribe to the `db:*:*` events from `DashboardEventManagerCore`.
        *   Parse raw DB payloads and emit higher-level, semantic business events (e.g., `user:role:changed`, `member:status:changed`, `activeOrganization:activated`, `activeOrganization:deactivated`).
        *   Ensure payloads for these semantic events are consistent and provide all necessary information (e.g., `orgId`, `userId`, `oldValue`, `newValue`).
    *   **Files to Modify/Create**:
        *   `src/lib/eventBus/channels/roleInterpreter.ts` (Review/Refine)
        *   `src/lib/eventBus/channels/statusInterpreter.ts` (Review/Refine)
        *   `src/lib/eventBus/channels/nameInterpreter.ts` (Review/Refine)
        *   `src/lib/eventBus/channels/contextInterpreter.ts` (Review/Refine)
        *   `src/lib/eventBus/channels/activeOrgStatusInterpreter.ts` (New - for `activeOrganization:activated`/`deactivated`)
        *   `src/lib/eventBus/registerInterpreters.ts` (or similar mechanism to ensure all are registered).
    *   **Notes**: The logic for detecting active organization status changes (currently in `DashboardEventManager.tsx useEffect`) will move to `activeOrgStatusInterpreter.ts`.

*   **[X] Task 3: Create Zustand Store for Auth Context (`useAuthContextStore.ts`)**
    *   **Description**: Centralize client-side auth context using Zustand.
    *   **Responsibilities**:
        *   Define a Zustand store to hold `orgId`, `userId`, `roleId`, `isUserActiveInOrg`, `isOrgActive`, derived booleans (e.g., `isSuperAdmin`), and potentially context loading/error states`.
    *   **Files to Modify/Create**:
        *   `src/stores/useAuthContextStore.ts` (New)

*   **[X] Task 4: Create `useServerContextRefresher.ts` Hook (with SWR & Zustand Integration)**
    *   **Description**: Encapsulate debounced server context refresh logic using SWR and update Zustand store.
    *   **Responsibilities**:
        *   Use SWR to fetch data from `/api/auth/refresh-context`. SWR key: `/api/auth/refresh-context`. This endpoint should return data reflecting the database's single source of truth.
        *   On successful SWR fetch/revalidation, update the `useAuthContextStore` (Zustand) with the new context. The structure of the data from `/api/auth/refresh-context` should align with the fields in `useAuthContextStore` (e.g., `RefreshedAuthContext` or similar).
        *   Expose a stable, debounced function (e.g., `forceRefreshContext()`) that triggers SWR's `mutate` for the context API key. This function will be called by the `DashboardScenarioManager`.
        *   Internally use `useRouter()` for `router.refresh()` (soft refresh part).
        *   Manage `isProcessingEvent` (or `isRefreshingContext`) state, potentially reflecting this in the Zustand store.
    *   **Files to Modify/Create**:
        *   `src/hooks/use-server-context-refresher.ts` (New)
        *   `src/components/dashboard/dashboard-event-manager.tsx` (Original refresh logic to be superseded).

**Phase 2: Scenario Management & Middleware Integration**

*   **[X] Task 5: Enhance Middleware for Proactive Checks**
    *   **Description**: Offload initial status checks from client to middleware based on the "determination model", using direct DB queries as the source of truth.
    *   **Responsibilities**:
        *   Verify Step 1 (Authentication) - already primarily handled by Supabase SSR middleware.
        *   For protected dashboard routes:
            *   Obtain `activeOrgId`: Read from a designated cookie (e.g., `active-org-id`) set during login or organization switching.
            *   Obtain `userId` from `await supabase.auth.getUser()`.
            *   If `activeOrgId` and `userId` are present, directly query the database (using the middleware's Supabase client) for:
                *   `organization_members.org_member_is_active` for the `userId` in `activeOrgId`.
                *   `organizations.is_active` for the `activeOrgId`.
                *   `organization_members.org_member_role` for the `userId` in `activeOrgId`.
            *   Based on these direct DB results:
                *   Perform Step 3 (User active in org): If `org_member_is_active` is `false`, redirect to `/dashboard/account-disabled`.
                *   Perform Step 4 (Org active): If `organizations.is_active` is `false` AND the user's role (from DB) in that org is not permitted to view inactive orgs (e.g., not superadmin), redirect to `/dashboard/organization-disabled`.
    *   **Files to Modify/Create**:
        *   `src/lib/rbac/middleware.ts` (Modify) or the main `src/middleware.ts` if RBAC and general auth middleware are combined/sequential.
    *   **Notes**: This ensures initial page loads respect the DB as the single source of truth for critical status and role information. The client-side event system will handle *changes* to this state.

*   **[X] Task 6: Implement `DashboardScenarioManager.tsx` (or `useDashboardScenarios.ts` Hook)**
    *   **Description**: Centralized component/hook for `scenario_test.md` outcomes, integrated with Zustand and the SWR-based refresher.
    *   **Responsibilities**:
        *   Subscribe to semantic events from Event Interpreters (e.g., `user:role:changed`, `member:status:changed`, `organization:statusChanged`).
        *   Use `useDashboard()` for initial `activeOrganization`, `userId` (if needed before Zustand is populated or for comparison).
        *   Use `useServerContextRefresher()` to get the `forceRefreshContext` function.
        *   Subscribe to `useAuthContextStore` (Zustand) to get the current, authoritative client-side auth context.
        *   Use `useRouter()` for navigation.
        *   **Scenario Logic**:
            1.  On receiving a relevant semantic event:
            2.  Call `forceRefreshContext()` (which triggers SWR and updates Zustand).
            3.  When `useAuthContextStore` updates (as a result of the refresh), evaluate the new context from Zustand.
            4.  Based on the *original event type* and the *new context from Zustand*, determine the current state according to the "determination model" logic.
            5.  Map the specific *event transition* and the *resulting state from Zustand* to the scenarios in `scenario_test.md` to determine the required redirect.
            6.  Execute `router.push(targetPage)` if a redirect is needed.
            7.  The `client:authContextUpdated` event (if still used) would primarily signal "refresh cycle complete"; primary data for UI updates comes from Zustand.
    *   **Files to Modify/Create**:
        *   `src/components/dashboard/dashboard-scenario-manager.tsx` (New, or `src/hooks/use-dashboard-scenarios.ts`)
        *   Relevant layout file to integrate `DashboardScenarioManager`.
        *   `src/components/dashboard/dashboard-event-manager.tsx` (Original scenario logic to be moved here and adapted).
    *   **Notes**: `scenario_test.md` logic becomes explicit here, driven by Zustand state.

**Phase 3: Refinement & UI Adaptation**

*   **[X] Task 7: Refactor Existing `DashboardEventManager.tsx`**
    *   **Description**: Strip down `DashboardEventManager.tsx` to only be `DashboardEventManagerCore`'s responsibilities, or remove if `DashboardEventManagerCore` is a new file and other new modules handle the rest.
    *   **Responsibilities**: Ensure no redundant logic remains.
    *   **Files to Modify/Create**:
        *   `src/components/dashboard/dashboard-event-manager.tsx`

*   **[ ] Task 8: Adapt UI Components to Use Zustand Store**
    *   **Description**: Modify UI components (sidebar, navigation, org switcher, etc.) that depend on auth context to read from `useAuthContextStore`.
    *   **Responsibilities**: Ensure UI reacts correctly to context changes via Zustand.
    *   **Files to Modify/Create**: Various UI component files.

*   **[ ] Task 9: Review and Test All Scenarios**
    *   **Description**: Systematically test each scenario from `scenario_test.md` with the new architecture.
    *   **Responsibilities**: Verify redirects, UI updates, data consistency through Zustand and SWR.
    *   **Files to Modify/Create**: (Testing files, potentially).

*   **[ ] Task 10: Address "Exception" Event Handling**
    *   **Description**: Plan and implement handling for events like `allmembers` updates, new org insertions/deletions that primarily affect non-redirect UI parts.
    *   **Responsibilities**: These might involve specific components subscribing to bus events and updating local state or dedicated Zustand stores if the data is global.
    *   **Files to Modify/Create**: TBD based on implementation choice.

This phased approach should help in systematically refactoring the event management system for improved clarity, maintainability, and performance.