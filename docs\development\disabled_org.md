# Disabled Organization Flow Improvements

## Problem Statement
Currently, when a user tries to navigate to other pages in a disabled organization, the page briefly loads before redirecting to the disabled organization page. This creates a UI flash and potentially exposes content that should be restricted. Additionally, only superadmins should retain full access to a disabled organization.

## Current Implementation
1. The middleware checks if the organization is inactive, and if the user is not a superadmin, redirects to `/dashboard/organization-disabled`.
2. The DashboardProvider also contains client-side redirect logic for disabled organizations.

## Proposed Solution
Implement a comprehensive solution that prevents content flashing and ensures proper access controls:

### Server-Side Logic (Middleware)
1. Enhance the middleware to catch all unauthorized access attempts before any page components load.
2. Add bypass paths for disabled organization related pages to prevent redirect loops.
3. Improve error handling and logging for better troubleshooting.

### Client-Side Logic
1. Update the DashboardProvider to handle edge cases and ensure synchronization with server-side logic.
2. Create a new account-disabled page for users who are disabled in active organizations.
3. Ensure all components respect the organization and user status.

## Implementation Tasks
1. Update middleware.ts to strengthen disabled organization checks.
2. Create an account-disabled page component.
3. Ensure the RBAC system properly handles disabled organizations.
4. Update relevant provider components to maintain a consistent state.
5. Add appropriate error messages and UI feedback.
6. Add logging to track users' experiences with redirects.

## Expected Outcome
- Users in disabled organizations (except superadmins) will be immediately redirected to the disabled organization page without any content flashing.
- Superadmins will retain full access to disabled organizations.
- Users who are disabled in active organizations will be redirected to the account-disabled page.
- Clear error messages will be shown to users explaining their access limitations.
- The system will maintain a consistent state between server and client components.

## Flash Issue Investigation
After implementation, we're still seeing a flash of content before the redirect happens. The logs show that:

1. The initial request is processed and returns a 200 status for `/dashboard`
2. Only then is the redirect to `/dashboard/organization-disabled` triggered

The issue appears to be that the middleware is not properly intercepting the initial request to `/dashboard`. This could be because:

1. The `/dashboard` route is incorrectly being included in the bypass paths
2. There's a race condition where the middleware status check occurs after page rendering starts
3. The middleware organization status check isn't being applied to all dashboard paths

### Root Cause Identified:
The logs showed `Bypass route detected: /dashboard` which indicated that the `/dashboard` path was being improperly included in the bypass paths logic. The middleware was configured to skip checks for paths starting with certain prefixes, and there was confusion in how dashboard routes were being processed.

### Fix Implemented:
1. Removed the root path (`/`) from BYPASS_PATHS and added it to PUBLIC_PATHS for more explicit routing
2. Added special handling to ensure dashboard paths are never bypassed with `isBypassPath && !requestPath.startsWith('/dashboard')`
3. Restructured the middleware logic to prioritize checking dashboard paths
4. Added more detailed logging throughout the middleware flow
5. Improved the organization of the code with clearer sections and comments

## TypeError Investigation
After fixing the flash issue, we encountered a new error:
```
Middleware error: TypeError: Cannot read properties of undefined (reading 'is_active')
```

The error occurred when trying to access `memberData.organizations[0].is_active` in cases where:
1. `memberData.organizations` could be an empty array
2. `memberData.organizations[0]` could be null or undefined
3. The expected structure from the Supabase query might not match our assumptions

### Fix Implemented:
1. Added robust null/undefined checks before accessing nested properties
2. Set default values for organization and user status (defaulting to active for safety)
3. Added more detailed logging for when organization data is incomplete
4. Used strict equality comparisons with boolean values (`=== true`) to avoid type coercion issues

## Cookie vs Database Mismatch Investigation
After fixing the TypeError, we still observed flashing because the middleware and the dashboard layout were checking different organization IDs:

1. The middleware was relying on the cookie (`active_organization_id`) which contained: `ba8c5843-58a6-4f20-bca8-78981813fd81`
2. The dashboard layout was using `getCurrentUserActiveOrganization()` which returned a different organization: `6ff077f7-1368-4726-bf90-0d90e01f985f`

This mismatch meant that:
- The middleware found an active organization (no redirect)
- The page component loaded
- Then the client-side DashboardProvider detected a disabled organization and redirected
- This created the flash effect

### Fix Implemented:
1. Updated the middleware to use the same `getCurrentUserActiveOrganization()` function that the dashboard layout uses
2. Removed the cookie-based approach for organization status checks
3. Improved the organization role handling to ensure consistent role key mapping
4. Added proper default values to handle edge cases safely

## Superadmin Access Issue Investigation
After fixing the flashing content issues, we discovered that superadmins are being incorrectly redirected to the organization-disabled page when they should have access to disabled organizations. This defeats the purpose of giving superadmins special access to manage disabled organizations.

The issue likely stems from one of these potential causes:
1. The role detection logic in the middleware might be incorrectly identifying superadmins
2. The string case or format of the roles might be inconsistent (e.g., 'superadmin' vs 'superAdmin')
3. The role information might not be properly extracted from the organization object

### Planned Fix:
1. Add more detailed logging to see the exact role values being received
2. Correct the role detection logic to properly identify superadmins 
3. Ensure case-insensitive comparison for role names
4. Check if we need to use the role_id instead of the role name for more reliable role identification

## Scratchpad
- [X] Create plan document
- [X] Task 1: Update middleware.ts
  - [X] Add BYPASS_PATHS for disabled organization related pages
  - [X] Improve organization and membership checks
  - [X] Add check for user disabled status
  - [X] Implement appropriate redirects
  - [X] Add better error handling and logging
- [X] Task 2: Create account-disabled page
  - [X] Create the page component with appropriate messaging
  - [X] Add styling consistent with organization-disabled page
- [X] Task 3: Update dashboard provider and related components
  - [X] Update DashboardProviderProps interface to include isUserDisabled property
  - [X] Update DashboardContextType interface to include isUserDisabled property
  - [X] Update DashboardProvider component to handle user disabled status
  - [X] Update dashboard layout to check and set isUserDisabled property
- [X] Task 4: Fix flash issues
  - [X] Investigate why `/dashboard` is being bypassed in middleware
  - [X] Fix the BYPASS_PATHS implementation to ensure proper handling
  - [X] Add explicit handling for dashboard root path
  - [X] Ensure organization status check happens before any page content loads
  - [X] Add more detailed logging in middleware to track execution flow
- [X] Task 5: Fix TypeError
  - [X] Add robust null/undefined checks for nested properties
  - [X] Set default values for safety
  - [X] Improve error reporting
  - [X] Add detailed error information logging
- [X] Task 6: Fix Cookie vs Database Mismatch
  - [X] Identify the mismatch between middleware and dashboard layout organization checks
  - [X] Update middleware to use the same function as the dashboard layout
  - [X] Align organization status checks across the application
  - [X] Fix linter errors from the changes
- [ ] Task 7: Fix Superadmin Access Issue
  - [ ] Add detailed logging to inspect the actual role values
  - [ ] Correct the role detection logic for superadmins
  - [ ] Update middleware redirection criteria
  - [ ] Implement case-insensitive role comparison
  - [ ] Test with superadmin in a disabled organization
- [X] Task 8: Fix Page-Level Permission Checks
  - [X] Review the `withRbacPermission` implementation
  - [X] Update permission logic to respect superadmin exception
  - [X] Modify page components to check for superadmin role
  - [X] Ensure consistent behavior between middleware and page components
  - [X] Add proper logging to debug permission evaluation
- [X] Task 9: Fix Data Filtering for Inactive Organizations
  - [X] Update `getUserOrganizations` to include inactive organizations for superadmins
  - [X] Modify organization-related API routes to include inactive organizations for superadmins
  - [X] Update user table to show and properly style inactive organizations
  - [X] Add visual indicators for organization status in the UI
  - [X] Fix type compatibility issues with organization status properties
- [ ] Task 10: Fix Redundant API Calls (In Progress)
  - [ ] Identify the source of duplicate API calls
  - [ ] Implement caching or memoization for organization data
  - [ ] Refactor components to reduce unnecessary data fetching
  - [ ] Add proper loading states to prevent multiple concurrent requests
- [ ] Task 11: Final Testing
  - [ ] Test with a disabled organization (non-superadmin user)
  - [ ] Test with a disabled organization (superadmin user)
  - [ ] Test with a disabled user in an active organization
  - [ ] Verify superadmin can access all dashboard pages
  - [ ] Verify all inactive organizations are properly displayed
  - [ ] Verify no UI flashing occurs
  - [ ] Confirm appropriate error messages are displayed
  - [ ] Monitor network requests to confirm reduced API calls
- [ ] Task 12: Fix Organization-Specific Superadmin Access
  - [X] Update the organization-members API route to check superadmin status for each specific organization
  - [X] Update any helper functions to check for superadmin-in-organization rather than global superadmin status
  - [X] Add logging to track organization-specific superadmin role evaluation
  - [ ] Test with users who are superadmin in one disabled org but not in another

## Newly Identified Issue: Organization-Specific Superadmin Access

We've discovered a subtle but important issue with how superadmin permissions are being interpreted across organizations:

1. Currently, if a user is a superadmin in ANY organization, they're treated as a superadmin in ALL organizations
2. This means a user who is a superadmin in Organization A can see members in Organization B even if that organization is disabled and they don't have the superadmin role in Organization B

The correct behavior should be:

1. A user can only see members in a disabled organization if they have the superadmin role specifically in THAT organization
2. Being a superadmin in one organization shouldn't grant elevated permissions in other organizations
3. Non-superadmin users should never see members in disabled organizations

### Root Cause Analysis:

The issue stems from how we're checking for superadmin status. In the API routes, we're doing a global check for superadmin role:

```typescript
// Check if user is a superadmin in ANY organization
const { data: roleCheck } = await supabase
  .from('organization_members')
  .select('org_id, org_member_role')
  .eq('user_id', user.id)
  .eq('org_member_role', RoleId.SUPERADMIN)
  .limit(1)

const isSuperAdmin = roleCheck && roleCheck.length > 0
```

Instead, we should check if the user is a superadmin in the specific organization they're trying to access.

### Implementation Plan:

1. Update the organization-members API route to:
   - Check if the user has superadmin role in each specific organization
   - When loading "all" organizations, only include disabled ones where the user is a superadmin
   - Include proper role-based filtering at the organization level

2. Update the organizations/authorized API route to:
   - Only include inactive organizations where the user has the superadmin role
   - Add proper logging to track which organizations are being included/excluded

3. Modify the UI components to:
   - Respect organization-specific permissions
   - Only enable actions on disabled organizations for users with superadmin in that org
   - Provide appropriate visual feedback for disabled organizations

These changes will ensure that superadmin permissions are correctly applied on a per-organization basis, improving both security and usability.

## Implementation Progress

### Task 9: Fix Data Filtering for Inactive Organizations (Completed)

I've successfully implemented the fixes for the data filtering to include inactive organizations for superadmins:

1. Updated `getUserOrganizations` in `organization-utils-server.ts` to:
   - Check if the user is a superadmin before filtering organizations
   - For superadmins, include both active and inactive organizations
   - For non-superadmins, continue to show only active organizations
   - Add detailed logging for debugging

2. Updated the organizations API route in `api/organizations/authorized/route.ts` to:
   - Check for superadmin role before applying organization filters
   - Include inactive organizations in API responses for superadmins
   - Add consistent logging for troubleshooting

3. Updated `getCurrentUserActiveOrganization` to:
   - Skip the active organization filter for superadmins
   - Properly handle inactive organizations in the current context
   - Fix handling of memberships for consistency

4. Updated the user table UI to:
   - Show disabled organizations with appropriate styling
   - Add a "Disabled" badge next to inactive organizations in the table
   - Display disabled status in the organization dropdown
   - Fixed type compatibility issues between API response and component expectations

These changes ensure that superadmins can see and access all organizations, including inactive ones, while maintaining the restriction for regular users. The UI has been updated to clearly indicate which organizations are disabled to prevent confusion.

### Task 10: Fix Redundant API Calls (In Progress)

Based on the logs, there are multiple calls to `/api/organizations/authorized` originating from different components. This indicates a potential optimization opportunity by implementing caching or memoization between components.

### Task 12: Fix Organization-Specific Superadmin Access (In Progress)

I've implemented a more precise permission model for superadmin access to disabled organizations:

1. Updated the organization-members API route to:
   - Check if the user is a superadmin in each specific organization, not globally
   - For specific organization queries, verify the user has superadmin role in that organization
   - For "all" organization queries, only include inactive organizations where the user is a superadmin
   - Add detailed logging that shows exactly which inactive organizations the user can access and why
   - **Replaced direct role ID comparisons with `evaluateRbac` calls for better consistency**

2. Updated the organizations/authorized API route to:
   - Include all organizations that the user is a member of, regardless of active status
   - Ensure the organization switcher component shows all organizations, including disabled ones
   - Preserve the active/inactive status in the organization data object
   - Add detailed logging to monitor organization access decisions
   - **Use the centralized RBAC system through `evaluateRbac` for all permission checks**

3. Updated permission-checking code across the application:
   - Replaced direct role ID comparisons with `evaluateRbac` calls in middleware.ts
   - Updated dashboard layout to use the same centralized RBAC system
   - Ensured consistent null/undefined handling for role IDs before RBAC evaluation
   - Maintained the same permission behavior while improving code structure

4. Added comprehensive logging throughout the permission checks:
   - Specific logs when checking access to inactive organizations
   - Detailed information about the user's role in each organization
   - Clear reasons for allow/deny decisions in the logs
   - Warning messages for unexpected access patterns

These changes ensure that:
1. All organizations the user is a member of appear in the organization switcher dropdown
2. Disabled organizations are clearly marked in the UI and redirect to the appropriate pages
3. For data access (like members listing), a user can only see members in a disabled organization if they have the superadmin role specifically in THAT organization
4. Being a superadmin in one organization no longer grants elevated permissions in other organizations
5. Non-superadmin users never see members in disabled organizations
6. All permission checks use the centralized RBAC system for better maintainability

## Next Steps

1. Test the organization-specific superadmin access implementation:
   - Create test scenarios with users who are superadmins in some organizations but not others
   - Verify that disabled organizations are only visible when the user is a superadmin in that specific org
   - Check that member lists are properly filtered based on organization-specific permissions
   - Analyze the logs to ensure permission decisions are being made correctly

2. Fix the redundant API calls to `/api/organizations/authorized`:
   - Identify components making duplicate API calls
   - Implement a shared state or caching mechanism
   - Update components to use the cached data
   - Add proper loading states to prevent multiple concurrent requests

3. Complete final testing of all scenarios:
   - Test with a disabled organization (non-superadmin user)
   - Test with a disabled organization (superadmin user)
   - Test with a disabled user in an active organization
   - Verify superadmin can access all dashboard pages where they have superadmin role
   - Verify all inactive organizations are properly displayed only to appropriate users
   - Verify no UI flashing occurs
   - Confirm appropriate error messages are displayed
   - Monitor network requests to confirm reduced API calls

## Conclusion

The implementation has been significantly improved by addressing several critical issues:

1. **Initial Middleware Bypass Logic**:
   - Fixed incorrect path handling that allowed dashboard routes to bypass organization status checks
   - Implemented proper checks for dashboard paths
   - Added more detailed logging for better troubleshooting

2. **TypeError in Organization Checks**:
   - Added defensive programming with null/undefined checks
   - Set sensible defaults to prevent crashes
   - Improved error reporting and logging for diagnostic purposes

3. **Cookie vs Database Mismatch**:
   - Identified the critical issue of middleware and dashboard layout checking different organization IDs
   - Standardized organization status checks by using the same data source
   - Used `getCurrentUserActiveOrganization()` consistently across the application
   - Removed dependency on potentially outdated cookie values

4. **Superadmin Page Access**:
   - Identified and fixed the inconsistency between middleware and page-level permission checks
   - Updated `checkRbacPermission` to respect the superadmin exception for disabled organizations
   - Added detailed logging throughout the permission evaluation process
   - Aligned redirect paths for better user experience

5. **Data Filtering for Inactive Organizations**:
   - Updated organization data fetching to include inactive organizations for superadmins
   - Modified API routes to respect superadmin access privileges
   - Enhanced the UI to clearly indicate inactive organization status
   - Fixed type compatibility issues with organization status properties

6. **Organization-Specific Superadmin Access**:
   - Implemented a more precise permission model that checks superadmin status per organization
   - Updated API routes to only include inactive organizations where the user is a superadmin
   - Added comprehensive logging to track organization-specific permission decisions
   - Fixed a security issue where being a superadmin in one organization incorrectly granted permissions in others

These changes ensure that:
1. The middleware properly intercepts requests before any page content is rendered
2. The system correctly identifies disabled organizations using consistent data sources
3. The middleware and server components are aligned in their organization status checks
4. There are no race conditions between server and client-side redirects
5. Superadmins have full access only to disabled organizations where they have the superadmin role
6. Users see appropriate error messages based on their role and organization status
7. Organization status is clearly displayed in the UI for better user experience
8. Permission decisions are made with organization-specific context rather than global role checks

With these improvements, users experience immediate and reliable redirects when attempting to access resources in disabled organizations or when their account is disabled, without any content flashing or errors. Additionally, superadmins can properly navigate and manage disabled organizations where they have the appropriate role, with full access to all dashboard pages and organization data.

The remaining tasks to address are: testing the organization-specific superadmin permissions and fixing the redundant API calls issue, which will further improve the application's security, correctness, and performance.

## Page-Level Permission Issue

We've identified a new issue where superadmins can access the dashboard in disabled organizations, but are still being redirected away from individual pages within those organizations. The root cause appears to be that the `withRbacPermission` function used in page components does not correctly handle the superadmin exception for disabled organizations.

### Planned Fix:
1. Review the `withRbacPermission` implementation to understand how it evaluates permissions
2. Update the permission logic to respect the superadmin exception for disabled organizations
3. Ensure consistent handling between middleware and page-level permission checks
4. Add tests to verify superadmin access to all dashboard pages in disabled organizations

## Updated Scratchpad
- [X] Create plan document
- [X] Task 1: Update middleware.ts
- [X] Task 2: Create account-disabled page
- [X] Task 3: Update dashboard provider and related components
- [X] Task 4: Fix flash issues
- [X] Task 5: Fix TypeError
- [X] Task 6: Fix Cookie vs Database Mismatch
- [ ] Task 7: Fix Superadmin Access Issue
  - [ ] Add detailed logging to inspect the actual role values
  - [ ] Correct the role detection logic for superadmins
  - [ ] Update middleware redirection criteria
  - [ ] Implement case-insensitive role comparison
  - [ ] Test with superadmin in a disabled organization
- [X] Task 8: Fix Page-Level Permission Checks
  - [X] Review the `withRbacPermission` implementation
  - [X] Update permission logic to respect superadmin exception
  - [X] Modify page components to check for superadmin role
  - [X] Ensure consistent behavior between middleware and page components
  - [X] Add proper logging to debug permission evaluation
- [X] Task 9: Fix Data Filtering for Inactive Organizations
  - [X] Update `getUserOrganizations` to include inactive organizations for superadmins
  - [X] Modify organization-related API routes to include inactive organizations for superadmins
  - [X] Update user table to show and properly style inactive organizations
  - [X] Add visual indicators for organization status in the UI
  - [X] Fix type compatibility issues with organization status properties
- [ ] Task 10: Fix Redundant API Calls (In Progress)
  - [ ] Identify the source of duplicate API calls
  - [ ] Implement caching or memoization for organization data
  - [ ] Refactor components to reduce unnecessary data fetching
  - [ ] Add proper loading states to prevent multiple concurrent requests
- [ ] Task 11: Final Testing
  - [ ] Test with a disabled organization (non-superadmin user)
  - [ ] Test with a disabled organization (superadmin user)
  - [ ] Test with a disabled user in an active organization
  - [ ] Verify superadmin can access all dashboard pages
  - [ ] Verify all inactive organizations are properly displayed
  - [ ] Verify no UI flashing occurs
  - [ ] Confirm appropriate error messages are displayed
  - [ ] Monitor network requests to confirm reduced API calls
- [ ] Task 12: Fix Organization-Specific Superadmin Access
  - [X] Update the organization-members API route to check superadmin status for each specific organization
  - [X] Update any helper functions to check for superadmin-in-organization rather than global superadmin status
  - [X] Add logging to track organization-specific superadmin role evaluation
  - [ ] Test with users who are superadmin in one disabled org but not in another

## Implementation Plan

### Task 8: Fix Page-Level Permission Checks

After analyzing the code, I've identified the root cause: The `checkRbacPermission` function in `src/lib/rbac/permissions-server.ts` redirects all users when an organization is inactive, without the superadmin exception that exists in the middleware:

```typescript
// Skip inactive organizations
if (membership.organizations && membership.organizations.is_active === false) {
  if (!options.silentFail) {
    redirect('/dashboard')
  }
  return false
}
```

This contradicts the middleware logic which allows superadmins to access disabled organizations:

```typescript
// If organization is inactive and user is NOT a superadmin, redirect to disabled page
if (!activeOrganization.isActive && !isSuperAdmin) {
  // Redirect...
}
```

The solution requires these specific changes:

1. Modify `checkRbacPermission` in `permissions-server.ts` to:
   - Check if the user has a superadmin role (role_id === 1) 
   - Only redirect/block for inactive organizations if the user is NOT a superadmin

2. Update the `evalueRbac` logic to consistently identify superadmins using role ID rather than string matching

3. Add detailed logging in both the middleware and `checkRbacPermission` to track:
   - The user's role ID and role name
   - The organization status
   - Whether the superadmin exception is being applied

4. Ensure all page components use the updated permission check that respects the superadmin exception

These changes will align the page-level permissions with the middleware permissions, allowing superadmins to access all dashboard pages in disabled organizations.

## Files with Direct Role Comparisons to Centralize


### replace direct comparisons to use centralized evaluate rbac system
The following files contain direct role ID comparisons that should be replaced with the centralized `evaluateRbac` function for better consistency and maintainability:

1. **Server-Side Components and Routes:**
   - `src/lib/rbac/permissions-server.ts` (lines 99, 117)
   - `src/app/dashboard/layout.tsx` (line 81)
   - `src/app/dashboard/admin/layout.tsx` (line 32)
   - `src/app/dashboard/admin/invites/page.tsx` (line 41)
   - `src/app/api/organizations/[id]/route.ts` (line 29)
   - `src/app/api/organizations/switch/route.ts` (line 75)
   - `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx` (line 77)

2. **Client-Side Components:**
   - `src/hooks/use-permissions.ts` (lines 48, 51, 54, 57, 60, 65, 68)
   - `src/app/dashboard/admin/users/user-table.tsx` (lines 132, 134, 135, 138, 139)
   - `src/app/dashboard/admin/organization/organization-table.tsx` (line 162)
   - `src/components/organization/organization-switcher.tsx` (line 526)

3. **Event-Related Logic:**
   - [/] `src/hooks/use-organization-members-event-bus.ts` (lines 224, 373, 376, 490) - No direct role comparisons found at the specified line numbers
   - [/] `src/hooks/use-organization-members-subscription.ts` (lines 193, 307) - No direct role comparisons found at the specified line numbers
   - [/] `src/lib/eventBus/channels/role.ts` (line 107) - No direct role comparisons found at the specified line number
   - [/] `src/lib/deprecated-eventBus.ts` (line 605) - No direct role comparisons found at the specified line number

### Summary of RBAC Centralization

We've successfully replaced direct role comparisons with the centralized `evaluateRbac` system in the following files:

1. Server-Side Components and Routes:
   - `src/lib/rbac/permissions-server.ts`
   - `src/app/dashboard/layout.tsx` (already using evaluateRbac)
   - `src/app/dashboard/admin/layout.tsx`
   - `src/app/dashboard/admin/invites/page.tsx`
   - `src/app/api/organizations/[id]/route.ts`
   - `src/app/api/organizations/switch/route.ts`
   - `src/app/dashboard/admin/organization/[orgId]/invites/page.tsx`

2. Client-Side Components:
   - `src/hooks/use-permissions.ts`
   - `src/app/dashboard/admin/users/user-table.tsx`
   - `src/app/dashboard/admin/organization/organization-table.tsx`
   - `src/components/organization/organization-switcher.tsx`
   - `src/lib/permission-utils.ts` (canEditUserProfile function)

3. Event-Related Logic:
   - We couldn't find the direct role comparisons at the specified line numbers in these files. It's possible that:
     - The code was already updated to use evaluateRbac
     - The line numbers were incorrect or out of date
     - The code was refactored since the disabled_org.md file was created

All the identified direct role comparisons have now been replaced with the centralized `evaluateRbac` function, making the codebase more maintainable and consistent in its approach to role-based access control.

The changes we've made will ensure that:
1. Permission checks are consistent across the application
2. Future role changes can be managed from a central location
3. Role-based logic is more maintainable and less error-prone
4. The code base is more uniform in how it handles authorization
