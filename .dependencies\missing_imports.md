# ❌ Missing Imports

- File: `src/app/api/organization-members/route.ts` (Line 5)
  - Tries to import: `OrganizationMemberFull`
  - From module: `@/types/organization/`
  - Resolved Target: `src/types/organization.ts` (Export not found in target module)

- File: `src/app/dashboard/admin/users/user-profile-dialog.tsx` (Line 7)
  - Tries to import: `OrganizationMemberFull`
  - From module: `@/types/organization/`
  - Resolved Target: `src/types/organization.ts` (Export not found in target module)

- File: `src/components/labels/LabelEditDialog.tsx` (Line 4)
  - Tries to import: `LabelUI`
  - From module: `@/components/ui/label`
  - Resolved Target: `src/components/ui/label.tsx` (Export not found in target module)

- File: `src/components/shared/member-table.tsx` (Line 7)
  - Tries to import: `OrganizationMemberFull`
  - From module: `@/types/organization/`
  - Resolved Target: `src/types/organization.ts` (Export not found in target module)

- File: `src/components/ui/badge.tsx` (Line 4)
  - Tries to import: `BadgeProps`
  - From module: `@/types/components/ui`
  - Resolved Target: `src/types/components/ui/index.ts` (Target file resolved but no exports were parsed/found.)

- File: `src/components/ui/button.tsx` (Line 5)
  - Tries to import: `ButtonProps`
  - From module: `@/types/components/ui`
  - Resolved Target: `src/types/components/ui/index.ts` (Target file resolved but no exports were parsed/found.)

- File: `src/components/ui/pagination.tsx` (Line 6)
  - Tries to import: `ButtonProps`
  - From module: `@/types/components/ui`
  - Resolved Target: `src/types/components/ui/index.ts` (Target file resolved but no exports were parsed/found.)

- File: `src/hooks/use-auth-context-events.ts` (Line 4)
  - Tries to import: `rawRefreshAuthContext`
  - From module: `@/lib/refresh-auth-context`
  - Resolved Target: `src/lib/refresh-auth-context.ts` (Export not found in target module)

- File: `src/hooks/use-organization-members-event-bus.ts` (Line 8)
  - Tries to import: `OrganizationMemberFull`
  - From module: `@/types/organization/`
  - Resolved Target: `src/types/organization.ts` (Export not found in target module)

- File: `src/hooks/use-rbac-permission.ts` (Line 11)
  - Tries to import: `RoleIdType`
  - From module: `@/types/lib/rbac`
  - Resolved Target: `src/types/lib/rbac/index.ts` (Export not found in target module)

- File: `src/types/components/UserProfileDialogProps.ts` (Line 2)
  - Tries to import: `OrganizationMemberFull`
  - From module: `@/types/organization/`
  - Resolved Target: `src/types/organization.ts` (Export not found in target module)

- File: `src/types/components/UserTableProps.ts` (Line 2)
  - Tries to import: `OrganizationMemberFull`
  - From module: `@/types/organization/`
  - Resolved Target: `src/types/organization.ts` (Export not found in target module)

- File: `src/types/navigation.ts` (Line 1)
  - Tries to import: `CoreRbacConditions`
  - From module: `@/types/lib/rbac`
  - Resolved Target: `src/types/lib/rbac/index.ts` (Export not found in target module)


*Note: This indicates imports where the specified name couldn't be found in the target module's exports, or the module itself couldn't be resolved within the workspace. This could be due to typos, incorrect paths, missing exports, or path alias configurations.*
