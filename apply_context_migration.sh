#!/bin/bash

# Apply the organization context migration
# This script applies the updated database functions to manage organization context

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color
YELLOW='\033[1;33m'

echo -e "${YELLOW}Starting migration to update organization context management...${NC}"

# First, get the Supabase URL and Key from .env file
if [ -f .env.local ]; then
  source .env.local
  SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
  SUPABASE_KEY=$SUPABASE_SERVICE_ROLE_KEY
else
  echo -e "${RED}Error: .env.local file not found${NC}"
  echo "Please ensure you have a .env.local file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY set"
  exit 1
fi

if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
  echo -e "${RED}Error: Supabase URL or Service Role Key not found in .env.local${NC}"
  exit 1
fi

echo -e "${GREEN}Found Supabase URL and Service Role Key${NC}"
echo "Applying migration from context_update_migration.sql..."

# Apply the migration with CURL
response=$(curl -s -X POST \
  "${SUPABASE_URL}/rest/v1/rpc/pg_query" \
  -H "apikey: ${SUPABASE_KEY}" \
  -H "Authorization: Bearer ${SUPABASE_KEY}" \
  -H "Content-Type: application/json" \
  -d @- << EOF
{
  "query": "$(cat context_update_migration.sql | tr '\n' ' ' | sed 's/"/\\"/g')"
}
EOF
)

# Check if there was an error
if [[ $response == *"error"* ]]; then
  echo -e "${RED}Error applying migration:${NC}"
  echo "$response" | jq '.'
  exit 1
else
  echo -e "${GREEN}Successfully applied database function updates!${NC}"
fi

echo ""
echo -e "${GREEN}Migration complete!${NC}"
echo "The application should now use the database-driven context system more efficiently."
echo "This reduces unnecessary updates and improves performance during organization switching." 