import { NextRequest, NextResponse } from 'next/server'
import { resolveUserAuthContext } from '@/lib/auth-context'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const orgId = searchParams.get('orgId')

  if (!userId) {
    return NextResponse.json({ error: 'userId is required' }, { status: 400 })
  }

  try {
    // Get the organization name if we have the orgId
    let orgName = ''
    if (orgId) {
      const supabase = await createClient()
      const { data } = await supabase
        .from('organizations')
        .select('org_name')
        .eq('id', orgId)
        .single()
      
      if (data?.org_name) {
        orgName = data.org_name
      }
    }
    
    const authContext = await resolveUserAuthContext(userId, orgId || undefined)
    
    if (!authContext) {
      return NextResponse.json({ error: 'Failed to resolve auth context' }, { status: 404 })
    }
    
    // Ensure the organization name is set
    if (orgName && !authContext.orgName) {
      authContext.orgName = orgName
    }
    
    return NextResponse.json(authContext)
  } catch (error) {
    console.error('Error resolving auth context:', error)
    return NextResponse.json(
      { error: 'Failed to resolve auth context' },
      { status: 500 }
    )
  }
} 