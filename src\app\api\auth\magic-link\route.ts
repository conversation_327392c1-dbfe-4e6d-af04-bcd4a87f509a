import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { magicLinkRequestSchema, isSuspiciousEmail } from '@/lib/auth/validation';
import { rateLimiter, getClientIP } from '@/lib/auth/rate-limiter';
import { sendAdminMagicLinkEmail } from '@/lib/email/admin-magic-link-service';

// Security headers
const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
};

// Generic response to prevent user enumeration
const GENERIC_SUCCESS_RESPONSE = {
  success: true,
  message: "If you are an existing user you'll receive a login link shortly. Please also check your junk/spam folder before trying again.",
};

const GENERIC_ERROR_RESPONSE = {
  success: false,
  message: "Unable to process your request. Please try again later.",
};

// Feature flag for hybrid magic link implementation
const USE_CUSTOM_MAGIC_LINKS = process.env.USE_CUSTOM_MAGIC_LINKS === 'true';

// Note: CAPTCHA validation is handled by Supabase automatically
// when the captchaToken is passed to signInWithOtp() or admin.generateLink()

export async function POST(request: NextRequest) {
  try {
    // Add security headers
    const headers = new Headers(SECURITY_HEADERS);
    headers.set('Content-Type', 'application/json');

    // Get client IP for rate limiting
    const clientIP = getClientIP(request);

    // Check IP rate limits first
    const ipLimitCheck = rateLimiter.checkIPLimit(clientIP);
    if (!ipLimitCheck.allowed) {
      console.warn(`IP rate limit exceeded: ${clientIP}`);
      return NextResponse.json(
        {
          success: false,
          message: "Too many requests. Please try again later.",
          resetTime: ipLimitCheck.resetTime
        },
        { status: 429, headers }
      );
    }

    // Parse and validate request body
    let requestData;
    try {
      const body = await request.json();
      requestData = magicLinkRequestSchema.parse(body);
    } catch (error) {
      console.warn('Invalid request data:', error);
      rateLimiter.recordIPRequest(clientIP);
      return NextResponse.json(GENERIC_ERROR_RESPONSE, { status: 400, headers });
    }

    const { email, website, captchaToken } = requestData;

    // Honeypot check - if website field is filled, it's likely a bot
    if (website && website.length > 0) {
      console.warn(`Honeypot triggered for email: ${email}, IP: ${clientIP}`);
      rateLimiter.recordIPRequest(clientIP);
      // Return success to not reveal the honeypot
      return NextResponse.json(GENERIC_SUCCESS_RESPONSE, { status: 200, headers });
    }

    // Check for suspicious email patterns
    if (isSuspiciousEmail(email)) {
      console.warn(`Suspicious email pattern detected: ${email}, IP: ${clientIP}`);
      rateLimiter.recordIPRequest(clientIP);
      return NextResponse.json(GENERIC_SUCCESS_RESPONSE, { status: 200, headers });
    }

    // CAPTCHA validation is handled by Supabase when we pass the token
    // to signInWithOtp() - no need for custom validation here

    // Check email-specific rate limits
    const emailLimitCheck = rateLimiter.checkEmailLimit(email);
    if (!emailLimitCheck.allowed) {
      console.warn(`Email rate limit exceeded: ${email}`);
      return NextResponse.json(
        {
          success: false,
          message: `Please wait before requesting another magic link. Try again in ${Math.ceil((emailLimitCheck.resetTime! - Date.now()) / 1000)} seconds.`,
          resetTime: emailLimitCheck.resetTime,
          cooldownSeconds: rateLimiter.getCooldownTime(email)
        },
        { status: 429, headers }
      );
    }

    // Record the request for rate limiting
    rateLimiter.recordEmailRequest(email);
    rateLimiter.recordIPRequest(clientIP);

    // Choose magic link implementation based on feature flag
    if (USE_CUSTOM_MAGIC_LINKS) {
      // New: Use admin API + custom Resend email for better deliverability
      console.info(`[Hybrid] Using custom magic link service for ${email}, IP: ${clientIP}`);

      const result = await sendAdminMagicLinkEmail({
        email,
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || request.nextUrl.origin}/auth/callback?redirectTo=/dashboard`,
        captchaToken,
        clientIP,
      });

      // Handle CAPTCHA-specific errors (admin API will return these)
      if (!result.success && result.error?.includes('captcha')) {
        console.warn(`[Hybrid] CAPTCHA verification failed for ${email}: ${result.error}, IP: ${clientIP}`);
        return NextResponse.json(
          {
            success: false,
            message: "Security verification failed. Please complete the CAPTCHA and try again.",
          },
          { status: 400, headers }
        );
      }

      // Add timing attack protection
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

      // Always return generic success for user enumeration protection
      console.info(`[Hybrid] Magic link process completed for ${email}, IP: ${clientIP}`);
      return NextResponse.json(GENERIC_SUCCESS_RESPONSE, { status: 200, headers });

    } else {
      // Legacy: Use Supabase built-in magic link system
      console.info(`[Legacy] Using Supabase magic link service for ${email}, IP: ${clientIP}`);

      const supabase = await createClient();

      // Attempt to send magic link - Supabase will validate CAPTCHA and only send if user exists
      const otpOptions: any = {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || request.nextUrl.origin}/auth/callback?redirectTo=/dashboard`,
      };

      // Only include CAPTCHA token if provided
      if (captchaToken) {
        otpOptions.captchaToken = captchaToken;
      }

      const { error: magicLinkError } = await supabase.auth.signInWithOtp({
        email,
        options: otpOptions,
      });

      // Check if the error indicates user doesn't exist or other issues
      if (magicLinkError) {
        // Handle CAPTCHA-specific errors differently
        if (magicLinkError.message?.includes('captcha') || magicLinkError.message?.includes('verification')) {
          console.warn(`[Legacy] CAPTCHA verification failed for ${email}: ${magicLinkError.message}, IP: ${clientIP}`);
          return NextResponse.json(
            {
              success: false,
              message: "Security verification failed. Please complete the CAPTCHA and try again.",
            },
            { status: 400, headers }
          );
        }

        // Log the attempt but always return generic success message for other errors
        console.info(`[Legacy] Magic link request failed for ${email}: ${magicLinkError.message}, IP: ${clientIP}`);

        // Add a small delay to make timing attacks harder
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

        // Always return generic success to prevent user enumeration
        return NextResponse.json(GENERIC_SUCCESS_RESPONSE, { status: 200, headers });
      }

      // Log successful magic link send
      console.info(`[Legacy] Magic link sent successfully to: ${email}, IP: ${clientIP}`);

      // Always return the same generic success message
      return NextResponse.json(GENERIC_SUCCESS_RESPONSE, { status: 200, headers });
    }

  } catch (error) {
    console.error('Magic link API error:', error);

    // Record failed request for rate limiting
    const clientIP = getClientIP(request);
    rateLimiter.recordIPRequest(clientIP);

    return NextResponse.json(
      GENERIC_ERROR_RESPONSE,
      {
        status: 500,
        headers: new Headers(SECURITY_HEADERS)
      }
    );
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    {
      status: 405,
      headers: new Headers({
        ...SECURITY_HEADERS,
        'Allow': 'POST'
      })
    }
  );
}

export async function PUT() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    {
      status: 405,
      headers: new Headers({
        ...SECURITY_HEADERS,
        'Allow': 'POST'
      })
    }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    {
      status: 405,
      headers: new Headers({
        ...SECURITY_HEADERS,
        'Allow': 'POST'
      })
    }
  );
}
