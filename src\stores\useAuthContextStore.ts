import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { RoleId } from '@/lib/rbac/roles'; // Correctly import RoleId

// Type definitions for migration handling
type PersistedAuthState = Partial<AuthContextState>;
// Define more specific type for migration state
type PersistedState = Record<string, unknown>;

interface AuthContextState {
  userId: string | null;
  orgId: string | null;
  roleId: number | null;
  isUserActiveInOrg: boolean | null;
  isOrgActive: boolean | null;
  isSuperAdmin: boolean;
  isLoading: boolean;
  error: string | null;

  // User profile data
  userEmail: string | null;
  userFullName: string | null;
  avatarUrl: string | null;
  activeOrgName: string | null;

  // UI notification states
  isActiveOrgDisabled: boolean | null;

  // States for optimistic updates (e.g., during org switching)
  optimisticLoading: boolean;
  optimisticOrgId: string | null; // Store the target orgId during optimistic switch
  optimisticRoleId: number | null; // Store the target roleId
  optimisticNavigation: boolean; // Flag for optimistic navigation (replacing sessionStorage)
}

interface AuthContextActions {
  setAuthContext: (context: Partial<Omit<AuthContextState, 'isSuperAdmin' | 'isLoading' | 'error' | 'optimisticLoading' | 'optimisticOrgId' | 'optimisticRoleId' | 'optimisticNavigation'>>) => void;
  clearAuthContext: () => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  updateFullContext: (context: {
    userId?: string | null;
    orgId?: string | null;
    roleId?: number | null;
    isUserActiveInOrg?: boolean | null;
    isOrgActive?: boolean | null;
    userEmail?: string | null;
    userFullName?: string | null;
    avatarUrl?: string | null;
    activeOrgName?: string | null;
    isActiveOrgDisabled?: boolean | null;
  }) => void;

  // Actions for optimistic updates
  startOptimisticUpdate: (targetOrgId: string, targetRoleId: number | null) => void;
  finishOptimisticUpdate: () => void;
  revertOptimisticUpdate: (errorMsg?: string) => void;
  setOptimisticNavigation: (isOptimisticNavigation: boolean) => void;
}

const initialState: AuthContextState = {
  userId: null,
  orgId: null,
  roleId: null,
  isUserActiveInOrg: null,
  isOrgActive: null,
  isSuperAdmin: false,
  isLoading: true, // Start with loading true until initial context is fetched
  error: null,

  // Initial values for user profile data
  userEmail: null,
  userFullName: null,
  avatarUrl: null,
  activeOrgName: null,

  // Initial UI notification states
  isActiveOrgDisabled: null,

  // Initial values for optimistic updates
  optimisticLoading: false,
  optimisticOrgId: null,
  optimisticRoleId: null,
  optimisticNavigation: false,
};

/**
 * Utility function to safely migrate persisted state
 * Ensures all required fields exist and have valid types
 */
const createSafeMigration = (persistedState: PersistedState): PersistedAuthState => {
  const safeState: PersistedAuthState = { ...initialState };

  // Safely copy over valid fields from persisted state
  if (persistedState && typeof persistedState === 'object') {
    // String fields
    if (typeof persistedState.userId === 'string' || persistedState.userId === null) {
      safeState.userId = persistedState.userId;
    }
    if (typeof persistedState.orgId === 'string' || persistedState.orgId === null) {
      safeState.orgId = persistedState.orgId;
    }
    if (typeof persistedState.userEmail === 'string' || persistedState.userEmail === null) {
      safeState.userEmail = persistedState.userEmail;
    }
    if (typeof persistedState.userFullName === 'string' || persistedState.userFullName === null) {
      safeState.userFullName = persistedState.userFullName;
    }
    if (typeof persistedState.avatarUrl === 'string' || persistedState.avatarUrl === null) {
      safeState.avatarUrl = persistedState.avatarUrl;
    }
    if (typeof persistedState.activeOrgName === 'string' || persistedState.activeOrgName === null) {
      safeState.activeOrgName = persistedState.activeOrgName;
    }

    // Number fields
    if (typeof persistedState.roleId === 'number' || persistedState.roleId === null) {
      safeState.roleId = persistedState.roleId;
    }

    // Boolean fields
    if (typeof persistedState.isUserActiveInOrg === 'boolean' || persistedState.isUserActiveInOrg === null) {
      safeState.isUserActiveInOrg = persistedState.isUserActiveInOrg;
    }
    if (typeof persistedState.isOrgActive === 'boolean' || persistedState.isOrgActive === null) {
      safeState.isOrgActive = persistedState.isOrgActive;
    }
    if (typeof persistedState.isActiveOrgDisabled === 'boolean' || persistedState.isActiveOrgDisabled === null) {
      safeState.isActiveOrgDisabled = persistedState.isActiveOrgDisabled;
    }
    if (typeof persistedState.optimisticNavigation === 'boolean') {
      safeState.optimisticNavigation = persistedState.optimisticNavigation;
    }
  }

  // Ensure derived fields are correctly calculated
  if (typeof safeState.roleId === 'number') {
    safeState.isSuperAdmin = safeState.roleId === RoleId.SUPERADMIN;
  }

  return safeState;
};

export const useAuthContextStore = create<AuthContextState & AuthContextActions>()(
  subscribeWithSelector(
    persist(
      (set) => ({
        ...initialState,

        setAuthContext: (context) => set((state) => {
          const newRoleId = typeof context.roleId === 'number' ? context.roleId : state.roleId;
          const isOrgActive = context.isOrgActive ?? state.isOrgActive;

          return {
            ...state,
            ...context,
            isSuperAdmin: newRoleId === RoleId.SUPERADMIN,
            isActiveOrgDisabled: isOrgActive === false,
          };
        }),

        clearAuthContext: () =>
          set(() => ({
            ...initialState,
            isLoading: false,
          })),

        setLoading: (isLoading) => set({ isLoading }),

        setError: (error) => set({ error, isLoading: false }),

        updateFullContext: (context) =>
          set((state) => {
            const baseState = state;
            const newRoleId = typeof context.roleId === 'number' ? context.roleId : baseState.roleId;
            const isOrgActive = context.isOrgActive ?? baseState.isOrgActive;

            return {
              userId: context.userId ?? baseState.userId,
              orgId: context.orgId ?? baseState.orgId,
              roleId: context.roleId ?? baseState.roleId,
              isUserActiveInOrg: context.isUserActiveInOrg ?? baseState.isUserActiveInOrg,
              isOrgActive,
              isSuperAdmin: newRoleId === RoleId.SUPERADMIN,

              userEmail: context.userEmail ?? baseState.userEmail,
              userFullName: context.userFullName ?? baseState.userFullName,
              avatarUrl: context.avatarUrl ?? baseState.avatarUrl,
              activeOrgName: context.activeOrgName ?? baseState.activeOrgName,

              // Derive isActiveOrgDisabled from isOrgActive
              isActiveOrgDisabled: isOrgActive === false,

              isLoading: false,
              error: null,

              // Preserve optimistic update state
              optimisticLoading: state.optimisticLoading,
              optimisticOrgId: state.optimisticOrgId,
              optimisticRoleId: state.optimisticRoleId,
            };
          }),

        // Optimistic update actions
        startOptimisticUpdate: (targetOrgId, targetRoleId) =>
          set(state => {
            // Find organization details from session storage or defaults
            let orgName = 'Loading...';
            let isOrgActive = true;  // Optimistically assume org is active
            let isUserActiveInOrg = true;  // Optimistically assume user is active

            // Try to get organization details from sessionStorage
            if (typeof window !== 'undefined') {
              try {
                const sessionOrgName = sessionStorage.getItem('optimisticOrgName');
                if (sessionOrgName) orgName = sessionOrgName;

                const sessionOrgActive = sessionStorage.getItem('optimisticOrgActive');
                if (sessionOrgActive) isOrgActive = sessionOrgActive === 'true';

                const sessionUserActive = sessionStorage.getItem('optimisticUserActive');
                if (sessionUserActive) isUserActiveInOrg = sessionUserActive === 'true';
              } catch (e) {
                console.error('Error reading optimistic data from sessionStorage:', e);
              }
            }

            return {
              ...state,
              optimisticLoading: true,
              optimisticOrgId: targetOrgId,
              optimisticRoleId: targetRoleId,
              // Update main context for UI responsiveness
              orgId: targetOrgId,
              roleId: targetRoleId,
              activeOrgName: orgName,
              isOrgActive: isOrgActive,
              isUserActiveInOrg: isUserActiveInOrg,
              isSuperAdmin: targetRoleId === RoleId.SUPERADMIN,
              // Derived state
              isActiveOrgDisabled: isOrgActive === false,
            };
          }),

        finishOptimisticUpdate: () =>
          set({
            optimisticLoading: false,
            optimisticOrgId: null,
            optimisticRoleId: null,
          }),

        revertOptimisticUpdate: (errorMsg) =>
          set({
            optimisticLoading: false,
            optimisticOrgId: null,
            optimisticRoleId: null,
            optimisticNavigation: false,
            error: errorMsg || 'Optimistic update failed and was reverted.',
          }),

        setOptimisticNavigation: (isOptimisticNavigation) =>
          set({
            optimisticNavigation: isOptimisticNavigation
          }),
      }),
      {
        name: 'auth-context',
        version: 1,

        // Migration function to handle state schema changes
        migrate: (persistedState: unknown, version: number): PersistedAuthState => {
          console.log(`[AuthContextStore] Migrating from version ${version} to version 1`);

          try {
            // Handle migration from version 0 (no version) to version 1
            if (version < 1) {
              console.log('[AuthContextStore] Migrating from legacy state to version 1');
              // Type assertion to pass unknown to PersistedState
              const migratedState = createSafeMigration(persistedState as PersistedState);
              console.log('[AuthContextStore] Migration to version 1 completed successfully');
              return migratedState;
            }

            // For future versions, add migration logic here
            //   // Add any version 2 specific transformations here
            //   // migratedState.newFieldInV2 = defaultValue;
            //   return migratedState;
            // }

            console.log('[AuthContextStore] No migration needed, state is current');
            // Type assertion to ensure returned state is PersistedAuthState
            return persistedState as PersistedAuthState;

          } catch (error) {
            console.error('[AuthContextStore] Migration failed, falling back to initial state:', error);
            // If migration fails, return initial state to prevent app crashes
            return initialState;
          }
        },

        // Only persist the actual data, not the loading/error/optimistic states
        partialize: (state) => {
          // Create a new object with only the properties we want to persist
          const nonPersistentKeys = [
            'error',
            'isLoading',
            'optimisticLoading',
            'optimisticOrgId',
            'optimisticRoleId'
          ];

          return Object.fromEntries(
            Object.entries(state).filter(([key]) => !nonPersistentKeys.includes(key))
          );
        },

        // Add storage error handling
        onRehydrateStorage: () => {
          console.log('[AuthContextStore] Starting state rehydration from localStorage');
          return (_state, error) => {
            if (error) {
              console.error('[AuthContextStore] Rehydration failed:', error);
              // Could implement fallback logic here, like clearing corrupted storage
            } else {
              console.log('[AuthContextStore] State rehydration completed successfully');
            }
          };
        },
      }
    )
  )
);

// Optional: Persist store if needed, e.g., to localStorage
// import { persist, createJSONStorage } from 'zustand/middleware';
// export const useAuthContextStore = create(
//   persist<AuthContextState & AuthContextActions>(
//     (set, get) => ({
//       // ... store definition
//     }),
//     {
//       name: 'auth-context-storage', // name of the item in the storage (must be unique)
//       storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
//     }
//   )
// );