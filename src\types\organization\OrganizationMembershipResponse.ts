/**
 * Type representing the response from the database for organization membership queries
 */
export interface OrganizationMembershipResponse {
  org_id: string;
  org_member_role: number; 
  org_member_is_active: boolean;
  is_default_org: boolean;
  organizations: {
    id: string;
    org_name: string;
    org_icon: string | null;
    created_at: string;
    updated_at: string;
    is_active: boolean;
  };
  roles: {
    role_id: number;
    role_name: string;
  };
} 