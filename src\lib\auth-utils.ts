import { createClient } from '@/lib/supabase/server'

export type UserRole = number

export async function getUserRole() {
  const supabase = await createClient()
  
  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser()
  
  console.log('Current auth user:', user?.id)
  
  if (userError) {
    console.error('Auth error:', userError)
    return null
  }
  
  if (!user) {
    console.log('No authenticated user found')
    return null
  }

  // Get the active organization context from database
  const { data: currentOrgMembership, error: contextError } = await supabase
    .from('organization_members')
    .select(`
      org_id,
      org_member_role,
      roles (
        role_id,
        role_name
      )
    `)
    .eq('user_id', user.id)
    .eq('is_current_context', true)
    .eq('org_member_is_active', true)
    .single()

  if (contextError || !currentOrgMembership) {
    console.log('No active organization context found', contextError)
    
    // Try to initialize one if not found
    const { data: orgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: user.id
    })
    
    if (initError || !orgId) {
      console.error('Failed to initialize organization context:', initError)
      return null
    }
    
    // Re-fetch with the newly set context
    const { data: updatedMembership, error: retryError } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        org_member_role,
        roles (
          role_id,
          role_name
        )
      `)
      .eq('user_id', user.id)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)
      .single()
      
    if (retryError || !updatedMembership) {
      console.error('Still no active organization after initialization:', retryError)
      return null
    }
    
    console.log('User role data after context initialization:', {
      orgId: updatedMembership.org_id,
      memberRole: updatedMembership.org_member_role,
      roleDetails: updatedMembership.roles
    })
    
    return updatedMembership.org_member_role
  }

  console.log('User role data:', {
    orgId: currentOrgMembership.org_id,
    memberRole: currentOrgMembership.org_member_role,
    roleDetails: currentOrgMembership.roles
  })
  
  return currentOrgMembership.org_member_role
}

export async function hasRequiredRole(requiredRole: number) {
  const userRole = await getUserRole()
  
  console.log('Checking role access:', { userRole, requiredRole })
  
  if (!userRole) {
    console.log('Access denied: No user role found')
    return false
  }
  
  const hasAccess = userRole <= requiredRole
  console.log('Access check result:', { hasAccess, userRole, requiredRole })
  
  return hasAccess
}

export async function getUserRoleServer() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return null

  // Get the active organization context from database
  const { data: currentOrg, error: contextError } = await supabase
    .from('organization_members')
    .select(`
      org_id,
      org_member_role,
      roles (
        role_id,
        role_name
      )
    `)
    .eq('user_id', user.id)
    .eq('is_current_context', true)
    .eq('org_member_is_active', true)
    .single()

  if (contextError || !currentOrg) {
    console.log('No active organization context found in server check')
    
    // Try to initialize context
    const { data: orgId, error: initError } = await supabase.rpc('initialize_user_organization_context', {
      p_user_id: user.id
    })
    
    if (initError || !orgId) {
      console.error('Failed to initialize organization context in server check:', initError)
      return null
    }
    
    // Re-fetch with the newly set context
    const { data: updatedContext, error: retryError } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        org_member_role,
        roles (
          role_id,
          role_name
        )
      `)
      .eq('user_id', user.id)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)
      .single()
      
    if (retryError || !updatedContext) {
      console.error('Still no active organization after initialization in server check:', retryError)
      return null
    }
    
    console.log('Server role check after context initialization:', {
      userId: user.id,
      orgId: updatedContext.org_id,
      role: updatedContext.org_member_role,
      roleDetails: updatedContext.roles
    })
    
    return updatedContext.org_member_role
  }

  console.log('Server role check:', {
    userId: user.id,
    orgId: currentOrg.org_id,
    role: currentOrg.org_member_role,
    roleDetails: currentOrg.roles
  })

  return currentOrg.org_member_role
} 