import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { OrganizationCheckProvider } from "@/providers/organization-check-provider";
import { GlobalDashboardProvider } from "@/components/providers/global-dashboard-provider";
import { SWRProvider } from "@/components/providers/swr-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Next.js Supabase SSR",
  description: "Next.js application with Supabase SSR authentication",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SWRProvider>
          <GlobalDashboardProvider>
            <OrganizationCheckProvider>
              {children}
              <Toaster />
            </OrganizationCheckProvider>
          </GlobalDashboardProvider>
        </SWRProvider>
      </body>
    </html>
  );
}
