import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { canDeleteMember } from '@/lib/permission-utils'
import type { OrganizationMemberBasic } from '@/types/organization/OrganizationMember'

export async function DELETE(request: Request) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the request body
    const body = await request.json()
    const { orgId, userId } = body

    if (!orgId || !userId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Verify user has permission to remove members
    const { data: userRole, error: userRoleError } = await supabase
      .from('organization_members')
      .select('org_member_role')
      .eq('org_id', orgId)
      .eq('user_id', user.id)
      .eq('org_member_is_active', true)
      .single()

    if (userRoleError || !userRole) {
      console.error('Error checking permissions:', userRoleError)
      return NextResponse.json({ error: 'You do not have permission to remove members from this organization' }, { status: 403 })
    }

    // Verify target member exists and check active status
    const { data: targetMember, error: targetMemberError } = await supabase
      .from('organization_members')
      .select('org_member_role, org_member_is_active')
      .eq('org_id', orgId)
      .eq('user_id', userId)
      .single()

    if (targetMemberError || !targetMember) {
      console.error('Error finding target member:', targetMemberError)
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    // Cannot remove active members
    if (targetMember.org_member_is_active) {
      return NextResponse.json({ 
        error: 'Cannot remove an active member. Please deactivate the member first.' 
      }, { status: 400 })
    }

    // Users cannot remove themselves
    if (userId === user.id) {
      return NextResponse.json({ error: 'You cannot remove yourself from the organization' }, { status: 403 })
    }

    // Build the member object for permission check
    const memberData: OrganizationMemberBasic = {
      user_id: userId,
      org_id: orgId,
      org_member_role: targetMember.org_member_role,
      org_member_is_active: targetMember.org_member_is_active
    };

    // Check permission using our domain-specific utility
    const hasPermission = canDeleteMember(
      memberData,
      userRole.org_member_role,
      user.id
    );

    if (!hasPermission) {
      return NextResponse.json({ 
        error: 'Insufficient permissions to remove this member' 
      }, { status: 403 })
    }

    // Delete the member record
    const { data, error: deleteError } = await supabase
      .from('organization_members')
      .delete()
      .eq('org_id', orgId)
      .eq('user_id', userId)
      .select()

    if (deleteError) {
      console.error('Error removing member:', deleteError)
      return NextResponse.json({ error: 'Failed to remove member' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Member removed successfully',
      data
    })
  } catch (error) {
    console.error('Unexpected error in remove member endpoint:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 