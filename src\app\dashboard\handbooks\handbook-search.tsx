"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { HandbookSearchProps } from "@/types/app/dashboard/handbooks/HandbookSearchProps";

export function HandbookSearch({ handbooks, onSelect }: HandbookSearchProps) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState("");

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between text-left"
        >
          <span className="line-clamp-2">
            {value
              ? handbooks.find(
                  (handbook) => handbook.service.service_case_id === value
                )?.service.service_name
              : "Select a handbook..."}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
        <Command>
          <CommandInput placeholder="Search handbook..." />
          <CommandList>
            <CommandEmpty>No handbook found.</CommandEmpty>
            <CommandGroup>
              {handbooks.map((handbook) => (
                <CommandItem
                  key={handbook.service.service_case_id}
                  onSelect={() => {
                    setValue("");
                    if (handbook.service.service_case_id) {
                      onSelect(handbook.service.service_case_id);
                    }
                    setOpen(false);
                  }}
                  className="flex flex-col items-start gap-1 py-3"
                >
                  <div className="flex items-center gap-2">
                    <Check
                      className={cn(
                        "h-4 w-4",
                        value === handbook.service.service_case_id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    <span className="font-medium">
                      {handbook.service.service_case_id}
                    </span>
                  </div>
                  <span className="ml-6 line-clamp-2">
                    {handbook.service.service_name}
                  </span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
