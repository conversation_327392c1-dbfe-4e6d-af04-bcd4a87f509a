/**
 * Legacy event conversion hook - NO LONGER NEEDED
 *
 * This hook previously converted legacy events to AUTH_CONTEXT_CHANGED events.
 * Now that all interpreters emit AUTH_CONTEXT_CHANGED directly, this hook is obsolete.
 *
 * The interpreters now handle the conversion internally:
 * - role.ts emits AUTH_CONTEXT_CHANGED for role changes
 * - status.ts emits AUTH_CONTEXT_CHANGED for status changes
 * - context.ts emits AUTH_CONTEXT_CHANGED for context changes
 * - organizationStatusInterpreter.ts emits AUTH_CONTEXT_CHANGED for org status changes
 * - name.ts emits AUTH_CONTEXT_CHANGED for name changes (if needed)
 */

// This hook is now a no-op and can be removed from components
export function useAuthEventEmitters() {
  // No longer needed - interpreters emit AUTH_CONTEXT_CHANGED directly
}
