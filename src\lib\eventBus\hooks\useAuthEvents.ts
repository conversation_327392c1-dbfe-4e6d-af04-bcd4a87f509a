import { useEffect } from 'react';
import { emitter } from '../emitter';
import { AUTH_CONTEXT_CHANGED } from '../constants';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import type { AuthEvent, UserRoleChangedEvent, MemberStatusChangedEvent, OrganizationActiveStatusChangedEvent, OrganizationContextEvent, OrgNameChangedEvent } from '@/lib/eventTypes';

/**
 * Hook to register emitters for auth-related events
 * This centralizes the emitting of auth events to ensure consistency
 */
export function useAuthEventEmitters() {
  // Get current user ID from the auth context store
  const { userId: currentUserId } = useAuthContextStore();

  useEffect(() => {
    // Handler for user role changes
    const handleUserRoleChanged = (event: UserRoleChangedEvent) => {
      const authEvent: AuthEvent = {
        userId: event.userId,
        orgId: event.orgId,
        reason: `Role changed to ${event.newRoleId}${event.oldRoleId ? ` from ${event.oldRoleId}` : ''}`,
        timestamp: event.timestamp || Date.now(),
      };

      console.log(`[EventBus] Emitting ${AUTH_CONTEXT_CHANGED} from user:role:changed`, authEvent);
      emitter.emit(AUTH_CONTEXT_CHANGED, authEvent);
    };

    // Handler for member status changes
    const handleMemberStatusChanged = (event: MemberStatusChangedEvent) => {
      const authEvent: AuthEvent = {
        userId: event.userId,
        orgId: event.orgId,
        reason: `Member status changed to ${event.isActive ? 'active' : 'inactive'}`,
        timestamp: event.timestamp || Date.now(),
      };

      console.log(`[EventBus] Emitting ${AUTH_CONTEXT_CHANGED} from member:status:changed`, authEvent);
      emitter.emit(AUTH_CONTEXT_CHANGED, authEvent);
    };

    // Handler for organization active status changes
    const handleOrgStatusChanged = (event: OrganizationActiveStatusChangedEvent) => {
      // Only process events for the current organization context
      const { orgId: currentOrgId } = useAuthContextStore.getState();
      if (event.orgId !== currentOrgId) {
        return; // Event is not for current organization context
      }

      // Use the current user ID from the auth context store
      const userId = currentUserId || 'unknown';

      const authEvent: AuthEvent = {
        userId,
        orgId: event.orgId,
        reason: `Organization status changed to ${event.isActive ? 'active' : 'inactive'}`,
        timestamp: event.timestamp || Date.now(),
      };

      console.log(`[EventBus] Emitting ${AUTH_CONTEXT_CHANGED} from organization:statusChanged`, authEvent);
      emitter.emit(AUTH_CONTEXT_CHANGED, authEvent);
    };

    // Handler for organization context changes
    const handleOrgContextChanged = (event: OrganizationContextEvent) => {
      // Check if this tab initiated the current organization switch
      const currentSwitchId = sessionStorage.getItem('currentOrgSwitchId');
      if (currentSwitchId) {
        console.log('[EventBus] Ignoring organization context change - this tab initiated the switch');
        return;
      }

      const authEvent: AuthEvent = {
        userId: event.userId,
        orgId: event.orgId,
        reason: 'Organization context changed',
        timestamp: event.timestamp || Date.now(),
      };

      console.log(`[EventBus] Emitting ${AUTH_CONTEXT_CHANGED} from organization:context:changed`, authEvent);
      emitter.emit(AUTH_CONTEXT_CHANGED, authEvent);
    };

    // Handler for organization name changes
    const handleOrgNameChanged = (event: OrgNameChangedEvent) => {
      // Only process events for the current organization context
      const { orgId: currentOrgId } = useAuthContextStore.getState();
      if (event.orgId !== currentOrgId) {
        return; // Event is not for current organization context
      }

      // Use the current user ID from the auth context store
      const userId = currentUserId || 'unknown';

      const authEvent: AuthEvent = {
        userId,
        orgId: event.orgId,
        reason: `Organization name changed to "${event.newName}"`,
        timestamp: event.timestamp || Date.now(),
      };

      console.log(`[EventBus] Emitting ${AUTH_CONTEXT_CHANGED} from organization:name:changed`, authEvent);
      emitter.emit(AUTH_CONTEXT_CHANGED, authEvent);
    };

    // Register event listeners for legacy events
    emitter.on('user:role:changed', handleUserRoleChanged);
    emitter.on('member:status:changed', handleMemberStatusChanged);
    emitter.on('organization:statusChanged', handleOrgStatusChanged);
    emitter.on('organization:context:changed', handleOrgContextChanged);
    emitter.on('organization:name:changed', handleOrgNameChanged);

    // Return cleanup function
    return () => {
      emitter.off('user:role:changed', handleUserRoleChanged);
      emitter.off('member:status:changed', handleMemberStatusChanged);
      emitter.off('organization:statusChanged', handleOrgStatusChanged);
      emitter.off('organization:context:changed', handleOrgContextChanged);
      emitter.off('organization:name:changed', handleOrgNameChanged);
    };
  }, [currentUserId]); // Include currentUserId in dependency array
}
