import { AlertCircle } from "lucide-react";

export default function ErrorPage({
  searchParams,
}: {
  searchParams: { message: string };
}) {
  const errorMessage =
    searchParams.message ||
    "We're sorry, your account does not have permission to access this page.";

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="mb-8">
        <AlertCircle className="h-16 w-16 text-[#194852]" />
      </div>

      <h1 className="text-4xl font-bold mb-4">
        You do not have access to this page
      </h1>

      <p className="text-gray-600 text-lg mb-8">{errorMessage}</p>

      <button
        onClick={() => (window.location.href = "/dashboard")}
        className="bg-black text-white px-8 py-4 text-lg font-medium rounded-lg cursor-pointer hover:bg-black/90"
      >
        Continue to Log In
      </button>
    </div>
  );
}
