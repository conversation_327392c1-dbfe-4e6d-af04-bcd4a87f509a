import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { evaluateRbac } from './rbac-utils'
// import { RoleId } from './roles' // Removed unused RoleId
import type { PermissionCheckOptions, RbacConditions } from '@/types/lib/rbac/index';
import { JSX } from 'react';

// Type definition for the data returned from Supabase
interface OrganizationData {
  is_active: boolean;
}

interface MembershipData {
  org_id: string;
  org_member_role: number;
  organizations: OrganizationData;
}

// Use unknown instead of any for better type safety
type ServerComponent<P = Record<string, unknown>> = (props: P) => Promise<JSX.Element>;

// Check permissions based on RBAC props
export async function checkRbacPermission(
  RbacConditions: RbacConditions,
  options: PermissionCheckOptions = {}
): Promise<boolean> {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    if (!options.silentFail) {
      redirect('/auth/login')
    }
    return false
  }

  const {
    orgId,
    orgContext = 'current', // Only accept 'current' | 'any' - reject any other values
    resourceOrgId,
    ...permissionProps
  } = RbacConditions

  // SECURITY: Validate orgContext to prevent bypass attacks
  if (orgContext !== 'current' && orgContext !== 'any') {
    console.error(`[SECURITY] Invalid orgContext value: ${orgContext}. Only 'current' and 'any' are allowed.`)
    if (!options.silentFail) {
      redirect('/dashboard')
    }
    return false
  }

  let targetOrgId = resourceOrgId || orgId

  if (!targetOrgId && orgContext === 'current') {
    const { data: currentOrg } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)
      .single()

    targetOrgId = currentOrg?.org_id

    if (!targetOrgId) {
      const { data: initializedOrgId } = await supabase.rpc('initialize_user_organization_context', {
        p_user_id: user.id
      })
      targetOrgId = initializedOrgId
    }
  }

  // This condition `orgContext === 'current'` along with `!targetOrgId` means
  // even after attempting to get/initialize context, it failed for a 'current' context requirement.
  if (!targetOrgId && orgContext === 'current') {
    if (!options.silentFail) {
      redirect('/dashboard') // Or perhaps a more specific error page
    }
    return false
  }

  const query = supabase
    .from('organization_members')
    .select('org_id, org_member_role, organizations(is_active)')
    .eq('user_id', user.id)

  // If orgContext is 'current', we must have a targetOrgId by now (or have returned false).
  // If orgContext is 'all', we don't filter by a specific orgId at this stage of the query.
  if (targetOrgId && orgContext === 'current') {
    query.eq('org_id', targetOrgId)
  }
  // If orgContext is 'all', the query proceeds to fetch all memberships for the user.

  const { data: memberships, error: membershipError } = await query

  if (membershipError || !memberships || memberships.length === 0) {
    // This means the user is not a member of the targetOrgId (if specified),
    // or has no organization memberships at all if orgContext was 'all' and query returned empty.
    if (!options.silentFail) {
      redirect('/dashboard') // Or a more specific "not a member" or "no orgs" page
    }
    return false
  }

  // If orgContext is 'any', iterate and check if any membership satisfies the conditions.
  if (orgContext === 'any') { // This check should now be valid
    return memberships.some(membership => {
      const member = membership as unknown as MembershipData;
      if (member.organizations && member.organizations.is_active === false) {
        const isSuperAdminEval = evaluateRbac(member.org_member_role, { rRoles: ["superAdmin"] });
        if (isSuperAdminEval) {
          return evaluateRbac(member.org_member_role, permissionProps)
        }
        return false // Not superAdmin, and org is inactive
      }
      // Org is active or no organization field (should not happen with inner join, but defensive)
      return evaluateRbac(member.org_member_role, permissionProps)
    })
  }

  // If orgContext was 'current', memberships should contain exactly one item if access is possible.
  // If it somehow contained more (shouldn't with .single() logic or specific orgId filter),
  // we proceed with the first one as per original logic.
  const membership = memberships[0] as unknown as MembershipData;
  const isSuperAdmin = evaluateRbac(membership.org_member_role, { rRoles: ["superAdmin"] });

  console.log('checkRbacPermission: Role evaluation for orgContext=current', {
    userRoleId: membership.org_member_role,
    isSuperAdmin,
    isOrgActive: membership.organizations?.is_active !== false,
    targetOrgId: targetOrgId // Log the org being checked
  });

  if (membership.organizations && membership.organizations.is_active === false) {
    if (isSuperAdmin) {
      return evaluateRbac(membership.org_member_role, permissionProps);
    }
    if (!options.silentFail) {
      redirect('/dashboard/organization-disabled')
    }
    return false
  }

  const hasAccess = evaluateRbac(membership.org_member_role, permissionProps)

  if (!hasAccess && !options.silentFail && options.redirectTo) {
    redirect(options.redirectTo)
  }
  return hasAccess
}

export function withRbacPermission(
  RbacConditions: RbacConditions,
  options: PermissionCheckOptions = {}
) {
  // Use unknown instead of any for better type safety
  return function <P = Record<string, unknown>>(
    WrappedComponent: ServerComponent<P>
  ): ServerComponent<P> {
    const ComponentWithRbacCheck: ServerComponent<P> = async (props) => {
      const hasAccess = await checkRbacPermission(RbacConditions, { silentFail: true });

      if (!hasAccess) {
        if (options.redirectTo) {
          redirect(options.redirectTo);
        }
        // Fallback redirect if options.redirectTo is not hit or not provided
        redirect('/dashboard');
      }

      return WrappedComponent(props);
    };

    // More type-safe access to displayName and name
    const wrappedComp = WrappedComponent as { displayName?: string; name?: string };
    const componentName = wrappedComp.displayName || wrappedComp.name || 'Component';

    // Also type ComponentWithRbacCheck for assigning displayName
    (ComponentWithRbacCheck as { displayName?: string }).displayName = `withRbacPermission(${componentName})`;

    return ComponentWithRbacCheck;
  };
}