import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { handleAuthenticationError } from '@/lib/auth-error-handler';

export async function refreshAuthContext(/* GONE: options?: RefreshAuthContextOptions */) {
  try {
    const res = await fetch('/api/auth/refresh-context', {
      method: 'POST',
      cache: 'no-store'
    });

    if (!res.ok) {
      const errorText = await res.text();
      const error = new Error(`Failed to refresh auth context: ${res.status} ${errorText}`);
      handleAuthenticationError(error, res.status);
      return null; // This line won't be reached if it's a 401, but satisfies TypeScript
    }

    const { context } = await res.json();

    if (process.env.NODE_ENV === 'development') {
      console.debug('[refreshAuthContext] Refreshed context:', context);
    }

    // Update the store with the refreshed context
    useAuthContextStore.getState().updateFullContext(context);

    return context;
  } catch (error) {
    console.error('[refreshAuthContext] Error refreshing auth context:', error);
    handleAuthenticationError(error as Error);
    return null; // This line won't be reached if it's a 401, but satisfies TypeScript
  }
}
