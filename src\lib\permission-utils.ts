import { evaluateRbac, roleKeyToId } from "@/lib/rbac/rbac-utils";
import type { OrganizationMemberBasic } from "@/types/organization/OrganizationMember";

/**
 * Utility functions for checking domain-specific permissions that extend
 * the base RBAC system with business-specific rules. These handle relationship-based
 * rules like preventing users from modifying their own roles or roles higher than theirs.
 */

/**
 * Check if a user can toggle another user's status based on roles.
 * @param member The target member whose status is being changed.
 * @param actingUserRoleId The role ID of the user performing the action *in the target member's org*.
 * @param actingUserId The ID of the user performing the action.
 * @returns boolean True if the action is permitted.
 */
export const canToggleMemberStatus = (
  member: OrganizationMemberBasic,
  actingUserRoleId: number | undefined,
  actingUserId: string | undefined
): boolean => {
  if (!actingUserRoleId || !actingUserId) return false;

  // Users cannot deactivate themselves using this UI action
  if (member.user_id === actingUserId) return false;

  // Superadmin can toggle anyone
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["superAdmin"] })) return true;

  // Support admin can toggle anyone *except* Superadmins
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["supportAdmin"] })) {
    return !evaluateRbac(member.org_member_role, { rRoles: ["superAdmin"] });
  }

  // Org admin can toggle anyone in their org *except* Superadmins and Support Admins
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["orgAdmin"] })) {
    return !evaluateRbac(member.org_member_role, { rRoles: ["superAdmin", "supportAdmin"] });
  }

  // Org members/accounting can only toggle clients within their org
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["orgMember", "orgAccounting"] })) {
    return evaluateRbac(member.org_member_role, { rRoles: ["orgClient"] });
  }

  // Clients cannot toggle anyone
  return false;
};

/**
 * Check if a user can change another user's role.
 * @param member The target member whose role is being changed.
 * @param actingUserRoleId The role ID of the user performing the action *in the target member's org*.
 * @param actingUserId The ID of the user performing the action.
 * @param newRoleId The new role ID to be assigned.
 * @returns boolean True if the action is permitted.
 */
export const canChangeUserRole = (
  member: OrganizationMemberBasic,
  actingUserRoleId: number | undefined,
  actingUserId: string | undefined,
  newRoleId?: number
): boolean => {
  if (!actingUserRoleId || !actingUserId) return false;

  // Users cannot change their own role using this UI action
  if (member.user_id === actingUserId) return false;

  // Superadmin can change anyone's role
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["superAdmin"] })) return true;

  // Support admin can change anyone's role *except* Superadmins
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["supportAdmin"] })) {
    return !evaluateRbac(member.org_member_role, { rRoles: ["superAdmin"] });
  }

  // Org admin can change roles for users at Org Admin level or below,
  // *except* Superadmins and Support Admins.
  if (evaluateRbac(actingUserRoleId, { ruRoles: ["orgAdmin"] })) {
    // Fix: OrgAdmins cannot change roles of superadmins or supportadmins
    if (evaluateRbac(member.org_member_role, { rRoles: ["superAdmin", "supportAdmin"] })) {
      return false;
    }

    // If newRoleId is provided, check if the new role level is allowed
    if (newRoleId !== undefined) {
      // Org Admin cannot assign roles higher than their own (superAdmin=1, supportAdmin=2, orgAdmin=3)
      // They can only assign orgAdmin (3) or lower privilege roles (4, 5, 6)
      return newRoleId >= 3; // orgAdmin=3, orgMember=4, orgAccounting=5, orgClient=6
    }

    return true;
  }

  // Lower roles cannot change roles
  return false;
};

/**
 * Check if a user can delete (remove) another member from an organization.
 * @param member The target member to be removed.
 * @param actingUserRoleId The role ID of the user performing the action *in the target member's org*.
 * @param actingUserId The ID of the user performing the action.
 * @returns boolean True if the action is permitted.
 */
export const canDeleteMember = (
  member: OrganizationMemberBasic,
  actingUserRoleId: number | undefined,
  actingUserId: string | undefined
): boolean => {
  if (!actingUserRoleId) return false;

  // Users cannot remove themselves
  if (member.user_id === actingUserId) return false;

  // Rule: Can only remove *inactive* members via this UI action.
  if (member.org_member_is_active) {
    return false;
  }

  // Superadmin can remove any inactive member.
  if (evaluateRbac(actingUserRoleId, { rdRoles: ["superAdmin"] })) return true;

  // Support Admin can remove inactive members *except* Superadmins.
  if (evaluateRbac(actingUserRoleId, { rdRoles: ["supportAdmin"] })) {
    return !evaluateRbac(member.org_member_role, { rRoles: ["superAdmin"] });
  }

  // Org Admin can remove inactive members at Org Admin level or below,
  // *except* Superadmins and Support Admins.
  if (evaluateRbac(actingUserRoleId, { rdRoles: ["orgAdmin"] })) {
    return !evaluateRbac(member.org_member_role, { rRoles: ["superAdmin", "supportAdmin"] });
  }

  // Lower roles cannot remove members.
  return false;
};

/**
 * Determines if a user can edit a user profile based on permissions and organization context.
 *
 * Rules:
 * 1. Users can always edit their own profile
 * 2. A user with a higher role can edit profiles of users with lower roles in the same organization
 * 3. A user can't edit profiles of users with equal or higher roles
 * 4. Superadmins can edit any user's profile regardless of organization
 *
 * @param targetMember The organization member data of the profile being edited
 * @param userRole The role ID of the current user
 * @param userId The ID of the current user
 * @param isSameOrg Whether the users are in the same organization
 * @returns Whether the current user can edit the target user's profile
 */
export function canEditUserProfile(
  targetMember: OrganizationMemberBasic,
  userRole: number,
  userId: string,
  isSameOrg: boolean
): boolean {
  // Users can always edit their own profile
  if (targetMember.user_id === userId) {
    return true;
  }

  // Superadmins (role_id = 1) can edit any user regardless of organization
  if (evaluateRbac(userRole, { rRoles: ["superAdmin"] })) {
    return true;
  }

  // For users in different organizations, only superadmins can edit
  if (!isSameOrg) {
    return false;
  }

  // Users with higher roles can edit users with lower roles in the same organization
  return userRole < targetMember.org_member_role;
}

/**
 * Check if a user can modify another user based on privilege hierarchy.
 * This enforces the rule that users can only modify accounts with lower privilege levels.
 * @param actingUserRoleId The role ID of the user performing the action
 * @param targetUserRoleId The role ID of the target user being modified
 * @returns Object with allowed boolean and reason string
 */
export const canModifyUser = (
  actingUserRoleId: number | undefined,
  targetUserRoleId: number
): { allowed: boolean; reason?: string } => {
  if (!actingUserRoleId) {
    return { allowed: false, reason: "Acting user role is undefined" };
  }

  const isTargetSuperAdmin = evaluateRbac(targetUserRoleId, { rRoles: ["superAdmin"] });
  const isTargetSupportAdmin = evaluateRbac(targetUserRoleId, { rRoles: ["supportAdmin"] });
  const isActingSuperAdmin = evaluateRbac(actingUserRoleId, { rRoles: ["superAdmin"] });

  // SuperAdmins can modify anyone
  if (isActingSuperAdmin) {
    return { allowed: true };
  }

  // Non-SuperAdmins cannot modify SuperAdmins
  if (isTargetSuperAdmin) {
    return { allowed: false, reason: "Insufficient permissions to modify SuperAdmin accounts." };
  }

  // Non-SuperAdmins cannot modify SupportAdmins
  if (isTargetSupportAdmin) {
    return { allowed: false, reason: "Insufficient permissions to modify SupportAdmin accounts." };
  }

  // All other modifications are allowed (subject to domain-specific rules)
  return { allowed: true };
};

/**
 * Get roles that can be assigned by a user based on their role and business rules.
 * @param actingUserRoleId The role ID of the user performing the assignment
 * @param availableRoles All available roles in the system
 * @param maxAssignableRole Optional maximum role that can be assigned (page-level restriction)
 * @returns Array of roles that can be assigned
 */
export const getAssignableRoles = (
  actingUserRoleId: number | undefined,
  availableRoles: Array<{ role_id: number; role_name: string }>,
  maxAssignableRole?: string
): Array<{ role_id: number; role_name: string }> => {
  if (!actingUserRoleId) return [];

  return availableRoles.filter(role => {
    // First, apply page-level restriction if maxAssignableRole is set
    if (maxAssignableRole) {
      const maxRoleId = roleKeyToId[maxAssignableRole as keyof typeof roleKeyToId];
      if (maxRoleId && role.role_id < maxRoleId) {
        return false; // Role is higher privilege than allowed maximum
      }
    }

    // Then apply user-level restrictions
    // SuperAdmin can assign any role (subject to page restrictions above)
    if (evaluateRbac(actingUserRoleId, { rRoles: ["superAdmin"] })) return true;

    // SupportAdmin can assign any role except SuperAdmin (subject to page restrictions above)
    if (evaluateRbac(actingUserRoleId, { rRoles: ["supportAdmin"] })) {
      return !evaluateRbac(role.role_id, { rRoles: ["superAdmin"] });
    }

    // OrgAdmin can only assign roles at orgAdmin level or below (orgAdmin, orgMember, orgAccounting, orgClient)
    // This means they CANNOT assign superAdmin or supportAdmin roles
    if (evaluateRbac(actingUserRoleId, { rRoles: ["orgAdmin"] })) {
      // Check if the role is orgAdmin or lower (higher role_id number = lower privilege)
      return role.role_id >= 3; // orgAdmin=3, orgMember=4, orgAccounting=5, orgClient=6
    }

    return false;
  });
};

/**
 * Business logic for determining access to disabled pages and recovery scenarios.
 * These functions implement the organization/user status transition rules.
 */

export interface DisabledPageAccessResult {
  canAccess: boolean;
  redirectTo: string;
  priority: 'account-disabled' | 'organization-disabled' | 'route-permission' | 'allowed';
  reason: string;
}

/**
 * Evaluate access to account-disabled page and determine appropriate action.
 * Business rules:
 * - User inactive → stay on account-disabled page
 * - User active + org active → redirect to dashboard
 * - User active + org inactive + super admin → redirect to dashboard
 * - User active + org inactive + not super admin → redirect to organization-disabled
 */
export function evaluateAccountDisabledPageAccess(
  userRoleId: number, // eslint-disable-line @typescript-eslint/no-unused-vars
  isUserActiveInOrg: boolean,
  isOrgActive: boolean,
  isSuperAdmin: boolean
): DisabledPageAccessResult {
  if (!isUserActiveInOrg) {
    // User is still inactive - stay on account-disabled page
    return {
      canAccess: true,
      redirectTo: '/dashboard/account-disabled',
      priority: 'account-disabled',
      reason: 'User is inactive in organization and on correct disabled page'
    };
  } else if (isOrgActive) {
    // User is now active and org is active - redirect to dashboard
    return {
      canAccess: false,
      redirectTo: '/dashboard',
      priority: 'route-permission',
      reason: 'User account is now active - redirecting away from account-disabled page'
    };
  } else if (!isOrgActive && isSuperAdmin) {
    // User is active, org is inactive, but user is super admin - redirect to dashboard
    return {
      canAccess: false,
      redirectTo: '/dashboard',
      priority: 'route-permission',
      reason: 'User is super admin and can access inactive organizations - redirecting to dashboard'
    };
  } else {
    // User is active but org is inactive and user is not super admin - redirect to org-disabled
    return {
      canAccess: false,
      redirectTo: '/dashboard/organization-disabled',
      priority: 'organization-disabled',
      reason: 'User is active but organization is inactive - redirecting to organization-disabled page'
    };
  }
}

/**
 * Evaluate access to organization-disabled page and determine appropriate action.
 * Business rules:
 * - User inactive → redirect to account-disabled (takes precedence)
 * - User active + org active → redirect to dashboard
 * - User active + org inactive + super admin → redirect to dashboard
 * - User active + org inactive + not super admin → stay on organization-disabled
 */
export function evaluateOrganizationDisabledPageAccess(
  userRoleId: number, // eslint-disable-line @typescript-eslint/no-unused-vars
  isUserActiveInOrg: boolean,
  isOrgActive: boolean,
  isSuperAdmin: boolean
): DisabledPageAccessResult {
  if (!isUserActiveInOrg) {
    // User is inactive - redirect to account-disabled (takes precedence)
    return {
      canAccess: false,
      redirectTo: '/dashboard/account-disabled',
      priority: 'account-disabled',
      reason: 'User is inactive in organization - redirecting to account-disabled page'
    };
  } else if (isOrgActive) {
    // User is active and org is now active - redirect to dashboard
    return {
      canAccess: false,
      redirectTo: '/dashboard',
      priority: 'route-permission',
      reason: 'Organization is now active - redirecting away from organization-disabled page'
    };
  } else if (!isOrgActive && isSuperAdmin) {
    // User is active, org is still inactive, but user is super admin - redirect to dashboard
    return {
      canAccess: false,
      redirectTo: '/dashboard',
      priority: 'route-permission',
      reason: 'User is super admin and can access inactive organizations - redirecting to dashboard'
    };
  } else {
    // User is active but org is still inactive and user is not super admin - stay on org-disabled
    return {
      canAccess: true,
      redirectTo: '/dashboard/organization-disabled',
      priority: 'organization-disabled',
      reason: 'Organization is inactive and user is not super admin - staying on organization-disabled page'
    };
  }
}