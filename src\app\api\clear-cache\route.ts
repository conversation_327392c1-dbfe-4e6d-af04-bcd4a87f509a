import { NextResponse } from 'next/server'
import { revalidatePath } from 'next/cache'

export async function POST() {
  try {
    // Revalidate the main layout paths to ensure fresh data
    revalidatePath('/', 'layout')
    revalidatePath('/dashboard', 'layout')
    
    // Log the cache clearing
    console.log('[clear-cache] Successfully cleared cache for main paths')
    
    // Return success response
    return NextResponse.json(
      { message: 'Cache cleared successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error('[clear-cache] Error clearing cache:', error)
    return NextResponse.json(
      { message: 'Error clearing cache', error: String(error) },
      { status: 500 }
    )
  }
}

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic' 