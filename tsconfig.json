{
  // tsconfig.json
  "compilerOptions": {
    /* ■ Runtime / emit behaviour
       ───────────────────────── */
    "target": "ES2020",                // ES2017 → ES2020 (still safe for all evergreen browsers)
    "module": "esnext",
    "lib": ["dom", "dom.iterable", "esnext"],

    /* ■ Type-checking strictness
       ─────────────────────────── */
    "strict": true,
    "noUnusedLocals": true,            // NEW  ➜ surfaces dead code immediately
    "noUnusedParameters": true,        // NEW  ➜ helps spot abandoned props in React components
    "exactOptionalPropertyTypes": true,// NEW  ➜ prevents “undefined” lurking in optionals

    /* ■ Module/bundler interop
       ───────────────────────── */
    "moduleResolution": "bundler",     // ✔ required for Turbopack
    "moduleDetection": "force",        // NEW  ➜ forbids accidental script files
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "paths": { "@/*": ["./src/*"] },

    /* ■ JS & JSX
       ─────────── */
    "allowJs": true,                   // keep until legacy JS fully migrated
    "isolatedModules": true,
    "jsx": "preserve",

    /* ■ DX
       ───── */
    "incremental": true,               // speed up tsc --watch
    "skipLibCheck": true,              // avoid noise from node_modules
    "plugins": [{ "name": "next" }],

    /* ■ Emit
       ─────── */
    "noEmit": true                     // handled by Next/Turbopack
  },

  /* Project globs
     ───────────── */
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}
