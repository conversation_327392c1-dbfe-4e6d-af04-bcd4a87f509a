"use client";

import { useEffect, useState, useCallback } from "react";
import { usePathname, useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { useOrganizationContextEvents } from "@/lib/eventBus";

// Paths that don't require organization check
const EXCLUDED_PATHS = [
  "/auth",
  "/login",
  "/signup",
  "/dashboard/admin/organization/create-organization",
  "/dashboard/admin/organization/create-organization-simple",
  "/dashboard/admin/organization/create-organization-test",
  "/api",
  "/",
  "/about",
  "/contact",
  "/pricing",
  "/terms",
  "/privacy",
];

export function OrganizationCheckProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const pathname = usePathname();
  const router = useRouter();
  const supabase = createClient();

  // Check if the current path is excluded from organization check
  const isExcludedPath = EXCLUDED_PATHS.some((path) =>
    pathname?.startsWith(path)
  );

  // Check if the current path is a create-organization route
  const isCreateOrgRoute = pathname?.includes("/create-organization");

  const checkUserOrganizations = useCallback(async () => {
    try {
      // Check if user is authenticated
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      // If not authenticated and not on an excluded path, redirect to login
      if (sessionError || !session) {
        if (!isExcludedPath && !isCreateOrgRoute) {
          router.push("/auth/login");
        }
        return;
      }

      // Store user ID for event bus subscription
      setUserId(session.user.id);

      // Skip check for excluded paths and create-organization routes
      if (isExcludedPath || isCreateOrgRoute) {
        return;
      }

      // Call the user_has_organizations function with auto-initialization
      const { data, error } = await supabase.rpc(
        "current_user_has_organizations"
      );

      if (error) {
        console.error("Error checking user organizations:", error);
        return;
      }

      // If user doesn't belong to any organization, redirect to create organization
      if (!data) {
        router.push("/dashboard/admin/organization/create-organization");
        return;
      }
    } catch (error) {
      console.error("Error in organization check:", error);
    } finally {
      // Only set initial loading to false after the first check
      setIsInitialLoading(false);
    }
  }, [supabase, isExcludedPath, isCreateOrgRoute, router]);

  // Handle organization context change events
  const handleOrgContextChange = useCallback(() => {
    // When organization context changes, revalidate
    checkUserOrganizations();
  }, [checkUserOrganizations]);

  // Use the event bus to respond to organization context changes
  useOrganizationContextEvents(userId, handleOrgContextChange);

  useEffect(() => {
    checkUserOrganizations();
  }, [pathname, checkUserOrganizations]);

  // Show loading UI only during initial organization check
  if (isInitialLoading) {
    return (
      <div className="min-h-screen bg-[#F9FAFB] flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#194852]"></div>
          <p className="text-sm text-gray-600">Checking organization access...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
