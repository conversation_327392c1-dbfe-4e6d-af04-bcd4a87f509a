
# Database-Driven Organization Context Implementation Plan


## Current Implementation Analysis

Your implementation has several key components:

1. **Cookie-Based Organization Selection**:
   - `switch-organization.ts` server action sets a cookie
   - API endpoint `/api/organizations/switch` is called from the UI

2. **Fallback Logic**:
   - `organization-utils-server.ts` has robust fallback logic:
     - First tries URL parameter
     - Then cookie
     - Then default organization
     - Then any organization the user belongs to
     - Log out user if no membership is available

3. **Client-Side Storage**:
   - `use-organization-storage.ts` synchronizes localStorage with server state
   - Handles default selection when no organization is active

4. **Key Issue**:
   - When an invalid organization is detected, the system identifies a valid fallback but doesn't update the cookie
   - This leads to repeated fallback lookups and navigation issues

## Detailed Implementation Plan

### Phase 1: Database Schema Update (Est. 1 day)

1. **Create Migration Script**
   ```sql
   -- Add is_current_context column to organization_members
   ALTER TABLE organization_members 
   ADD COLUMN is_current_context BOOLEAN DEFAULT false;

   -- Add constraint to ensure only one selected org per user
   CREATE UNIQUE INDEX idx_one_active_context_per_user 
   ON organization_members (user_id, is_current_context)
   WHERE is_current_context = true;
   ```

2. **Initialize Data**
   ```sql
   -- Create temporary function to ensure data consistency
   CREATE OR REPLACE FUNCTION initialize_current_context()
   RETURNS void AS $$
   DECLARE
       r RECORD;
   BEGIN
       -- For each user, find their first active organization (preferring default)
       FOR r IN 
           SELECT DISTINCT ON (user_id) 
               user_id, 
               org_id
           FROM organization_members
           WHERE org_member_is_active = true
           ORDER BY user_id, is_default_org DESC, created_at ASC
       LOOP
           -- Set that as their current context
           UPDATE organization_members
           SET is_current_context = true
           WHERE user_id = r.user_id AND org_id = r.org_id;
       END LOOP;
   END;
   $$ LANGUAGE plpgsql;

   -- Execute function
   SELECT initialize_current_context();

   -- Drop function after use
   DROP FUNCTION initialize_current_context();
   ```

### Phase 2: Create Organization Context API (Est. 1-2 days)

1. **Create `src/lib/organization-context.ts`**

```typescript
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import { Organization } from '@/types/organization'
import { revalidatePath } from 'next/cache'

/**
 * Sets the active organization for a user in the database
 */
export async function setActiveOrganization(userId: string, orgId: string): Promise<boolean> {
  if (!userId || !orgId) {
    console.error('Invalid parameters for setActiveOrganization')
    return false
  }

  try {
    const supabase = await createClient()
    
    // Verify user is a member of this organization
    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', userId)
      .eq('org_id', orgId)
      .eq('org_member_is_active', true)
      .single()
    
    if (membershipError || !membership) {
      console.error('User is not a member of this organization:', {
        userId,
        orgId,
        error: membershipError
      })
      return false
    }

    // Clear any existing active contexts
    const { error: clearError } = await supabase
      .from('organization_members')
      .update({ is_current_context: false })
      .eq('user_id', userId)
      .eq('is_current_context', true)
    
    if (clearError) {
      console.error('Error clearing existing active contexts:', {
        userId,
        error: clearError
      })
    }

    // Set new active context
    const { error: updateError } = await supabase
      .from('organization_members')
      .update({ is_current_context: true })
      .eq('user_id', userId)
      .eq('org_id', orgId)
    
    if (updateError) {
      console.error('Error updating active organization:', {
        userId,
        orgId,
        error: updateError
      })
      return false
    }

    // For backward compatibility, also update the cookie
    const cookieStore = await cookies()
    cookieStore.set({
      name: 'active_organization_id',
      value: orgId,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 30 // 30 days
    })

    console.log('Successfully set active organization:', {
      userId,
      orgId
    })
    
    return true
  } catch (error) {
    console.error('Error in setActiveOrganization:', error)
    return false
  }
}

/**
 * Gets the active organization for a user from the database
 */
export async function getActiveOrganization(userId: string): Promise<Organization | null> {
  if (!userId) {
    console.error('Invalid userId for getActiveOrganization')
    return null
  }
  
  try {
    const supabase = await createClient()
    
    // Get active organization from database
    const { data, error } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        org_member_role,
        is_default_org,
        organizations!inner (
          id,
          org_name,
          org_icon,
          created_at,
          updated_at,
          is_active
        ),
        roles!org_member_role (
          role_id,
          role_name
        )
      `)
      .eq('user_id', userId)
      .eq('is_current_context', true)
      .eq('org_member_is_active', true)
      .eq('organizations.is_active', true)
      .single()
    
    if (error) {
      console.error('Error fetching active organization:', {
        userId,
        error
      })
      return null
    }
    
    if (!data) {
      console.log('No active organization found for user:', userId)
      return null
    }
    
    return {
      id: data.organizations.id,
      name: data.organizations.org_name,
      org_icon: data.organizations.org_icon || '',
      role: data.roles.role_name,
      org_member_role: data.org_member_role,
      isActive: data.organizations.is_active,
      isDefault: data.is_default_org,
      createdAt: data.organizations.created_at,
      updatedAt: data.organizations.updated_at
    }
  } catch (error) {
    console.error('Error in getActiveOrganization:', error)
    return null
  }
}
```

2. **Update `src/app/actions/switch-organization.ts`**

```typescript
'use server'

import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'
import { setActiveOrganization } from '@/lib/organization-context'
import { createClient } from '@/lib/supabase/server'

export async function switchOrganization(orgId: string) {
  // Get current user
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    throw new Error('Not authenticated')
  }
  
  // Set active organization in database
  const success = await setActiveOrganization(user.id, orgId)
  
  if (!success) {
    throw new Error('Failed to switch organization')
  }
  
  // Revalidate all pages
  revalidatePath('/', 'layout')
  
  return success
}
```

3. **Create Organization Context API Endpoint**

```typescript
// src/app/api/organizations/switch/route.ts
import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { setActiveOrganization } from '@/lib/organization-context'

export async function POST(request: NextRequest) {
  try {
    const { organizationId } = await request.json()
    
    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }
    
    const success = await setActiveOrganization(user.id, organizationId)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to switch organization' },
        { status: 400 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error switching organization:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

### Phase 3: Update Organization Utils (Est. 1 day)

1. **Modify `src/lib/organization-utils-server.ts`**

```typescript
// Modify the getCurrentUserActiveOrganization function
export async function getCurrentUserActiveOrganization(orgIdParam?: string): Promise<{ organization: Organization | null; error: Error | null }> {
  console.log('Resolving active organization:', {
    context: orgIdParam ? 'URL parameter' : 'Default flow',
    orgIdParam: orgIdParam || 'not provided'
  })
  
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      console.error('Auth error in getCurrentUserActiveOrganization:', userError || 'No user found')
      return { organization: null, error: userError || new Error('No authenticated user found') }
    }
    
    // If orgIdParam is provided (from URL), verify access to that specific organization
    if (orgIdParam) {
      const organization = await getUserOrganizationById(user.id, orgIdParam)
      if (organization) {
        // If organization from URL is valid, update database to make it active
        await setActiveOrganization(user.id, orgIdParam)
        
        console.log('Access granted to requested organization:', {
          userId: user.id,
          orgId: orgIdParam,
          orgName: organization.name,
          role: organization.role
        })
        return { organization, error: null }
      }
      console.log('No access to requested organization:', {
        userId: user.id,
        orgId: orgIdParam
      })
    }

    // Get active organization from database
    const activeOrg = await getActiveOrganization(user.id)
    if (activeOrg) {
      console.log('Using active organization from database:', {
        userId: user.id,
        orgId: activeOrg.id,
        orgName: activeOrg.name,
        role: activeOrg.role
      })
      return { organization: activeOrg, error: null }
    }
    
    // Backward compatibility: Check cookie
    const cookieStore = await cookies()
    const activeOrgCookie = cookieStore.getAll().find(cookie => cookie.name === 'active_organization_id')
    
    if (activeOrgCookie?.value) {
      const organization = await getUserOrganizationById(user.id, activeOrgCookie.value)
      if (organization) {
        // Update database to match cookie
        await setActiveOrganization(user.id, activeOrgCookie.value)
        
        console.log('Using active organization from cookie:', {
          userId: user.id,
          orgId: activeOrgCookie.value,
          orgName: organization.name,
          role: organization.role
        })
        return { organization, error: null }
      }
      console.log('Cookie organization not accessible:', {
        userId: user.id,
        orgId: activeOrgCookie.value
      })
    }
    
    // Try default organization
    const defaultOrg = await getUserDefaultOrganization(user.id)
    if (defaultOrg) {
      // Update active organization in database
      await setActiveOrganization(user.id, defaultOrg.id)
      
      console.log('Using default organization:', {
        userId: user.id,
        orgId: defaultOrg.id,
        orgName: defaultOrg.name,
        role: defaultOrg.role
      })
      return { organization: defaultOrg, error: null }
    }
    
    // Last resort: any organization
    const organizations = await getUserOrganizations(user.id)
    if (organizations?.length) {
      const firstOrg = organizations[0]
      
      // Update active organization in database
      await setActiveOrganization(user.id, firstOrg.id)
      
      console.log('Using first available organization:', {
        userId: user.id,
        orgId: firstOrg.id,
        orgName: firstOrg.name,
        role: firstOrg.role,
        totalOrgs: organizations.length
      })
      return { organization: firstOrg, error: null }
    }
    
    console.log('No organization found for user:', {
      userId: user.id,
      context: orgIdParam ? 'URL parameter flow' : 'Default flow'
    })
    return { organization: null, error: null }
  } catch (error) {
    console.error('Error in getCurrentUserActiveOrganization:', {
      error,
      context: orgIdParam ? 'URL parameter flow' : 'Default flow'
    })
    return { organization: null, error: error as Error }
  }
}
```

2. **Update Middleware for Consistent Organization Context** (if using middleware)

```typescript
// Add this to middleware.ts if you have organization-specific middleware
// Import the required functions
import { getActiveOrganization } from '@/lib/organization-context'

// In your middleware function
// Ensure cookie matches database state
if (user) {
  const activeOrg = await getActiveOrganization(user.id)
  const response = NextResponse.next({ request })
  
  // Synchronize cookie with database state
  if (activeOrg) {
    response.cookies.set({
      name: 'active_organization_id',
      value: activeOrg.id,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 30 // 30 days
    })
  } else {
    // Clear the cookie if no active organization
    response.cookies.delete('active_organization_id')
  }
  
  return response
}
```

### Phase 4: Update Client Components (Est. 1 day)

1. **Update Organization Switcher**

The organization switcher component can remain largely unchanged, as it already makes an API call to switch organizations. The main change needed is ensuring it has proper error handling:

```typescript
// No major changes needed to organization-switcher.tsx
// The API endpoint will now update the database instead of just setting a cookie
```

2. **Update Organization Storage Hook**

```typescript
// src/hooks/use-organization-storage.ts
'use client'

import { useEffect } from 'react'
import { Organization } from '@/types/organization'

const STORAGE_KEY = 'active_organization'

export function useOrganizationStorage(
  activeOrganization: Organization | null,
  organizations: Organization[]
) {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    // Only switch to default organization if:
    // 1. No active organization is set
    // 2. We have organizations available
    if (!activeOrganization && organizations.length > 0) {
      // This will now use the database-driven approach via the API
      fetch('/api/organizations/switch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ organizationId: organizations[0].id }),
        credentials: 'include',
      }).catch(console.error)
    }
  }, [activeOrganization, organizations])

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return
    
    // Still keep localStorage for client-side performance
    if (activeOrganization) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify({
        id: activeOrganization.id,
        name: activeOrganization.name,
        role: activeOrganization.role,
        lastUpdated: new Date().toISOString()
      }))
    }
  }, [activeOrganization])
} 
```

### Phase 5: Testing & Validation (Est. 1 day)

1. **Create Test Cases**
   - Test normal organization switching
   - Test fallback when active organization is deleted
   - Test fallback when user is removed from active organization
   - Test URL parameter-based organization selection
   - Test behavior when cookie is missing/invalid

2. **Deployment Steps**
   - Run database migrations
   - Deploy code changes
   - Monitor logs for errors
   - Have fallback plan if issues arise

## Benefits of This Approach

1. **Resilience**: Organization selection persists across devices and browsers
2. **Consistency**: Single source of truth (database)
3. **Security**: Server-side validation of organization access
4. **Backward Compatibility**: Maintains cookie for existing code
5. **Improved UX**: Automatic recovery from invalid organization states

## Detailed Task Checklist

- **Database Updates**
  - [ ] Add `is_current_context` column to `organization_members`
  - [ ] Add unique constraint for one active context per user
  - [ ] Create data migration script to initialize values

- **Backend Development**
  - [ ] Create `organization-context.ts` utility library
  - [ ] Update `switch-organization.ts` server action
  - [ ] Create or update API endpoint for switching organizations
  - [ ] Modify `organization-utils-server.ts` to use database state

- **Middleware & Auth**
  - [ ] Update middleware to synchronize cookies with database state
  - [ ] Ensure all authorization checks use the database state

- **Frontend Development**
  - [ ] Update client-side components to handle new error scenarios
  - [ ] Update organization storage hook to work with database state

- **Testing & Deployment**
  - [ ] Create test cases to validate all scenarios
  - [ ] Test in development environment
  - [ ] Monitor deployment for issues

This phased approach maintains backward compatibility while providing a more robust solution to your organization context issues.
