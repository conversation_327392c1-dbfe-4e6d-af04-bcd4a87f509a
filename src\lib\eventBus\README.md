# Event Bus Architecture

## Overview

The event bus is a centralized event system for organization/user context and permissions. It uses the `mitt` library to provide a type-safe, small event emitter system.

## Key Components

### 1. Emitter (`emitter.ts`)

The **ONLY** mitt singleton instance for the entire application. All event consumers and producers MUST import the emitter from here (directly or via `@/lib/eventBus`).

```typescript
import { emitter } from '@/lib/eventBus'
```

### 2. Event Types (`eventTypes.ts`)

Contains all event type definitions, standardized with consistent fields.

### 3. Event Interpreters/Channels (`channels/`)

Modular interpreters that listen for raw database events (`db:*`) and emit higher-level, business-meaningful events:

- `role.ts` - Emits `user:role:changed` events
- `status.ts` - Emits `member:status:changed` events 
- `name.ts` - Emits `organization:name:changed` events
- `context.ts` - Emits `organization:context:changed` events
- `allMembers.ts` - Emits `organization:member:changed` events

### 4. React Hooks (`hooks/`)

Convenient React hooks for components to subscribe to events:

- `useOrganizationContextEvents`
- `useUserRoleEvents`
- `useMemberStatusEvents`
- `useOrgNameEvents`
- `useAllMemberChanges`

Also provides a generic `useBusEvent` hook for any event type.

### 5. Dashboard Event Manager (`dashboard-event-manager.tsx`)

The central orchestrator component that:
1. Sets up all Supabase realtime listeners
2. Emits raw `db:*` events when data changes
3. Registers all event interpreters
4. Handles context refreshes and navigation

## Event Flow

1. **Database Change** → Detected by `DashboardEventManager` Supabase listeners
2. **Raw Event** → `db:organization_members:*` or `db:organizations:*` events emitted
3. **Interpretation** → Channel interpreters emit high-level events (`user:role:changed`, etc.)
4. **UI Updates** → Components react to high-level events via hooks

## Using the Event Bus

### To listen for events:

```typescript
// In a React component
import { useBusEvent } from '@/lib/useBusEvent'

useBusEvent('user:role:changed', (event) => {
  // Handle role change event
})

// Or use specialized hooks
import { useUserRoleEvents } from '@/lib/eventBus'

useUserRoleEvents(userId, handleRoleChange)
```

### To emit events (typically done by interpreters/DashboardEventManager):

```typescript
import { emitter } from '@/lib/eventBus'

emitter.emit('client:authContextUpdated', { isUserActive: true })
```

## Migration Notice

**Important**: The event system has been refactored to centralize Supabase listeners in `DashboardEventManager` and make event interpreters pure event handlers without direct Supabase logic.

- **Legacy files `core.ts` and `deprecated-eventBus.ts`** are maintained for backward compatibility but should not be used in new code.
- All new code should import directly from `/emitter.ts` or the re-exports in `index.ts`.

## Debugging

In development mode:
- All events are logged to the console
- The emitter is attached to `window.__appEmitter`
- Use `window.__mittDebug('eventName')` to check for registered listeners

## Best Practices

1. **Only import emitter from the right place** - Always import from `@/lib/eventBus` or `@/lib/eventBus/emitter`.
2. **Clean up event listeners** - Always clean up event listeners when components unmount.
3. **Centralize Supabase listeners** - Never set up Supabase listeners outside of `DashboardEventManager`.
4. **Use the correct hooks** - Use the specialized hooks when possible for better type safety.
5. **Mount DashboardEventManager only once** - This component should only be mounted once at the app root. 